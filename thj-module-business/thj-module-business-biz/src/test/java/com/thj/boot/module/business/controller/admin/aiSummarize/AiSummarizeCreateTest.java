package com.thj.boot.module.business.controller.admin.aiSummarize;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.pojo.aisummarize.vo.AiSummarizeCreateReqVO;
import com.thj.boot.module.business.pojo.aisummarize.vo.AiSummarizeRespVO;
import com.thj.boot.module.business.service.aiSummarize.AiSummarizeService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AI透析小结创建接口测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AiSummarizeCreateTest {

    @Mock
    private AiSummarizeService aiSummarizeService;

    @InjectMocks
    private AiSummarizeController aiSummarizeController;

    @Test
    void testCreateAiSummarize_Success() {
        // 准备测试数据
        AiSummarizeCreateReqVO reqVO = new AiSummarizeCreateReqVO();
        reqVO.setPatientId(1L);
        reqVO.setPatientName("张三");
        reqVO.setDialysisPrescription("透析处方：血流量200ml/min，透析液流量500ml/min，透析时间4小时");
        reqVO.setMonitoringRecord("监测记录：血压130/80mmHg，心率80次/分，体重减少2kg");
        
        AiSummarizeRespVO mockResponse = new AiSummarizeRespVO();
        mockResponse.setPatientId(1L);
        mockResponse.setPatientName("张三");
        mockResponse.setSummarize1Result("透析过程顺利，患者生命体征平稳，透析效果良好。");
        
        // 模拟服务层调用
        when(aiSummarizeService.createAiSummarize(any(AiSummarizeCreateReqVO.class))).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiSummarizeRespVO> result = aiSummarizeController.createAiSummarize(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getPatientId());
        assertEquals("张三", result.getData().getPatientName());
        assertNotNull(result.getData().getSummarize1Result());
    }

    @Test
    void testCreateAiSummarize_ValidationError() {
        // 准备无效的测试数据（缺少必填字段patientId）
        AiSummarizeCreateReqVO reqVO = new AiSummarizeCreateReqVO();
        reqVO.setPatientName("张三");
        // 不设置patientId
        
        // 这个测试主要验证Controller能正常处理请求
        // 实际的验证会在Service层进行
        AiSummarizeRespVO mockResponse = new AiSummarizeRespVO();
        when(aiSummarizeService.createAiSummarize(any(AiSummarizeCreateReqVO.class))).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiSummarizeRespVO> result = aiSummarizeController.createAiSummarize(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
    }

}