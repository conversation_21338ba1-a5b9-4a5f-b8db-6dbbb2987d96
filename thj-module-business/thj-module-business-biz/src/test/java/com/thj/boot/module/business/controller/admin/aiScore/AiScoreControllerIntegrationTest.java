package com.thj.boot.module.business.controller.admin.aiScore;

import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreCreateReqVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreDetailsVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreExpertCommentReqVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreFormattedRespVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreItemVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreRespVO;
import com.thj.boot.module.business.service.aiScore.AiScoreService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * AiScoreController 单元测试
 * 测试控制器层逻辑，使用Mock对象模拟服务层
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AiScoreControllerIntegrationTest {

    @Mock
    private AiScoreService aiScoreService;

    @InjectMocks
    private AiScoreController aiScoreController;

    @BeforeEach
    void setUp() {
        // 重置所有Mock对象
        reset(aiScoreService);
    }

    @Test
    void testGetAiScore_WithExistingData() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreRespVO mockResponse = new AiScoreRespVO();
        mockResponse.setPatientId(patientId);
        mockResponse.setTotalScore(92);
        mockResponse.setLevel("高风险");
        mockResponse.setContent("患者多项指标异常，需要立即就医");
        mockResponse.setExpertComment("专家建议立即就医");

        // 配置Mock行为
        when(aiScoreService.getAiScore(patientId)).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode()); // 成功时code为0
        assertNotNull(result.getData());
        assertEquals(patientId, result.getData().getPatientId());
        assertEquals(92, result.getData().getTotalScore());
        assertEquals("高风险", result.getData().getLevel());
        assertEquals("患者多项指标异常，需要立即就医", result.getData().getContent());
        assertEquals("专家建议立即就医", result.getData().getExpertComment());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getAiScore(patientId);
    }

    @Test
    void testGetAiScore_WithNonExistentPatient() {
        // 准备测试数据
        Long patientId = 99999L;

        // 配置Mock行为 - 返回null表示未找到数据
        when(aiScoreService.getAiScore(patientId)).thenReturn(null);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode()); // 成功时code为0
        assertNull(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getAiScore(patientId);
    }

    @Test
    void testGetAiScore_WithValidPatientId() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreRespVO mockResponse = new AiScoreRespVO();
        mockResponse.setPatientId(patientId);
        mockResponse.setTotalScore(85);
        mockResponse.setLevel("中风险");
        mockResponse.setContent("患者血压偏高，建议定期监测");
        mockResponse.setExpertComment("专家建议定期监测血压");

        // 配置Mock行为
        when(aiScoreService.getAiScore(patientId)).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode()); // 成功时code为0
        assertNotNull(result.getData());
        assertEquals(patientId, result.getData().getPatientId());
        assertEquals(85, result.getData().getTotalScore());
        assertEquals("中风险", result.getData().getLevel());
        assertEquals("患者血压偏高，建议定期监测", result.getData().getContent());
        assertEquals("专家建议定期监测血压", result.getData().getExpertComment());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getAiScore(patientId);
    }

    @Test
    void testGetAiScore_WithNullPatientId() {
        // 配置Mock行为 - 当传入null时返回null
        when(aiScoreService.getAiScore(null)).thenReturn(null);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode()); // 成功时code为0
        assertNull(result.getData());

        // 验证Mock被调用了一次（Controller直接传递null给Service）
        verify(aiScoreService, times(1)).getAiScore(null);
    }

    // ==================== createAiReports 测试方法 ====================

    @Test
    void testCreateAiReports_Success() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(patientId);

        AiScoreRespVO mockResponse = createMockAiReportsRespVO(patientId);

        // 配置Mock行为
        when(aiScoreService.createAiReports(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiReports(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        
        // 验证基础字段
        assertEquals(1001L, result.getData().getId());
        assertEquals(patientId, result.getData().getPatientId());
        assertNotNull(result.getData().getCreateTime());
        assertEquals(88, result.getData().getTotalScore());
        assertEquals("优秀", result.getData().getLevel());
        assertEquals("AI综合评分报告：患者整体状况良好，各项指标正常", result.getData().getContent());
        
        // 验证详细字段
        assertNotNull(result.getData().getDetails());
        assertEquals("根据患者病历和诊疗信息进行AI智能评分", result.getData().getAiPrompt());
        assertEquals("基于多维度医疗数据分析，患者整体健康状况评估为优秀", result.getData().getAiReasoning());
        assertEquals("血液检查：各项指标正常；肾功能检查：功能良好", result.getData().getCheckResult());
        assertEquals("低风险：患者目前状况稳定，建议定期复查", result.getData().getRiskResult());
        assertNotNull(result.getData().getScoreResult());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiReports(eq(reqVO));
    }

    @Test
    void testCreateAiReports_WithNullPatientId() {
        // 准备测试数据
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(null);

        // 配置Mock行为 - 当传入null patientId时返回null
        when(aiScoreService.createAiReports(any(AiScoreCreateReqVO.class))).thenReturn(null);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiReports(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNull(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiReports(eq(reqVO));
    }

    @Test
    void testCreateAiReports_WithValidPatientId() {
        // 准备测试数据
        Long patientId = 67890L;
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(patientId);

        AiScoreRespVO mockResponse = createMockAiReportsRespVO(patientId);
        // 设置不同的测试数据值
        mockResponse.setId(2002L);
        mockResponse.setTotalScore(75);
        mockResponse.setLevel("良好");
        mockResponse.setContent("AI综合评分报告：患者状况良好，需要注意血压控制");
        mockResponse.setRiskResult("中风险：建议加强血压监测，定期复查");

        // 配置Mock行为
        when(aiScoreService.createAiReports(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiReports(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        
        // 验证基础字段
        assertEquals(2002L, result.getData().getId());
        assertEquals(patientId, result.getData().getPatientId());
        assertEquals(75, result.getData().getTotalScore());
        assertEquals("良好", result.getData().getLevel());
        assertEquals("AI综合评分报告：患者状况良好，需要注意血压控制", result.getData().getContent());
        
        // 验证风险评估字段
        assertEquals("中风险：建议加强血压监测，定期复查", result.getData().getRiskResult());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiReports(eq(reqVO));
    }

    @Test
    void testCreateAiReports_ServiceException() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(patientId);

        // 配置Mock行为 - 模拟Service层抛出业务异常
        String errorMessage = "AI服务调用失败：网络超时";
        when(aiScoreService.createAiReports(any(AiScoreCreateReqVO.class)))
            .thenThrow(new ServiceException(500, errorMessage));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            aiScoreController.createAiReports(reqVO);
        });

        // 验证异常信息
        assertEquals(500, exception.getCode());
        assertEquals(errorMessage, exception.getMessage());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiReports(eq(reqVO));
    }

    /**
     * 创建模拟的AI报告响应数据
     */
    private AiScoreRespVO createMockAiReportsRespVO(Long patientId) {
        AiScoreRespVO respVO = new AiScoreRespVO();
        
        // 基础字段
        respVO.setId(1001L);
        respVO.setPatientId(patientId);
        respVO.setCreateTime(new Date());
        respVO.setTotalScore(88);
        respVO.setLevel("优秀");
        respVO.setContent("AI综合评分报告：患者整体状况良好，各项指标正常");
        
        // 详细字段
        AiScoreDetailsVO details = new AiScoreDetailsVO();
        details.setMainComplaint(new AiScoreItemVO(10, 9, "主诉记录清晰完整"));
        details.setMedicalHistory(new AiScoreItemVO(20, 18, "病史记录详细准确"));
        details.setPhysicalExam(new AiScoreItemVO(15, 14, "体格检查全面规范"));
        details.setDiagnosis(new AiScoreItemVO(15, 14, "诊断准确明确"));
        details.setTreatment(new AiScoreItemVO(20, 19, "治疗方案科学合理"));
        details.setOverallEvaluation(new AiScoreItemVO(5, 5, "整体评价优秀"));
        details.setOther(new AiScoreItemVO(15, 14, "其他方面表现良好"));
        respVO.setDetails(details);
        
        respVO.setAiPrompt("根据患者病历和诊疗信息进行AI智能评分");
        respVO.setAiReasoning("基于多维度医疗数据分析，患者整体健康状况评估为优秀");
        respVO.setCheckResult("血液检查：各项指标正常；肾功能检查：功能良好");
        respVO.setRiskResult("低风险：患者目前状况稳定，建议定期复查");
        respVO.setScoreResult("{\"totalScore\":88,\"level\":\"优秀\",\"details\":{\"mainComplaint\":{\"maxScore\":10,\"actualScore\":9}}}");
        
        return respVO;
    }

    // ==================== getRenalCheck 测试方法 ====================

    @Test
    void testGetRenalCheck_Success() {
        // 准备测试数据
        Long patientId = 12345L;
        String mockRenalCheckData = "{\"患者ID\":12345,\"血红蛋白(g/L)\":\"120\",\"透析前钾(mmol/L)\":\"4.5\",\"透析前磷(mmol/L)\":\"1.8\",\"全段甲状旁腺激素(pg/ml)\":\"250\"}";

        // 配置Mock行为
        when(aiScoreService.getRenalCheck(patientId)).thenReturn(mockRenalCheckData);

        // 执行测试
        CommonResult<String> result = aiScoreController.getRenalCheck(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("患者ID"));
        assertTrue(result.getData().contains("血红蛋白"));
        assertTrue(result.getData().contains("120"));

        // 验证Mock调用
        verify(aiScoreService, times(1)).getRenalCheck(eq(patientId));
    }

    @Test
    void testGetRenalCheck_WithNullData() {
        // 准备测试数据
        Long patientId = 99999L;

        // 配置Mock行为 - 返回null表示无化验数据
        when(aiScoreService.getRenalCheck(patientId)).thenReturn(null);

        // 执行测试
        CommonResult<String> result = aiScoreController.getRenalCheck(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNull(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getRenalCheck(eq(patientId));
    }

    @Test
    void testGetRenalCheck_WithEmptyData() {
        // 准备测试数据
        Long patientId = 12345L;
        String emptyRenalCheckData = "{}";

        // 配置Mock行为
        when(aiScoreService.getRenalCheck(patientId)).thenReturn(emptyRenalCheckData);

        // 执行测试
        CommonResult<String> result = aiScoreController.getRenalCheck(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertEquals("{}", result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getRenalCheck(eq(patientId));
    }

    // ==================== getFormattedAiScore 测试方法 ====================

    @Test
    void testGetFormattedAiScore_Success() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreFormattedRespVO mockFormattedResponse = createMockFormattedRespVO(patientId);

        // 配置Mock行为
        when(aiScoreService.getFormattedAiScore(patientId)).thenReturn(mockFormattedResponse);

        // 执行测试
        CommonResult<AiScoreFormattedRespVO> result = aiScoreController.getFormattedAiScore(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(String.valueOf(patientId), result.getData().getPatientId());
        assertEquals(88, result.getData().getTotalScore());
        assertEquals("优秀", result.getData().getLevel());
        assertNotNull(result.getData().getCreateTime());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getFormattedAiScore(eq(patientId));
    }

    @Test
    void testGetFormattedAiScore_NotFound() {
        // 准备测试数据
        Long patientId = 99999L;

        // 配置Mock行为 - 返回null表示未找到记录
        when(aiScoreService.getFormattedAiScore(patientId)).thenReturn(null);

        // 执行测试
        CommonResult<AiScoreFormattedRespVO> result = aiScoreController.getFormattedAiScore(patientId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNull(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).getFormattedAiScore(eq(patientId));
    }

    // ==================== saveExpertComment 测试方法 ====================

    @Test
    void testSaveExpertComment_Success() {
        // 准备测试数据
        AiScoreExpertCommentReqVO reqVO = new AiScoreExpertCommentReqVO();
        reqVO.setPatientId(12345L);
        reqVO.setExpertComment("患者病情稳定，建议继续当前治疗方案，定期复查。");

        // 配置Mock行为
        when(aiScoreService.saveExpertComment(eq(12345L), eq("患者病情稳定，建议继续当前治疗方案，定期复查。")))
            .thenReturn(true);

        // 执行测试
        CommonResult<Boolean> result = aiScoreController.saveExpertComment(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertTrue(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).saveExpertComment(eq(12345L), eq("患者病情稳定，建议继续当前治疗方案，定期复查。"));
    }

    @Test
    void testSaveExpertComment_Failed() {
        // 准备测试数据
        AiScoreExpertCommentReqVO reqVO = new AiScoreExpertCommentReqVO();
        reqVO.setPatientId(99999L);
        reqVO.setExpertComment("测试评论");

        // 配置Mock行为 - 返回false表示保存失败
        when(aiScoreService.saveExpertComment(eq(99999L), eq("测试评论")))
            .thenReturn(false);

        // 执行测试
        CommonResult<Boolean> result = aiScoreController.saveExpertComment(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertFalse(result.getData());

        // 验证Mock调用
        verify(aiScoreService, times(1)).saveExpertComment(eq(99999L), eq("测试评论"));
    }

    @Test
    void testSaveExpertComment_ServiceException() {
        // 准备测试数据
        AiScoreExpertCommentReqVO reqVO = new AiScoreExpertCommentReqVO();
        reqVO.setPatientId(12345L);
        reqVO.setExpertComment("测试评论");

        // 配置Mock行为 - 模拟Service层抛出异常
        String errorMessage = "患者ID不能为空";
        when(aiScoreService.saveExpertComment(eq(12345L), eq("测试评论")))
            .thenThrow(new ServiceException(400, errorMessage));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            aiScoreController.saveExpertComment(reqVO);
        });

        // 验证异常信息
        assertEquals(400, exception.getCode());
        assertEquals(errorMessage, exception.getMessage());

        // 验证Mock调用
        verify(aiScoreService, times(1)).saveExpertComment(eq(12345L), eq("测试评论"));
    }

    // ==================== createAiScore 测试方法 ====================

    @Test
    void testCreateAiScore_Success() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(patientId);

        AiScoreRespVO mockResponse = createMockAiScoreRespVO(patientId);

        // 配置Mock行为
        when(aiScoreService.createAiScore(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiScore(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(patientId, result.getData().getPatientId());
        assertEquals(85, result.getData().getTotalScore());
        assertEquals("良好", result.getData().getLevel());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiScore(eq(reqVO));
    }

    @Test
    void testCreateAiScore_ServiceException() {
        // 准备测试数据
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(null); // 故意设置为null触发异常

        // 配置Mock行为 - 模拟Service层抛出参数异常
        String errorMessage = "患者ID不能为空";
        when(aiScoreService.createAiScore(any(AiScoreCreateReqVO.class)))
            .thenThrow(new ServiceException(400, errorMessage));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            aiScoreController.createAiScore(reqVO);
        });

        // 验证异常信息
        assertEquals(400, exception.getCode());
        assertEquals(errorMessage, exception.getMessage());

        // 验证Mock调用
        verify(aiScoreService, times(1)).createAiScore(eq(reqVO));
    }

    // ==================== testCreateAiScore 测试方法 ====================

    @Test
    void testTestCreateAiScore_Success() {
        // 准备测试数据
        Long patientId = 12345L;
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(patientId);

        AiScoreRespVO mockResponse = new AiScoreRespVO();
        mockResponse.setPatientId(patientId);
        mockResponse.setContent("测试AI服务响应内容");
        mockResponse.setExpertComment(new Date().toString());

        // 配置Mock行为
        when(aiScoreService.testCreateAiScore(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);

        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.testCreateAiScore(reqVO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(patientId, result.getData().getPatientId());
        assertEquals("测试AI服务响应内容", result.getData().getContent());
        assertNotNull(result.getData().getExpertComment());

        // 验证Mock调用
        verify(aiScoreService, times(1)).testCreateAiScore(eq(reqVO));
    }

    @Test
    void testTestCreateAiScore_ServiceException() {
        // 准备测试数据
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(null);

        // 配置Mock行为 - 模拟Service层抛出异常
        String errorMessage = "患者ID不能为空";
        when(aiScoreService.testCreateAiScore(any(AiScoreCreateReqVO.class)))
            .thenThrow(new ServiceException(400, errorMessage));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            aiScoreController.testCreateAiScore(reqVO);
        });

        // 验证异常信息
        assertEquals(400, exception.getCode());
        assertEquals(errorMessage, exception.getMessage());

        // 验证Mock调用
        verify(aiScoreService, times(1)).testCreateAiScore(eq(reqVO));
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建模拟的格式化AI评分响应数据
     */
    private AiScoreFormattedRespVO createMockFormattedRespVO(Long patientId) {
        AiScoreFormattedRespVO respVO = new AiScoreFormattedRespVO();
        respVO.setPatientId(String.valueOf(patientId));
        respVO.setTotalScore(88);
        respVO.setLevel("优秀");
        respVO.setCreateTime("2024-01-15 10:30:00");
        
        // 设置详细评分
        AiScoreDetailsVO details = new AiScoreDetailsVO();
        details.setMainComplaint(new AiScoreItemVO(10, 9, "主诉记录清晰完整"));
        details.setMedicalHistory(new AiScoreItemVO(20, 18, "病史记录详细准确"));
        details.setPhysicalExam(new AiScoreItemVO(15, 14, "体格检查全面规范"));
        respVO.setDetails(details);
        
        return respVO;
    }

    /**
     * 创建模拟的AI评分响应数据（用于createAiScore测试）
     */
    private AiScoreRespVO createMockAiScoreRespVO(Long patientId) {
        AiScoreRespVO respVO = new AiScoreRespVO();
        respVO.setPatientId(patientId);
        respVO.setTotalScore(85);
        respVO.setLevel("良好");
        respVO.setContent("AI评分内容：患者整体状况良好");
        respVO.setExpertComment("专家点评：建议继续当前治疗");
        respVO.setScoreResult("{\"totalScore\":85,\"level\":\"良好\"}");
        
        // 设置详细评分
        AiScoreDetailsVO details = new AiScoreDetailsVO();
        details.setMainComplaint(new AiScoreItemVO(10, 8, "主诉记录完整"));
        details.setMedicalHistory(new AiScoreItemVO(20, 17, "病史记录详细"));
        details.setPhysicalExam(new AiScoreItemVO(15, 12, "体格检查规范"));
        details.setAuxiliaryExam(new AiScoreItemVO(5, 4, "辅助检查完善"));
        details.setDiagnosis(new AiScoreItemVO(15, 13, "诊断准确"));
        details.setTreatment(new AiScoreItemVO(20, 18, "治疗方案合理"));
        details.setOverallEvaluation(new AiScoreItemVO(5, 4, "整体评价良好"));
        details.setOther(new AiScoreItemVO(10, 9, "其他方面表现良好"));
        respVO.setDetails(details);
        
        return respVO;
    }
}