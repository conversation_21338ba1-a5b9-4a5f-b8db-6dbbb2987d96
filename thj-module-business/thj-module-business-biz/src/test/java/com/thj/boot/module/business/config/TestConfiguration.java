package com.thj.boot.module.business.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;

/**
 * 测试配置类
 * 用于Spring Boot测试时的配置
 *
 * <AUTHOR>
 */
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {
    org.redisson.spring.starter.RedissonAutoConfiguration.class,
    org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class,
    com.thj.starter.mq.config.WeiduMQAutoConfiguration.class
})
@ComponentScan(
    basePackages = {
        "com.thj.boot.module.business.controller.admin.aiScore",
        "com.thj.boot.module.business.service.aiScore",
        "com.thj.boot.module.business.service.ai",
        "com.thj.boot.module.business.config",
        "com.thj.boot.module.business.convert.aiScore",
        "com.thj.boot.module.business.util"
    },
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = com.thj.boot.module.business.aspect.RepeatAspect.class
    )
)
@MapperScan("com.thj.boot.module.business.dal.mapper.aiScore")
public class TestConfiguration {

    /**
     * Mock DictDataApi for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.system.api.dict.DictDataApi dictDataApi() {
        return org.mockito.Mockito.mock(com.thj.boot.module.system.api.dict.DictDataApi.class);
    }

    /**
     * Mock OutpatientBloodService for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.business.service.outpatientblood.OutpatientBloodService outpatientBloodService() {
        return org.mockito.Mockito.mock(com.thj.boot.module.business.service.outpatientblood.OutpatientBloodService.class);
    }

    /**
     * Mock RenalProjectService for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.business.service.renalproject.RenalProjectService renalProjectService() {
        return org.mockito.Mockito.mock(com.thj.boot.module.business.service.renalproject.RenalProjectService.class);
    }

    /**
     * Mock FirstCourseRecordService for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.business.service.firstcourserecord.FirstCourseRecordService firstCourseRecordService() {
        return org.mockito.Mockito.mock(com.thj.boot.module.business.service.firstcourserecord.FirstCourseRecordService.class);
    }

    /**
     * Mock AdminUserApi for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.system.api.user.AdminUserApi adminUserApi() {
        return org.mockito.Mockito.mock(com.thj.boot.module.system.api.user.AdminUserApi.class);
    }

    /**
     * Mock OperLogApi for testing
     */
    @Bean
    @Primary
    public com.thj.boot.module.system.api.log.OperLogApi operLogApi() {
        return org.mockito.Mockito.mock(com.thj.boot.module.system.api.log.OperLogApi.class);
    }
}