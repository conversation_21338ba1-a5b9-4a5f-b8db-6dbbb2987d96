package com.thj.boot.module.business.controller.admin.aiScore;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.pojo.aiscore.vo.*;
import com.thj.boot.module.business.service.aiScore.AiScoreService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * AiScoreController 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AiScoreControllerTest {

    @Mock
    private AiScoreService aiScoreService;

    @InjectMocks
    private AiScoreController aiScoreController;



    @Test
    void testGetAiScore_Success() {
        // 准备测试数据
        Long patientId = 1L;
        AiScoreRespVO mockResponse = createMockAiScoreRespVO();
        
        // 模拟服务层调用
        when(aiScoreService.getAiScore(patientId)).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(patientId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(85, result.getData().getTotalScore());
        assertEquals("良好", result.getData().getLevel());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).getAiScore(patientId);
    }

    @Test
    void testGetAiScore_PatientNotFound() {
        // 准备测试数据
        Long patientId = 999L;
        
        // 模拟服务层返回null
        when(aiScoreService.getAiScore(patientId)).thenReturn(null);
        
        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.getAiScore(patientId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNull(result.getData());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).getAiScore(patientId);
    }

    @Test
    void testGetFormattedAiScore_Success() {
        // 准备测试数据
        Long patientId = 1L;
        AiScoreFormattedRespVO mockResponse = createMockFormattedRespVO();
        
        // 模拟服务层调用
        when(aiScoreService.getFormattedAiScore(patientId)).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiScoreFormattedRespVO> result = aiScoreController.getFormattedAiScore(patientId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals("1", result.getData().getPatientId());
        assertEquals(85, result.getData().getTotalScore());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).getFormattedAiScore(patientId);
    }

    @Test
    void testSaveExpertComment_Success() {
        // 准备测试数据
        AiScoreExpertCommentReqVO reqVO = new AiScoreExpertCommentReqVO();
        reqVO.setPatientId(1L);
        reqVO.setExpertComment("专家评价内容");
        
        // 模拟服务层调用
        when(aiScoreService.saveExpertComment(anyLong(), anyString())).thenReturn(true);
        
        // 执行测试
        CommonResult<Boolean> result = aiScoreController.saveExpertComment(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertTrue(result.getData());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).saveExpertComment(eq(1L), eq("专家评价内容"));
    }

    @Test
    void testSaveExpertComment_ValidationError() {
        // 准备无效的测试数据（缺少必填字段）
        AiScoreExpertCommentReqVO reqVO = new AiScoreExpertCommentReqVO();
        // 不设置patientId和expertComment，但Controller层不做验证，直接调用service
        
        // 模拟服务层调用
        when(aiScoreService.saveExpertComment(isNull(), isNull())).thenReturn(true);
        
        // 执行测试
        CommonResult<Boolean> result = aiScoreController.saveExpertComment(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertTrue(result.getData());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).saveExpertComment(isNull(), isNull());
    }

    @Test
    void testCreateAiScore_Success() {
        // 准备测试数据
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(1L);
        reqVO.setPatientName("张三");
        reqVO.setMedicalRecord("病历内容");
        reqVO.setTreatmentInfo("治疗信息");
        
        AiScoreRespVO mockResponse = createMockAiScoreRespVO();
        
        // 模拟服务层调用
        when(aiScoreService.createAiScore(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiScore(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(85, result.getData().getTotalScore());
        assertEquals("良好", result.getData().getLevel());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).createAiScore(any(AiScoreCreateReqVO.class));
    }

    @Test
    void testCreateAiScore_ValidationError() {
        // 准备无效的测试数据（缺少必填字段patientId）
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientName("张三");
        // 不设置patientId，但Controller层不做验证，直接调用service
        
        AiScoreRespVO mockResponse = createMockAiScoreRespVO();
        
        // 模拟服务层调用
        when(aiScoreService.createAiScore(any(AiScoreCreateReqVO.class))).thenReturn(mockResponse);
        
        // 执行测试
        CommonResult<AiScoreRespVO> result = aiScoreController.createAiScore(reqVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        
        // 验证服务层调用
        verify(aiScoreService, times(1)).createAiScore(any(AiScoreCreateReqVO.class));
    }



    /**
     * 创建模拟的AiScoreRespVO对象
     */
    private AiScoreRespVO createMockAiScoreRespVO() {
        AiScoreRespVO respVO = new AiScoreRespVO();
        respVO.setTotalScore(85);
        respVO.setLevel("良好");
        respVO.setContent("AI评分内容");
        respVO.setExpertComment("专家点评");
        respVO.setScoreResult("{\"totalScore\":85,\"level\":\"良好\"}");
        
        // 设置详细评分
        AiScoreDetailsVO details = new AiScoreDetailsVO();
        details.setMainComplaint(new AiScoreItemVO(10, 8, "主诉记录完整"));
        details.setMedicalHistory(new AiScoreItemVO(20, 17, "病史记录详细"));
        details.setPhysicalExam(new AiScoreItemVO(15, 12, "体格检查规范"));
        details.setAuxiliaryExam(new AiScoreItemVO(5, 4, "辅助检查完善"));
        details.setDiagnosis(new AiScoreItemVO(15, 13, "诊断准确"));
        details.setTreatment(new AiScoreItemVO(20, 18, "治疗方案合理"));
        details.setOverallEvaluation(new AiScoreItemVO(5, 4, "整体评价良好"));
        details.setOther(new AiScoreItemVO(10, 9, "其他方面表现良好"));
        
        respVO.setDetails(details);
        return respVO;
    }

    /**
     * 创建模拟的AiScoreFormattedRespVO对象
     */
    private AiScoreFormattedRespVO createMockFormattedRespVO() {
        AiScoreFormattedRespVO respVO = new AiScoreFormattedRespVO();
        respVO.setPatientId("1");
        respVO.setTotalScore(85);
        respVO.setLevel("良好");
        respVO.setCreateTime("2024-01-15");
        
        // 设置详细评分
        AiScoreDetailsVO details = new AiScoreDetailsVO();
        details.setMainComplaint(new AiScoreItemVO(10, 8, "主诉记录完整"));
        details.setMedicalHistory(new AiScoreItemVO(20, 17, "病史记录详细"));
        respVO.setDetails(details);
        
        return respVO;
    }
}