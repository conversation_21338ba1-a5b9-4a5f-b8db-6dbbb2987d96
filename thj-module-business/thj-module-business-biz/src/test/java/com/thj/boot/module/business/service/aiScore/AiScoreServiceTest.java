package com.thj.boot.module.business.service.aiScore;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.module.business.config.AiConfig;
import com.thj.boot.module.business.dal.datado.aiScore.AiScoreDO;
import com.thj.boot.module.business.dal.mapper.aiScore.AiScoreMapper;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreCreateReqVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreFormattedRespVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreRespVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordRespVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodRespVO;
import com.thj.boot.module.business.service.ai.AiService;
import com.thj.boot.module.business.service.firstcourserecord.FirstCourseRecordService;
import com.thj.boot.module.business.service.outpatientblood.OutpatientBloodService;
import com.thj.boot.module.business.service.renalproject.RenalProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("AiScoreService 单元测试")
class AiScoreServiceTest {

    @InjectMocks
    private AiScoreServiceImpl aiScoreService;

    @Mock
    private AiScoreMapper aiScoreMapper;
    @Mock
    private AiService aiService;
    @Mock
    private OutpatientBloodService outpatientBloodService;
    @Mock
    private RenalProjectService renalProjectService;
    @Mock
    private FirstCourseRecordService firstCourseRecordService;
    @Mock
    private AiConfig aiConfig;

    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();

    private final Long TEST_PATIENT_ID = 12345L;

    @BeforeEach
    void setUp() {
        when(aiConfig.getModel()).thenReturn("test-model");
        ReflectionTestUtils.setField(aiScoreService, "reportGenerationTimeoutMinutes", 1L);
        setupBaseMocks();
    }

    @Test
    @DisplayName("createAiReports - 成功路径测试")
    void testCreateAiReports_Success() {
        AiScoreCreateReqVO reqVO = new AiScoreCreateReqVO();
        reqVO.setPatientId(TEST_PATIENT_ID);
        setupMocksForSuccess();

        AiScoreRespVO result = aiScoreService.createAiReports(reqVO);

        assertNotNull(result);
        ArgumentCaptor<AiScoreDO> captor = ArgumentCaptor.forClass(AiScoreDO.class);
        verify(aiScoreMapper, times(1)).insert(captor.capture());
        assertEquals("优秀", captor.getValue().getScoreLevel());
    }

    @Test
    @DisplayName("saveExpertComment - 成功保存专家点评")
    void testSaveExpertComment_Success() {
        AiScoreDO existingRecord = new AiScoreDO();
        when(aiScoreMapper.selectOne(any(SFunction.class), eq(TEST_PATIENT_ID))).thenReturn(existingRecord);
        when(aiScoreMapper.updateById(any(AiScoreDO.class))).thenReturn(1);

        boolean result = aiScoreService.saveExpertComment(TEST_PATIENT_ID, "New Comment");

        assertTrue(result);
        verify(aiScoreMapper, times(1)).updateById(any(AiScoreDO.class));
    }

    @Test
    @DisplayName("getAiScore - 成功获取记录")
    void testGetAiScore_Success() {
        AiScoreDO dbRecord = new AiScoreDO();
        when(aiScoreMapper.selectOne(any(SFunction.class), eq(TEST_PATIENT_ID))).thenReturn(dbRecord);
        AiScoreRespVO result = aiScoreService.getAiScore(TEST_PATIENT_ID);
        assertNotNull(result);
    }

    @Test
    @DisplayName("getFormattedAiScore - 成功获取并格式化记录")
    void testGetFormattedAiScore_Success() {
        AiScoreDO dbRecord = new AiScoreDO();
        dbRecord.setScoreResult("{\"totalScore\":88,\"level\":\"优秀\",\"details\":{}}");
        dbRecord.setScoreLevel("优秀"); // Explicitly set the level
        when(aiScoreMapper.selectOne(any(SFunction.class), eq(TEST_PATIENT_ID))).thenReturn(dbRecord);
        AiScoreFormattedRespVO result = aiScoreService.getFormattedAiScore(TEST_PATIENT_ID);
        assertEquals("优秀", result.getLevel());
    }

    private void setupBaseMocks() {
        try {
            String outpatientBloodJson = "{\"id\": 1001, \"patientId\": 12345, \"patientName\": \"张三\"}";
            OutpatientBloodRespVO vo = objectMapper.readValue(outpatientBloodJson, OutpatientBloodRespVO.class);
            when(outpatientBloodService.getOutpatientBlood(TEST_PATIENT_ID)).thenReturn(vo);
            when(firstCourseRecordService.getFirstCourseRecord(any())).thenReturn(new FirstCourseRecordRespVO());
        } catch (JsonProcessingException e) {
            fail("Base mock setup failed", e);
        }
    }

    private void setupMocksForSuccess() {
        when(aiService.buildPrompt(anyString(), any(), any(), any())).thenReturn("Mocked Prompt String");
        when(aiService.scoreMedicalRecord(anyString())).thenReturn(Arrays.asList(
            "{\"totalScore\":88,\"level\":\"优秀\"}", "Mocked Reasoning", "Mocked Prompt"
        ));
        when(aiService.getAiRawResponse(contains("check"))).thenReturn("Check Result");
        when(aiService.getAiRawResponse(contains("risk"))).thenReturn("Risk Result");
    }
}