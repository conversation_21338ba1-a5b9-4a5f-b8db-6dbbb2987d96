server:
  port: 0  # 使用随机端口

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    dynamic: # 多数据源配置
      primary: master
      datasource:
        master:
          name: testdb
          url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
          username: sa
          password:
          driver-class-name: org.h2.Driver

  # Redis 配置 - 使用嵌入式Redis或禁用Redis
  redis:
    host: localhost
    port: 6379
    database: 0
    password:
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: NONE
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

# 测试环境配置
logging:
  level:
    com.thj.boot.module.business: DEBUG
    org.springframework.web: DEBUG
    org.springframework.boot.test: DEBUG

# AI配置 - 测试环境使用模拟配置
ai:
  deepseek:
    api-url: http://localhost:8000/v1/chat/completions
    api-key: test-key
    model: test-model
    temperature: 0.1
    max-tokens: 1000
    timeout: 30000
    retry-count: 1

# 基础设施配置 - 测试环境
infra:
  hostname: http://localhost:8080/admin-api/business/infra/

# 文件配置 - 测试环境
file:
  staticPath: http://localhost:8080
  staticPatternPath: /upimages
  uploadFolder: /tmp/test/

# HIS系统配置 - 测试环境
his:
  drug: http://localhost:8080/his/drug
  consumables: http://localhost:8080/his/consumables
  prescriptionItem: http://localhost:8080/his/prescriptionItem
  prescriptionItemType: http://localhost:8080/his/prescriptionItemType
  combo: http://localhost:8080/his/combo
