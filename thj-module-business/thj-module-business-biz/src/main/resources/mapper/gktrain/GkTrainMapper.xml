<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.gktrain.GkTrainMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryYearList" resultType="com.thj.boot.module.business.pojo.gktrain.vo.GkTrainRespVO">
        select DATE_FORMAT(plan_time,'%Y-%m') as mouthTime, count(*) as cunt, sum(people_no) as peopleCunt from wl_gk_train
        where 1=1
            <if test="year != null">
               and year = #{year}
            </if>
        <if test="mouth != null">
            and mouth = #{mouth}
        </if>
        group by mouthTime;
    </select>

    <select id="queryListOnMouth" resultType="com.thj.boot.module.business.dal.datado.gktrain.GkTrainDO">
        select * from wl_gk_train
                 where 1=1 and deleted = 0
        <if test="year != null">
            and year = #{year}
        </if>
        <if test="mouth != null">
            and mouth = #{mouth}
        </if>
    </select>

    <select id="queryGkSystem" resultType="com.thj.boot.module.business.pojo.gktrain.vo.GkDictTypeVo">
        select id, sort,label,`value`
        from system_dict_data
        where dict_type = 'infection_control'
    </select>

    <select id="h5List" resultType="com.thj.boot.module.business.dal.datado.gksystem.GkSystemDO">
        select * from wl_gk_system where group_id in (select id from wl_gk_group where group_type = 'gk_system')
    </select>


</mapper>
