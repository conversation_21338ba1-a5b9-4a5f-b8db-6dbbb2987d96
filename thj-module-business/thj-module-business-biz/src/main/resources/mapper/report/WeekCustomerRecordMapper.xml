<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.report.WeekCustomerRecordMapper">

    <select id="getOutComeList" resultType="com.thj.boot.module.business.dal.datado.report.WeekCustomerRecordDO">
        SELECT
        main.id AS outComeId,
        main.type AS category,
        main.patient_name AS `name`,
        main.classify AS lossType,
        main.reason AS lossCategory,
        main.dialyze_no AS dialyzeNo,
        main.dept_id AS deptId,
        CASE
        WHEN main.type IN ('17', '18') THEN prev.prognosis_time
        ELSE main.prognosis_time
        END AS receiveTime,
        CASE
        WHEN main.type IN ('17', '18') THEN main.prognosis_time
        ELSE NULL
        END AS transferOutTime,
        MIN(hemo.end_dialyze_time) AS firstDialysisTime
        FROM wl_outcome_record AS main
        LEFT JOIN wl_outcome_record AS prev
        ON prev.patient_id = main.patient_id
        AND prev.type = '16'
        AND prev.prognosis_time &lt;= main.prognosis_time AND prev.deleted = 0
        LEFT JOIN wl_hemodialysis_manager AS hemo
        ON hemo.patient_id = main.patient_id
        AND hemo.deleted = 0
        AND hemo.end_dialyze_time > CASE
        WHEN main.type IN ('17', '18') THEN prev.prognosis_time
        ELSE main.prognosis_time
        END
        WHERE main.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND main.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="req.startTime != null and req.endTime != null">
            AND main.prognosis_time between #{req.startTime} AND #{req.endTime}
        </if>
        GROUP BY
        main.id
        HAVING firstDialysisTime IS NOT NULL;
    </select>

    <select id="getPatientCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT wp.id) AS patient_count
        FROM wl_patient wp
        JOIN (
        SELECT
        wor.patient_id,
        wor.prognosis_time AS prognosisTime
        FROM wl_outcome_record wor
        JOIN (
        SELECT
        patient_id,
        MAX(id) AS id
        FROM wl_outcome_record
        WHERE deleted = 0
        AND prognosis_time &lt; #{endTime}
        GROUP BY patient_id
        ) temp ON temp.id = wor.id
        where wor.type = '16'
        ) AS wor ON wor.patient_id = wp.id
        JOIN wl_hemodialysis_manager hd
        ON hd.patient_id = wp.id
        AND hd.deleted = 0
        AND hd.end_dialyze_time &gt; wor.prognosisTime
        WHERE wp.deleted = 0
        AND wp.dept_id IN
        <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="getPatientCountByDept" resultType="java.util.Map">
        SELECT wp.dept_id AS deptId, COUNT(DISTINCT wp.id) AS num
        FROM wl_patient wp
        LEFT JOIN (
        SELECT
        wor.patient_id,
        wor.prognosis_time AS prognosisTime
        FROM wl_outcome_record wor
        JOIN (
        SELECT max(id) as id
        FROM wl_outcome_record
        WHERE deleted = 0 AND prognosis_time &lt; #{req.endTime}
        GROUP BY patient_id
        ) temp ON temp.id = wor.id
        WHERE wor.type = '16'
        ) wor ON wor.patient_id = wp.id
        JOIN wl_hemodialysis_manager hd
        ON hd.patient_id = wp.id
        AND hd.deleted = 0
        AND hd.end_dialyze_time &gt; wor.prognosisTime
        WHERE wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
        group by wp.dept_id
    </select>


    <select id="selectCompanyWeekStatistics" resultType="com.thj.boot.module.business.service.report.vo.CompanyWeekStatistics">
        SELECT dept.id                      AS deptId,
               dept.name                    AS deptName,
               COALESCE(w.comeNum, 0)       AS weekAdd,
               COALESCE(w.outNum, 0)        AS weekLose,
               COALESCE(w.differenceNum, 0) AS weekDifference,
               COALESCE(m.differenceNum, 0) AS monthDifference,
               COALESCE(y.differenceNum, 0) AS yearDifference
        FROM system_dept AS dept
                 -- 周统计子查询（给定时间区间）
                 LEFT JOIN (SELECT dept_id,
                                   SUM(IF(type = '16', 1, 0))                                       AS comeNum,
                                   SUM(IF(type IN ('17', '18'), 1, 0))                              AS outNum,
                                   SUM(IF(type = '16', 1, 0)) - SUM(IF(type IN ('17', '18'), 1, 0)) AS differenceNum
                            FROM wl_outcome_record
                            WHERE deleted = 0
                              AND prognosis_time BETWEEN #{req.startTime} AND #{req.endTime}
                            GROUP BY dept_id) AS w ON dept.id = w.dept_id
                -- 月统计子查询（结束时间所在月）
                 LEFT JOIN (SELECT dept_id,
                                   SUM(IF(type = '16', 1, 0))                                       AS comeNum,
                                   SUM(IF(type IN ('17', '18'), 1, 0))                              AS outNum,
                                   SUM(IF(type = '16', 1, 0)) - SUM(IF(type IN ('17', '18'), 1, 0)) AS differenceNum
                            FROM wl_outcome_record
                            WHERE deleted = 0
                              AND prognosis_time BETWEEN DATE_FORMAT(#{req.endTime}, '%Y-%m-01')
                                AND LAST_DAY(#{req.endTime})
                            GROUP BY dept_id) AS m ON dept.id = m.dept_id
                -- 年统计子查询（结束时间所在年）
                 LEFT JOIN (SELECT dept_id,
                                   SUM(IF(type = '16', 1, 0))                                       AS comeNum,
                                   SUM(IF(type IN ('17', '18'), 1, 0))                              AS outNum,
                                   SUM(IF(type = '16', 1, 0)) - SUM(IF(type IN ('17', '18'), 1, 0)) AS differenceNum
                            FROM wl_outcome_record
                            WHERE deleted = 0
                              AND prognosis_time BETWEEN DATE_FORMAT(#{req.endTime}, '%Y-01-01')
                                AND DATE_FORMAT(#{req.endTime}, '%Y-12-31')
                            GROUP BY dept_id) AS y ON dept.id = y.dept_id
        WHERE dept.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND dept.id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>


</mapper>
