<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper">
    <update id="upDpIdNull" >
        update wl_dialysis_advice set dp_id = null,dpd_id = null where id in
        <foreach item="item" collection="collect" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </update>
</mapper>
