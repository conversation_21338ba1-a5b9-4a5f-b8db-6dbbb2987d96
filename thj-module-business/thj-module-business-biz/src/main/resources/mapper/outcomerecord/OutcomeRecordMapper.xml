<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper">

    <sql id="selectPatientStatus">
        <if test="req.patientStatus != null"> wor.pname AS patientStatus,</if>
    </sql>
    <sql id="orderByDescPatientStatus">
        <if test="req.patientStatus != null"> ORDER BY wor.prognosisTime DESC</if>
    </sql>
    <sql id="joinPatientByStatus">
        <if test="req.patientStatus != null">
            LEFT JOIN (
            SELECT wor.id AS worId,wor.patient_id, wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify,
            wor.reason AS reason, wms.pname
            FROM wl_outcome_record wor
            JOIN (
            SELECT max(wor.id) as id FROM wl_outcome_record wor WHERE wor.deleted = 0
            <if test="req.endTime != null">AND wor.prognosis_time &lt;= #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY wor.patient_id
            ) temp ON temp.id = wor.id
            LEFT JOIN wl_motto_simple wms ON  wms.id = wor.type
            WHERE wms.pid = 0 AND wms.deleted = 0
            ) wor ON wor.patient_id = wp.id
        </if>
    </sql>
    <sql id="joinPatientByStatus1">
        <if test="req.patientStatus != null">
            LEFT JOIN (
            SELECT
            wor.id AS worId,
            wor.patient_id,
            wor.prognosis_time AS prognosisTime,
            wor.type AS type,
            wor.classify AS classify,
            wor.reason AS reason,
            wms.pname,
            ROW_NUMBER() OVER (PARTITION BY wor.patient_id ORDER BY wor.id DESC) rn
            FROM wl_outcome_record wor
            LEFT JOIN wl_motto_simple wms ON wms.id = wor.type
            WHERE wms.pid = 0
            AND wms.deleted = 0
            <if test="req.endTime != null">and  wor.prognosis_time <![CDATA[ <=]]> #{req.endTime,jdbcType=TIMESTAMP}</if>
            ) wor ON wor.patient_id = wp.id and wor.rn = 1

        </if>
    </sql>
    <sql id="joinPatientStatus">
        <if test="req.patientStatus != null">
        <choose>
            <when test="req.patientStatus != null and req.patientStatus == 1">
            LEFT JOIN (
                SELECT wor.patient_id, wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.type )
                WHERE wms.pid = 0 AND wms.deleted = 0
            ) wor ON wor.patient_id = wp.id
            </when>
            <when test="req.patientStatus != null and req.patientStatus == 2">
            LEFT JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.type )
                WHERE wms.pid = 0 AND wms.deleted = 0
            ) wor ON wor.patient_id = wp.id
            </when>
            <otherwise>
            LEFT JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.type )
                WHERE wms.pid = 0 AND wms.deleted = 0
            ) wor ON wor.patient_id = wp.id
            </otherwise>
        </choose>
        </if>
    </sql>
    <sql id="joinAggregatePatientStatus">
        <if test="req.patientStatus != null">
        <choose>
            <when test="req.patientStatus != null and req.patientStatus == 1">
            RIGHT JOIN (
                SELECT wor.patient_id, wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    <if test="req.type != null and req.type != ''">AND wor.type = #{req.type}</if>
                    <if test="req.classify != null and req.classify != ''">AND wor.classify = #{req.classify}</if>
                    <if test="req.reason != null and req.reason != ''">AND wor.reason = #{req.reason}</if>
                    <if test="req.reasonList != null and req.reasonList.size() > 0">AND wor.reason IN <foreach collection="req.reasonList" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach></if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
            ) wor ON wor.patient_id = wp.id
            </when>
            <when test="req.patientStatus != null and req.patientStatus == 2">
            RIGHT JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    <if test="req.type != null and req.type != ''">AND wor.type = #{req.type}</if>
                    <if test="req.classify != null and req.classify != ''">AND wor.classify = #{req.classify}</if>
                    <if test="req.reason != null and req.reason != ''">AND wor.reason = #{req.reason}</if>
                    <if test="req.reasonList != null and req.reasonList.size() > 0">AND wor.reason IN <foreach collection="req.reasonList" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach></if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
            ) wor ON wor.patient_id = wp.id
            </when>
            <otherwise>
            LEFT JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    <if test="req.type != null and req.type != ''">AND wor.type = #{req.type}</if>
                    <if test="req.classify != null and req.classify != ''">AND wor.classify = #{req.classify}</if>
                    <if test="req.reason != null and req.reason != ''">AND wor.reason = #{req.reason}</if>
                    <if test="req.reasonList != null and req.reasonList.size() > 0">AND wor.reason IN <foreach collection="req.reasonList" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach></if>
                    GROUP BY wor.patient_id
                ) temp ON temp.id = wor.id
            ) wor ON wor.patient_id = wp.id
            </otherwise>
        </choose>
        </if>
    </sql>

    <sql id="joinOutComeByPname">
            RIGHT JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY wor2.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.type )
                WHERE  wms.deleted = 0 <if test="req.type != null and req.type != ''">AND  wms.pname like concat('%', #{req.type,jdbcType=VARCHAR}, '%')</if>
            ) wor1 ON wor1.patient_id = wp.id
                <choose>
                    <when test="req.type != null and req.type != ''">LEFT</when>
                    <otherwise>RIGHT</otherwise>
                </choose>
            JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY wor2.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.classify )
                WHERE  wms.deleted = 0  <if test="req.classify != null and req.classify != ''">AND wms.pname like concat('%', #{req.classify,jdbcType=VARCHAR}, '%')</if>
            ) wor2 ON wor2.patient_id = wp.id
            <choose>
                    <when test="req.type != null and req.type != ''">LEFT</when>
                    <when test="req.classify != null and req.classify != ''">LEFT</when>
                    <otherwise>RIGHT</otherwise>
                </choose>
            JOIN (
                SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
                FROM wl_outcome_record wor
                JOIN (
                    SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY wor2.patient_id
                ) temp ON temp.id = wor.id
                LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.reason )
                WHERE  wms.deleted = 0  <if test="req.reason != null and req.reason != ''"> AND wms.pname like concat('%', #{req.reason,jdbcType=VARCHAR}, '%')</if>
            ) wor3 ON wor3.patient_id = wp.id
    </sql>
    <sql id="joinOutCome">
        <choose>
            <when test="req.patientStatus != null and req.patientStatus == 1">RIGHT</when>
            <when test="req.patientStatus != null and req.patientStatus == 2">LEFT</when>
            <otherwise>LEFT</otherwise>
        </choose>
         JOIN (
            SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
            FROM wl_outcome_record wor
            JOIN (
            SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
            <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY wor2.patient_id
            ) temp ON temp.id = wor.id
            LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.type )
            WHERE  wms.deleted = 0
            <choose>
                <when test="req.patientStatus != null and req.patientStatus == 1">AND wms.pname != #{req.transferOut}</when>
                <when test="req.patientStatus != null and req.patientStatus == 2">AND wms.pname = #{req.transferOut}</when>
            </choose>
        ) wor1 ON wor1.patient_id = wp.id
        LEFT JOIN (
            SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
            FROM wl_outcome_record wor
            JOIN (
            SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
            <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY wor2.patient_id
            ) temp ON temp.id = wor.id
            LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.classify )
            WHERE  wms.deleted = 0
        ) wor2 ON wor2.patient_id = wp.id
        LEFT JOIN (
            SELECT wor.patient_id,wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason, wms.pname
            FROM wl_outcome_record wor
            JOIN (
                SELECT max(wor2.id) as id  FROM wl_outcome_record wor2 WHERE wor2.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND wor2.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY wor2.patient_id
            ) temp ON temp.id = wor.id
            LEFT JOIN  wl_motto_simple wms ON ( wms.id = wor.reason )
            WHERE  wms.deleted = 0
        ) wor3 ON wor3.patient_id = wp.id
    </sql>

    <select id="queryIndividuation" resultType="java.util.Map">
        SELECT type , COUNT(type) AS total_amount ,(CASE WHEN type = '16' THEN '转入'
                                                         WHEN type = '17' THEN '转出'
                                                         WHEN type = '19' THEN '住院'
                                                         ELSE 0
            END) AS description
        FROM wl_outcome_record
        where type in (16,17,19)
        GROUP BY type ;
    </select>
</mapper>
