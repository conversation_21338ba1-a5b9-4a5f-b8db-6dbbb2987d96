<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.system.dal.mapper.dept.DeptMapper">
    <select id="selectByUserId" resultType="com.thj.boot.module.system.dal.datado.dept.DeptDO">
        select s.* from system_dept
                            s join system_users u on find_in_set(s.id,u.dept_ids) and s.deleted = 0
        where u.id = #{id}
    </select>
</mapper>