<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.courserecord.CourseRecordMapper">
    <update id="updateRecord">
        update wl_course_record set is_completed = 2,content = #{content}
                                    where id = #{id}
    </update>

    <select id="getDoctorWorkloadCount" resultType="java.util.Map">
        SELECT
            COUNT( wcr.user_id ) AS count, wcr.user_id AS userId, su.nickname AS nickName
        <if test="req.userId != null">, wcr.format, wcr.formatDate</if>
        FROM system_users su
        JOIN (
            SELECT wcr.user_id,wcr.id
        <if test="req.userId != null">, DATE_FORMAT( wcr.start_time,'%Y-%m-%d') formatDate, DATE_FORMAT( wcr.start_time, #{req.format,jdbcType=VARCHAR} ) format</if>
            FROM wl_course_record wcr
            JOIN (
                SELECT max( id ) maxId FROM wl_course_record wcr
                WHERE wcr.deleted = '0'
            <if test="req.startTime != null and req.endTime != null">AND (wcr.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
            <if test="req.userId != null"> AND wcr.user_id = #{req.userId,jdbcType=BIGINT}</if>
            <if test="req.userIdList != null and req.userIdList.size() >0 ">
                AND wcr.user_id IN <foreach collection="req.userIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
                GROUP BY wcr.patient_id
            ) temp ON temp.maxId = wcr.id
        ) wcr ON su.id = wcr.user_id
        GROUP BY
        <choose>
            <when test="req.userId != null">
                format
            </when>
            <otherwise>
                wcr.user_id
            </otherwise>
        </choose>

    </select>
    <select id="getDoctorWorkloadPage" resultType="java.util.Map">
        SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/> wcr.user_id AS userId, wcr.content, wcr.name, su.nickname AS doctorName
        FROM system_users su
        JOIN (
            SELECT wcr.user_id,wcr.id,wcr.patient_id, wcr.content, wcr.name, wcr.create_time AS createTime
            FROM wl_course_record wcr
            JOIN (
                SELECT max( id ) maxId FROM wl_course_record wcr
                WHERE wcr.deleted = '0'
            <if test="req.startTime != null and req.endTime != null">AND (wcr.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
            <if test="req.userId != null"> AND wcr.user_id = #{req.userId,jdbcType=BIGINT}</if>
            <if test="req.userIdList != null and req.userIdList.size() >0 ">
                AND wcr.user_id IN <foreach collection="req.userIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
                GROUP BY wcr.patient_id
            ) temp ON temp.maxId = wcr.id
        ) wcr ON su.id = wcr.user_id
        LEFT JOIN  wl_patient wp ON wp.id = wcr.patient_id
    </select>
    <select id="selectSourceCourseRecord"
            resultType="com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO">
        select * from wl_course_record where record_name like '%季度小结%' and is_completed = 0
                                         and start_time > '2025-03-01' and deleted = 0 limit 100
    </select>
</mapper>
