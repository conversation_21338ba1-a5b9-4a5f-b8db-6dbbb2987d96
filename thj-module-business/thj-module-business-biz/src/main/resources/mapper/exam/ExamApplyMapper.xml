<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.exam.ExamApplyMapper">
    <select id="getPatientListByDate" parameterType="com.thj.boot.module.business.dal.datado.exam.ExamApplyVo" resultType="com.thj.boot.module.business.pojo.patient.vo.PatientRespVO">
        select pat_id as dialyzeNo,pat_name as name from exam_apply where DATE(sampling_time) = #{orderDate}
        <if test="dayState != null and dayState != ''">
            and day_state =#{dayState}
        </if>
        group by pat_id order by create_time desc;
    </select>

    <select id="getPatientListByDateBody" parameterType="com.thj.boot.module.business.dal.datado.exam.ExamApplyVo" resultType="com.thj.boot.module.business.pojo.patient.vo.PatientRespVO">
        select pat_id as dialyzeNo,pat_name as name from exam_apply where DATE(sampling_time) = #{orderDate}
        group by pat_id order by create_time desc;
    </select>

    <select id="getExamList" resultType="com.thj.boot.module.business.dal.datado.exam.ExamApplyDO">
        select * from exam_apply where DATE(sampling_time) = #{orderDate};
    </select>

    <select id="findExamListByDate" parameterType="com.thj.boot.module.business.dal.datado.exam.ExamApplyVo" resultType="com.thj.boot.module.business.dal.datado.exam.DialysisAdviceTransferDO">
        select id as id,dialyze_no as dialyzeNo,patient_name as patientName,advice_id as adviceId,dept_id as deptId,printed as printed,
               advice_name as adviceName,advice_user as adviceUser,sample_type as sampleType,cap_color as capColor,
               take_num as takeNum,  dept_name as deptName,pat_sex as patSex,pat_age as patAge,start_time as startTime,
               create_time as createTime,remark as remark,day_state as dayState
        from wl_dialysis_advice_transfor where DATE(start_time) = #{orderDate}
        <if test="dayState != null and dayState != ''">
            and day_state =#{dayState}
        </if>
        and dept_id = #{deptId} and printed is null
        and id in (select id from wl_dialysis_advice where dept_id = #{deptId} and DATE(start_time) = #{orderDate} and type = 0 and deleted = 0);
    </select>

    <update id="updateDialysisAdviceTransfer" parameterType="com.thj.boot.module.business.dal.datado.exam.DialysisAdviceTransferDO">
       update wl_dialysis_advice_transfor set printed = 1 where id = #{id} ;
    </update>
</mapper>
