<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.MedicalDischargeSummaryMapper">

<!--    <resultMap id="BaseResultMap" type="com.thj.boot.module.business.dal.datado.MedicalDischargeSummary">-->
<!--            <id property="id" column="id" />-->
<!--            <result property="deptid" column="deptId" />-->
<!--            <result property="patientid" column="patientId" />-->
<!--            <result property="birthplace" column="birthPlace" />-->
<!--            <result property="admissiondate" column="admissionDate" />-->
<!--            <result property="leavedate" column="leaveDate" />-->
<!--            <result property="admissiondiagnosis" column="admissionDiagnosis" />-->
<!--            <result property="admissionstatus" column="admissionStatus" />-->
<!--            <result property="dischargediagnosis" column="dischargeDiagnosis" />-->
<!--            <result property="dischargestatus" column="dischargeStatus" />-->
<!--            <result property="dischargeorder" column="dischargeOrder" />-->
<!--            <result property="leaverecorddate" column="leaveRecordDate" />-->
<!--            <result property="createdby" column="createdBy" />-->
<!--            <result property="createdat" column="createdAt" />-->
<!--            <result property="updatedby" column="updatedBy" />-->
<!--            <result property="updatedat" column="updatedAt" />-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,deptId,patientId,birthPlace,admissionDate,leaveDate,-->
<!--        admissionDiagnosis,admissionStatus,dischargeDiagnosis,dischargeStatus,dischargeOrder,-->
<!--        leaveRecordDate,createdBy,createdAt,updatedBy,updatedAt-->
<!--    </sql>-->

</mapper>
