<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper">
    <select id="getVolumeAnalysis2" resultType="java.util.Map">
        SELECT
        COUNT(*) AS count, wac.dialyze_name as dialyzeName
        FROM
        wl_arrange_classes wac
        LEFT JOIN wl_patient wp ON wac.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        WHERE
        wac.deleted = '0'
        <if test="req.startTime != null and req.endTime != null">AND (wac.create_time BETWEEN
            #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})
        </if>
        GROUP BY wac.dialyze_name
    </select>

    <select id="selectPageHistory"
            parameterType="com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesPageReqVO"
            resultType="com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO">
        SELECT t1.id,
        t1.patient_id,
        t1.patient_name,
        t1.classes_time,
        t1.day_state,
        t1.dialysis_name,
        t1.week_state,
        t1.facility_id,
        t1.facility_subarea_id,
        t1.deleted,
        t2.NAME AS facilitySubareaName,
        t4.`name` AS equipmentType
        FROM wl_arrange_classes t1
        LEFT JOIN wl_facility_subarea t2 ON t1.facility_subarea_id = t2.id
        LEFT JOIN wl_facility_manager t3 ON t3.facility_id = t1.facility_id
        LEFT JOIN wl_facility_name t4 ON t4.id = t3.facility_name_id
        <where>
            t1.deleted = 1 AND t1.temp_type = 0
            <if test="patientName!=null and patientName!=''">
                AND t1.patient_name like concat('%',#{patientName},'%')
            </if>
            <if test="classesTime!=null">
                AND t1.classes_time = #{classesTime}
            </if>
        </where>
        order by t1.classes_time desc
        limit #{pageNo} , #{pageSize}

    </select>

    <select id="selectCountHistory"
            parameterType="com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesPageReqVO"
            resultType="long">
        SELECT count(*) total
        FROM wl_arrange_classes t1
        LEFT JOIN wl_facility_subarea t2 ON t1.facility_subarea_id = t2.id
        LEFT JOIN wl_facility_manager t3 ON t3.facility_id = t1.facility_id
        LEFT JOIN wl_facility_name t4 ON t4.id = t3.facility_name_id
        <where>
            t1.deleted = 1 AND t1.temp_type = 0
            <if test="patientName!=null and patientName!=''">
                AND t1.patient_name like concat('%',#{patientName},'%')
            </if>
            <if test="classesTime!=null">
                AND t1.classes_time = #{classesTime}
            </if>
        </where>
    </select>


    <update id="updateDeleted"
            parameterType="com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesUpdateReqVO">
        UPDATE wl_arrange_classes
        SET deleted=0
        WHERE id = #{id}
    </update>

    <update id="updateDeletedBatch">
        UPDATE wl_arrange_classes
        SET deleted=1
        WHERE id in
        <foreach collection="collect" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="deleteArrange" parameterType="com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO">
        update wl_arrange_classes set deleted = 1 where (patient_id = #{patientId} or (day_state =  #{dayState} and facility_id = #{facilityId})) and week_state = #{weekState} and temp_type = '0'
        and classes_time = #{classesTime}
    </update>

    <update id="deleteByClassTime" >
        update wl_arrange_classes set deleted = 1,update_time=#{updateTime}, updater=#{userId} where classes_time = #{time} and temp_type = '0'
    </update>

    <update id="deleteByAfterClassTime" >
        update wl_arrange_classes set deleted = 1,update_time=#{updateTime}, updater=#{userId} where classes_time between #{startTime} and #{endTime} and temp_type = '0'
    </update>

    <select id="selectArrangeList"
            parameterType="long"
            resultType="com.thj.boot.module.business.pojo.arrangeclasses.vo.SynToHisArrangeVo">
        SELECT t1.day_state as dayState , t1.classes_time as classesTime, t2.id_card as idCard,t3.hospital_id as hospitalId
        FROM wl_arrange_classes t1
                 left join wl_patient t2 on t1.patient_id = t2.id
                 left join system_dept t3 on t1.dept_id = t3.id where t3.id = #{deptId} and (date_format(`t1`.`classes_time`,'%Y%m') = date_format(curdate(),'%Y%m'))
                and t1.deleted =0
    </select>
</mapper>
