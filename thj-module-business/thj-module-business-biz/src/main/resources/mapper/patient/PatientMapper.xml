<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.patient.PatientMapper">

    <sql id="selectPatientInfo">
        wp.id AS patientId, wp.`name` AS patientName, wp.dialyze_no AS dialyzeNo, wp.sex, wp.dialyze_age AS dialysisAge, wp.age, wp.medic, wp.nurse,
    </sql>
    <sql id="selectDataSort">
        <if test="page != null">( @i := @i + 1 ) AS sort,</if>
    </sql>
    <sql id="joinItable">
        <if test="page != null"> ,( SELECT @i := (#{page.current} - 1) * #{page.size} ) AS itable </if>
    </sql>
    <sql id="wherePatientCondition">
        AND wp.patient_type = '1'
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <if test="req.firstReceiveTimeStart != null">AND wp.first_receive_time &gt; #{req.firstReceiveTimeStart,jdbcType=TIMESTAMP}</if>
        <if test="req.firstReceiveTimeEnd != null">AND wp.first_receive_time &lt;= #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}</if>
        <if test="req.maxAge != null ">AND wp.age &lt; #{req.maxAge,jdbcType=INTEGER}</if>
        <if test="req.minAge != null">AND wp.age &gt; #{req.minAge,jdbcType=INTEGER}</if>
        <if test="req.patientStatus != null">
            <choose>
                <when test="req.patientStatus == 1">AND wp.patient_type_source = '00'</when>
                <when test="req.patientStatus == 2">AND wp.patient_type_source = '01'</when>
            </choose>
        </if>

    </sql>
    <sql id="wherePatientCondition1">
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <if test="req.diadialyzeAge != null || req.diadialyzeAge != 0">
            <if test="req.diadialyzeAge == 1">
                <choose>
                    <when test="req.endTime != null">AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &gt; 3</when>
                    <otherwise>AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, NOW()) &gt; 3</otherwise>
                </choose>
            </if>
            <if test="req.diadialyzeAge == 2">
                <choose>
                    <when test="req.endTime != null">AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &lt;= 3</when>
                    <otherwise>AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, NOW()) &lt;= 3</otherwise>
                </choose>
            </if>
        </if>
        <if test="req.maxAge != null ">AND wp.age &lt; #{req.maxAge,jdbcType=INTEGER}</if>
        <if test="req.minAge != null">AND wp.age &gt; #{req.minAge,jdbcType=INTEGER}</if>
        <if test="req.patientStatus != null">
            <choose>
                <when test="req.patientStatus == 1">AND wp.patient_type_source = '00'</when>
                <when test="req.patientStatus == 2">AND wp.patient_type_source = '01'</when>
            </choose>
        </if>

    </sql>

    <select id="getPatientListWithPrognosisTime" resultType="com.thj.boot.module.business.service.departmentControl.vo.PatientOutComeVO">
        select wp.*,wor.prognosisTime from wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        where wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus1"/>
    </select>

    <sql id="wherePatientConditionByStatus">
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="req.maxAge != null ">AND wp.age &lt; #{req.maxAge,jdbcType=INTEGER}</if>
        <if test="req.minAge != null">AND wp.age &gt; #{req.minAge,jdbcType=INTEGER}</if>
        <if test="req.patientStatus != null">
            <choose>
                <when test="req.patientStatus == 1">AND ((wp.patient_type_source = '00'
                    AND ((wp.receive_time &lt;= #{req.endTime,jdbcType=TIMESTAMP} AND wor.worId IS NULL) or (wor.worId
                    IS NOT NULL
                    AND wor.type != 17))) OR (wp.patient_type_source = '01' AND (wor.type != 17 OR (wor.type = 17 AND
                    DATE_ADD(wor.prognosisTime, INTERVAL 3 MONTH) &gt;= #{req.endTime,jdbcType=TIMESTAMP}))))
                </when>
                <when test="req.patientStatus == 2">AND wor.type = 17 AND
                    DATE_ADD(wor.prognosisTime, INTERVAL 3 MONTH) &lt;= #{req.endTime,jdbcType=TIMESTAMP}</when>
            </choose>
        </if>
        <if test="req.centerDiadialyzeAge != null">
            <if test="req.centerDiadialyzeAge == 1">
                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &lt;=
                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3
                MONTH) &lt;= #{req.endTime,jdbcType=TIMESTAMP}))
            </if>
            <if test="req.centerDiadialyzeAge == 2">
                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &gt;
                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3
                MONTH) &gt; #{req.endTime,jdbcType=TIMESTAMP}))
            </if>
        </if>
    </sql>

<!--    除去转出3个月的人-->
    <sql id="wherePatientConditionByStatus1">
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="req.maxAge != null ">AND wp.age &lt; #{req.maxAge,jdbcType=INTEGER}</if>
        <if test="req.minAge != null">AND wp.age &gt; #{req.minAge,jdbcType=INTEGER}</if>
        <if test="req.patientStatus != null">
            <choose>
                <when test="req.patientStatus == 1">AND (wp.patient_type_source = '00'
                    AND ((wp.receive_time &lt;= #{req.endTime,jdbcType=TIMESTAMP} AND wor.worId IS NULL) or (wor.worId
                    IS NOT NULL
                    AND wor.type != 17)))
                </when>
                <when test="req.patientStatus == 2">AND wor.type = 17</when>
            </choose>
        </if>
        <if test="req.centerDiadialyzeAge != null">
            <if test="req.centerDiadialyzeAge == 1">
                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &lt;=
                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3
                MONTH) &lt;= #{req.endTime,jdbcType=TIMESTAMP}))
            </if>
            <if test="req.centerDiadialyzeAge == 2">
                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &gt;
                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3
                MONTH) &gt; #{req.endTime,jdbcType=TIMESTAMP}))
            </if>
        </if>
    </sql>
    <!--    除去转出3个月的人-->
    <sql id="wherePatientConditionByStatus2">
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
<!--        <if test="req.maxAge != null ">AND wp.age &lt; #{req.maxAge,jdbcType=INTEGER}</if>-->
<!--        <if test="req.minAge != null">AND wp.age &gt; #{req.minAge,jdbcType=INTEGER}</if>-->
        <if test="req.patientStatus != null">
            <choose>
                <when test="req.patientStatus == 1">AND (wp.patient_type_source = '00'
                    AND ((wp.receive_time &lt;= #{req.endTime,jdbcType=TIMESTAMP} AND wor.worId IS NULL) or (wor.worId
                    IS NOT NULL
                    AND wor.type != 17)))
                </when>
                <when test="req.patientStatus == 2">AND wor.type = 17</when>
            </choose>
        </if>
<!--        <if test="req.centerDiadialyzeAge != null">-->
<!--            <if test="req.centerDiadialyzeAge == 1">-->
<!--                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &lt;=-->
<!--                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3-->
<!--                MONTH) &lt;= #{req.endTime,jdbcType=TIMESTAMP}))-->
<!--            </if>-->
<!--            <if test="req.centerDiadialyzeAge == 2">-->
<!--                AND ((wor.worId IS NULL AND DATE_ADD(wp.receive_time, INTERVAL 3 MONTH) &gt;-->
<!--                #{req.endTime,jdbcType=TIMESTAMP}) OR (wor.worId IS NOT NULL AND DATE_ADD(wor.prognosisTime, INTERVAL 3-->
<!--                MONTH) &gt; #{req.endTime,jdbcType=TIMESTAMP}))-->
<!--            </if>-->
<!--        </if>-->
    </sql>

    <select id="getInfectionCompletionRateNew" resultType="java.lang.Long">
        SELECT
            count(*) as count
        FROM
        wl_patient wp
        <if test="req.completeStatus != null">
            <choose>
                <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
                <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
            </choose>
            JOIN (

                SELECT
                MAX(CASE WHEN json_key = 171 THEN json_value END) AS json_value1,
                MAX(CASE WHEN json_key = 172 THEN json_value END) AS json_value2,
                MAX(CASE WHEN json_key = 173 THEN json_value END) AS json_value3,
                MAX(CASE WHEN json_key = 174 THEN json_value END) AS json_value4,
                max( info.id ) AS id,
                info.create_time,
                info.project_id,
                pro.patient_id
                FROM wl_renal_project_info info
                LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                WHERE info.deleted = 0 AND pro.deleted = 0
                    <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                    <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                    <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                    AND info.json_key IN (171, 172, 173, 174)
                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY info.create_time, pro.patient_id
            ) wrp ON wrp.patient_id = wp.id
        </if>
        RIGHT JOIN (
            SELECT wor.patient_id, wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason
            FROM wl_outcome_record wor
            JOIN (
                SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <if test="req.type != null and req.type != ''">AND wor.type = #{req.type}</if>
                GROUP BY wor.patient_id
            ) temp ON temp.id = wor.id
        ) wor ON wor.patient_id = wp.id
        WHERE
        wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        <if test="req.completeStatus != null and req.completeStatus == 1">
            AND wrp.project_id IS NOT NULL
            AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value1 != '未查'
            AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value2 != '未查'
            AND wrp.json_value3 IS NOT NULL AND wrp.json_value3 != '' AND wrp.json_value3 != '未查'
            AND wrp.json_value4 IS NOT NULL AND wrp.json_value4 != '' AND wrp.json_value4 != '未查'
        </if>
        <if test="req.completeStatus != null and req.completeStatus == 2">
            AND wrp.project_id IS NULL
            OR (
                wrp.project_id IS NOT NULL
                AND (
                    wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value1 = '未查'
                    OR wrp.json_value2 IS NULL OR wrp.json_value2 = '' OR wrp.json_value2 = '未查'
                    OR wrp.json_value3 IS NULL OR wrp.json_value3 = '' OR wrp.json_value3 = '未查'
                    OR wrp.json_value4 IS NULL OR wrp.json_value4 = '' OR wrp.json_value4 = '未查'
                )
            )
        </if>
        <if test="req.firstReceiveTimeEnd != null">
            AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getInfectionCompletionRate" resultType="java.lang.Long">
        SELECT
        count(*) AS count
        FROM wl_patient wp
        <choose>
            <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
            <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
        </choose>
        <if test="req.completeStatus != null">
            JOIN (
            SELECT wrp.id AS projectId, wrp.patient_id, info.project_id<if test="req.chartType != null and req.chartType == 5">,info.json_key AS jsonKey, info.json_value AS jsonValue</if>
            FROM wl_renal_project wrp
            RIGHT JOIN (
            SELECT info.id, info.project_id
            FROM  wl_renal_project_info info
            JOIN (
            SELECT
            max( info.id ) id, pro.patient_id
            FROM
            wl_renal_project_info info
            LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
            LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
            WHERE  info.deleted = 0
            AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}
            <if test="req.projectName != null and req.projectName != ''">
                AND sd.label = #{req.projectName,jdbcType=VARCHAR}
            </if>
            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY pro.patient_id
            ) temp ON temp.id = info.id
            ) info ON wrp.id = info.project_id
            WHERE wrp.deleted = 0
            ) wrp ON wrp.patient_id = wp.id
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <where>
            <choose>
                <when test="req.chartType == 2">
                    wp.deleted = 0
                    AND wp.patient_type = '1'
                    AND
                    (
                        wp.patient_type_source = '00'
                        <choose>
                            <when test="req.endTime != null">AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &gt; 3</when>
                            <otherwise>AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, NOW()) &gt; 3</otherwise>
                        </choose>
                        AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &gt; 3
                    )
                    <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                </when>
                <otherwise>
                    wp.deleted = 0
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                </otherwise>
            </choose>
            <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL</if>
            <if test="req.completeStatus != null and req.completeStatus == 2">AND wrp.project_id IS NULL</if>
            <if test="req.chartType != null and req.chartType == 1 and req.startTime != null and req.endTime != null">
                AND wp.receive_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="req.firstReceiveTimeEnd != null">
                AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="req.infect != null and req.infect != ''">AND find_in_set(#{req.infect,jdbcType=VARCHAR}, wp.infect)</if>
        </where>
    </select>

<!--    <select id="getInfectionCompletionRate" resultType="java.lang.Long">-->
<!--        SELECT-->
<!--            count(*) AS count-->
<!--        FROM wl_patient wp-->
<!--        <choose>-->
<!--            <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>-->
<!--            <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>-->
<!--        </choose>-->
<!--        <if test="req.completeStatus != null">-->
<!--        JOIN (-->
<!--            SELECT wrp.id AS projectId, wrp.patient_id, info.project_id<if test="req.chartType != null and req.chartType == 5">,info.json_key AS jsonKey, info.json_value AS jsonValue</if>-->
<!--            FROM wl_renal_project wrp-->
<!--            RIGHT JOIN (-->
<!--                SELECT info.id, info.project_id-->
<!--                FROM  wl_renal_project_info info-->
<!--                JOIN (-->
<!--                    SELECT-->
<!--                        max( info.id ) id, pro.patient_id-->
<!--                    FROM-->
<!--                        wl_renal_project_info info-->
<!--                    LEFT JOIN wl_renal_project pro ON info.project_id = pro.id-->
<!--                    LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )-->
<!--                    WHERE  info.deleted = 0-->
<!--                    AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}-->
<!--                <if test="req.projectName != null and req.projectName != ''">-->
<!--                    AND sd.label = #{req.projectName,jdbcType=VARCHAR}-->
<!--                </if>-->
<!--                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>-->
<!--                    GROUP BY pro.patient_id-->
<!--                ) temp ON temp.id = info.id-->
<!--            ) info ON wrp.id = info.project_id-->
<!--            WHERE wrp.deleted = 0-->
<!--        ) wrp ON wrp.patient_id = wp.id-->
<!--        </if>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>-->
<!--        <where>-->
<!--            wp.deleted = 0-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>-->
<!--            <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL</if>-->
<!--            <if test="req.completeStatus != null and req.completeStatus == 2">AND wrp.project_id IS NULL</if>-->
<!--            <if test="req.chartType != null and req.chartType == 1 and req.startTime != null and req.endTime != null">-->
<!--                AND wp.receive_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}-->
<!--            </if>-->
<!--            <if test="req.firstReceiveTimeEnd != null">-->
<!--                AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}-->
<!--            </if>-->
<!--            <if test="req.infect != null and req.infect != ''">AND find_in_set(#{req.infect,jdbcType=VARCHAR}, wp.infect)</if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="getInfectionCompletionRatePageNew" resultType="java.util.Map">
        SELECT
        <include refid="selectDataSort"/>
        <include refid="selectPatientInfo"/>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
        <if test="req.completeStatus != null">
            wrp.*,
            CASE
                WHEN
                    wrp.project_id IS NOT NULL
                    AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value1 != '未查'
                    AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value2 != '未查'
                    AND wrp.json_value3 IS NOT NULL AND wrp.json_value3 != '' AND wrp.json_value3 != '未查'
                    AND wrp.json_value4 IS NOT NULL AND wrp.json_value4 != '' AND wrp.json_value4 != '未查'
                THEN '已完成'
                WHEN
                    wrp.project_id IS NULL
                    OR (
                        wrp.project_id IS NOT NULL
                        AND (
                            wrp.json_value1 IS NULL OR wrp.json_value1 = '' AND wrp.json_value1 = '未查'
                            OR wrp.json_value2 IS NULL OR wrp.json_value2 = '' AND wrp.json_value2 = '未查'
                            OR wrp.json_value3 IS NULL OR wrp.json_value3 = '' AND wrp.json_value3 = '未查'
                            OR wrp.json_value4 IS NULL OR wrp.json_value4 = '' AND wrp.json_value4 = '未查'
                        )
                    )
                THEN '未完成'
                ELSE '未完成'
            END AS completeStatus,
        </if>
        <if test="(req.infect != null and req.infect != '')"> wp.infect,</if>
        wp.patient_type AS patientType
        FROM wl_patient wp
        <if test="req.completeStatus != null">
            <choose>
                <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
                <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
                <otherwise>LEFT</otherwise>
            </choose>
            JOIN (
                SELECT
                    MAX(CASE WHEN json_key = 171 THEN json_value END) AS json_value1,
                    MAX(CASE WHEN json_key = 172 THEN json_value END) AS json_value2,
                    MAX(CASE WHEN json_key = 173 THEN json_value END) AS json_value3,
                    MAX(CASE WHEN json_key = 174 THEN json_value END) AS json_value4,
                    max( info.id ) AS id,
                    info.create_time,
                    info.project_id,
                    pro.patient_id
                FROM wl_renal_project_info info
                LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                WHERE info.deleted = 0 AND pro.deleted = 0
                    <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                    <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                    <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                    AND info.json_key IN (171, 172, 173, 174)
                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY info.create_time, pro.patient_id
            ) wrp ON wrp.patient_id = wp.id
        </if>
        RIGHT JOIN (
            SELECT wor.patient_id, wor.prognosis_time AS prognosisTime, wor.type AS type, wor.classify AS classify, wor.reason AS reason
            FROM wl_outcome_record wor
            JOIN (
                SELECT max(wor.id) as id  FROM wl_outcome_record wor WHERE wor.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND wor.prognosis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <if test="req.type != null and req.type != ''">AND wor.type = #{req.type}</if>
                GROUP BY wor.patient_id
            ) temp ON temp.id = wor.id
        ) wor1 ON wor1.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
            <if test="req.completeStatus != null and req.completeStatus == 1">
                AND wrp.project_id IS NOT NULL
                AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value1 != '未查'
                AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value2 != '未查'
                AND wrp.json_value3 IS NOT NULL AND wrp.json_value3 != '' AND wrp.json_value3 != '未查'
                AND wrp.json_value4 IS NOT NULL AND wrp.json_value4 != '' AND wrp.json_value4 != '未查'
            </if>
            <if test="req.completeStatus != null and req.completeStatus == 2">
                AND wrp.project_id IS NULL
                OR (
                    wrp.project_id IS NOT NULL
                    AND (
                        wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value1 = '未查'
                        OR wrp.json_value2 IS NULL OR wrp.json_value2 = '' OR wrp.json_value2 = '未查'
                        OR wrp.json_value3 IS NULL OR wrp.json_value3 = '' OR wrp.json_value3 = '未查'
                        OR wrp.json_value4 IS NULL OR wrp.json_value4 = '' OR wrp.json_value4 = '未查'
                    )
                )
            </if>
            <if test="req.infect != null and req.infect != ''">AND find_in_set(#{req.infect,jdbcType=VARCHAR}, wp.infect)</if>
        </where>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>

    <select id="getInfectionCompletionRatePage" resultType="java.util.Map">
        SELECT
        <include refid="selectDataSort"/>
        <include refid="selectPatientInfo"/>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
        <if test="req.completeStatus != null">
            wrp.*,
            CASE
            WHEN wrp.projectId IS NOT NULL THEN '已完成'
            WHEN wrp.projectId IS NULL THEN '未完成'
            ELSE ''
            END AS completeStatus,
        </if>
        <if test="(req.infect != null and req.infect != '')"> wp.infect,</if>
        <if test="req.chartType != null and req.chartType != ''">
            <choose>
                <when test="req.chartType == 4"> wp.infect,</when>
                <when test="req.chartType == 5"> wrp.label AS labels, wrp.json_value AS jsonValues,wrp.prop AS props, DATE_FORMAT(wrp.check_time , '%Y-%m-%d') AS checkTime,</when>
            </choose>
        </if>
        wp.patient_type AS patientType
        FROM wl_patient wp
        <if test="req.completeStatus != null or req.chartType != null and req.chartType == 5">
            <choose>
                <when test="req.completeStatus == 1">RIGHT</when>
                <when test="req.completeStatus == 2">LEFT</when>
                <otherwise>LEFT</otherwise>
            </choose>
            JOIN (
            SELECT wrp.id AS projectId, wrp.patient_id, info.project_id<if test="req.chartType != null and req.chartType == 5">,info.label, info.prop, info.json_value, wrp.last_check_time AS check_time</if>
            FROM wl_renal_project wrp
            RIGHT JOIN (
            SELECT ANY_VALUE(info.id) AS id, ANY_VALUE(info.project_id) AS project_id <if test="req.chartType != null and req.chartType == 5">,GROUP_CONCAT(item.label) label,GROUP_CONCAT(item.prop) prop,GROUP_CONCAT(info.json_value) json_value</if>
            FROM wl_renal_project_info info
            LEFT JOIN wl_renal_project_item item ON info.json_key = item.id
            JOIN (
            SELECT
            max( info.id ) id, pro.patient_id
            FROM
            wl_renal_project_info info
            LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
            LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
            WHERE  info.deleted = 0
            AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR} AND sd.label = #{req.projectName,jdbcType=VARCHAR}
            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY pro.patient_id, info.json_key
            ) temp ON temp.id = info.id
            GROUP BY temp.patient_id
            ) info ON wrp.id = info.project_id
            WHERE wrp.deleted = 0
            ) wrp ON wrp.patient_id = wp.id
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <choose>
                <when test="req.chartType == 2">
                    wp.deleted = 0
                    AND wp.patient_type = '1'
                    AND
                    (
                        wp.patient_type_source = '00'
                        <choose>
                            <when test="req.endTime != null">AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &gt; 3</when>
                            <otherwise>AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, NOW()) &gt; 3</otherwise>
                        </choose>
                        AND TIMESTAMPDIFF(MONTH, wp.first_receive_time, #{req.endTime,jdbcType=TIMESTAMP}) &gt; 3
                    )
                    <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                </when>
                <otherwise>
                    wp.deleted = 0
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                </otherwise>
            </choose>
            <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL</if>
            <if test="req.completeStatus != null and req.completeStatus == 2">AND wrp.project_id IS NULL</if>
            <if test="req.chartType != null and req.chartType == 1 and req.startTime != null and req.endTime != null">
                AND wp.receive_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="req.infect != null and req.infect != ''">AND find_in_set(#{req.infect,jdbcType=VARCHAR}, wp.infect)</if>
        </where>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>

<!--    <select id="getInfectionCompletionRatePage" resultType="java.util.Map">-->
<!--        SELECT-->
<!--        <include refid="selectDataSort"/>-->
<!--        <include refid="selectPatientInfo"/>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>-->
<!--        <if test="req.completeStatus != null">-->
<!--            wrp.*,-->
<!--            CASE-->
<!--            WHEN wrp.projectId IS NOT NULL THEN '已完成'-->
<!--            WHEN wrp.projectId IS NULL THEN '未完成'-->
<!--            ELSE ''-->
<!--            END AS completeStatus,-->
<!--        </if>-->
<!--        <if test="(req.infect != null and req.infect != '')"> wp.infect,</if>-->
<!--        <if test="req.chartType != null and req.chartType != ''">-->
<!--            <choose>-->
<!--                <when test="req.chartType == 4"> wp.infect,</when>-->
<!--                <when test="req.chartType == 5"> wrp.label AS labels, wrp.json_value AS jsonValues,wrp.prop AS props, DATE_FORMAT(wrp.check_time , '%Y-%m-%d') AS checkTime,</when>-->
<!--            </choose>-->
<!--        </if>-->
<!--        wp.patient_type AS patientType-->
<!--        FROM wl_patient wp-->
<!--        <if test="req.completeStatus != null or req.chartType != null and req.chartType == 5">-->
<!--            <choose>-->
<!--                <when test="req.completeStatus == 1">RIGHT</when>-->
<!--                <when test="req.completeStatus == 2">LEFT</when>-->
<!--                <otherwise>LEFT</otherwise>-->
<!--            </choose>-->
<!--        JOIN (-->
<!--            SELECT wrp.id AS projectId, wrp.patient_id, info.project_id<if test="req.chartType != null and req.chartType == 5">,info.label, info.prop, info.json_value, wrp.last_check_time AS check_time</if>-->
<!--            FROM wl_renal_project wrp-->
<!--            RIGHT JOIN (-->
<!--                SELECT ANY_VALUE(info.id) AS id, ANY_VALUE(info.project_id) AS project_id <if test="req.chartType != null and req.chartType == 5">,GROUP_CONCAT(item.label) label,GROUP_CONCAT(item.prop) prop,GROUP_CONCAT(info.json_value) json_value</if>-->
<!--                FROM wl_renal_project_info info-->
<!--                LEFT JOIN wl_renal_project_item item ON info.json_key = item.id-->
<!--                JOIN (-->
<!--                    SELECT-->
<!--                        max( info.id ) id, pro.patient_id-->
<!--                    FROM-->
<!--                        wl_renal_project_info info-->
<!--                    LEFT JOIN wl_renal_project pro ON info.project_id = pro.id-->
<!--                    LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )-->
<!--                    WHERE  info.deleted = 0-->
<!--                    AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR} AND sd.label = #{req.projectName,jdbcType=VARCHAR}-->
<!--                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>-->
<!--                    GROUP BY pro.patient_id, info.json_key-->
<!--                ) temp ON temp.id = info.id-->
<!--                GROUP BY temp.patient_id-->
<!--            ) info ON wrp.id = info.project_id-->
<!--            WHERE wrp.deleted = 0-->
<!--        ) wrp ON wrp.patient_id = wp.id-->
<!--        </if>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>-->
<!--        <where>-->
<!--            wp.deleted = 0-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>-->
<!--            <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL</if>-->
<!--            <if test="req.completeStatus != null and req.completeStatus == 2">AND wrp.project_id IS NULL</if>-->
<!--            <if test="req.chartType != null and req.chartType == 1 and req.startTime != null and req.endTime != null">-->
<!--                AND wp.receive_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}-->
<!--            </if>-->
<!--            <if test="req.infect != null and req.infect != ''">AND find_in_set(#{req.infect,jdbcType=VARCHAR}, wp.infect)</if>-->
<!--        </where>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>-->
<!--    </select>-->

    <select id="getQualityIndexCompletionRate" resultType="java.lang.Long">
        <if test="req.statisticType != null and req.statisticType == 1">
            SELECT
                count(*) as count
            FROM
                wl_patient wp
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <if test="req.completeStatus != null">
                <choose>
                    <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
                    <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
                </choose>
            JOIN (

                <choose>
                    <when test="req.hasMultipleJsonKey == 1">
                        SELECT
                        MAX(CASE WHEN json_key = 97 THEN json_value END) AS json_value1,
                        MAX(CASE WHEN json_key = 98 THEN json_value END) AS json_value2,
                        max( info.id ) AS id,
                        info.create_time,
                        info.project_id,
                        pro.patient_id
                        FROM wl_renal_project_info info
                        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                        WHERE info.deleted = 0 AND pro.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                            <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                            <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                            <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                            <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY pro.patient_id
                        order by info.id DESC
                    </when>
                    <otherwise>
                        SELECT
                            MAX(json_value) AS json_value,
                            max( info.id ) AS id,
                            info.create_time,
                            info.project_id,
                            pro.patient_id
                        FROM wl_renal_project_info info
                        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                        WHERE info.deleted = 0 AND pro.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                            <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                            <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                            <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                            <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY pro.patient_id
                        order by info.id DESC

<!--                        SELECT info.id, info.project_id,wrp.patient_id, info.json_value-->
<!--                        FROM wl_renal_project wrp-->
<!--                        RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id-->
<!--                        JOIN (-->
<!--                            SELECT-->
<!--                            max( info.id ) id, pro.patient_id-->
<!--                            FROM wl_renal_project_info info-->
<!--                            LEFT JOIN wl_renal_project pro ON info.project_id = pro.id-->
<!--                            LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )-->
<!--                            WHERE info.deleted = 0 AND pro.deleted = 0-->
<!--                                <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>-->
<!--                                <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>-->
<!--                                <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>-->
<!--                                <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>-->
<!--                                <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>-->
<!--                            GROUP BY pro.patient_id-->
<!--                        ) temp ON temp.id = info.id-->
<!--                        WHERE wrp.deleted = 0-->
                    </otherwise>
                </choose>
            ) wrp ON wrp.patient_id = wp.id
            </if>
            WHERE
                wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
            <choose>
                <when test="req.hasMultipleJsonKey == 1">

                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value1 not like '%NaN%' AND wrp.json_value2 not like '%NaN%' </if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR ( wrp.project_id IS NOT NULL AND (wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value2 IS NULL OR wrp.json_value2 = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND (wrp.json_value1 like '%NaN%' or wrp.json_value2 like '%NaN%')</if>
                </when>
                <otherwise>
                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value not like '%NaN%'</if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND (wrp.json_value IS NULL OR wrp.json_value = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value like '%NaN%'</if>
                </otherwise>
            </choose>
            <if test="req.firstReceiveTimeEnd != null">
                AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}
            </if>
        </if>

        <if test="req.statisticType != null and req.statisticType == 2">
            SELECT count(*) count
            FROM (
                SELECT AVG( a.json_value ) AS avgNum,  patient_id
                FROM wl_patient wp
                LEFT JOIN (
                    SELECT wrp.id, wrp.patient_id, info.json_value
                    FROM
                    `wl_renal_project` wrp
                    LEFT JOIN wl_patient wp ON wrp.patient_id = wp.id
                    JOIN wl_renal_project_info info ON info.project_id = wrp.id
                    <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
                    WHERE wp.deleted = 0 AND info.deleted = 0
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                    <if test="req.dictId != null">AND wrp.dict_id = #{req.dictId}</if>
                    <if test="req.itemId != null">AND info.json_key = #{req.itemId,jdbcType=BIGINT}</if>
                    <if test="req.startTime != null and req.endTime != null">AND info.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                ) a ON  a.patient_id = wp.id
                <where>
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                </where>
                GROUP BY wp.id
            ) b
            <where>
                <if test="req.range != null">
                    <if test="req.range == 1"><if test="req.standardValueMax != null">AND b.avgNum &gt; #{req.standardValueMax,jdbcType=DOUBLE}</if></if>
                    <if test="req.range == 2">
                        <if test="req.standardValueMax != null">AND b.avgNum &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
                        <if test="req.standardValueMin != null">AND b.avgNum &gt; #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    </if>
                    <if test="req.range == 3"> <if test="req.standardValueMin != null">AND b.avgNum &lt; #{req.standardValueMin,jdbcType=DOUBLE}</if></if>
                    <if test="req.range == 4"> AND b.avgNum IS NULL</if>
                </if>
            </where>
        </if>
    </select>

    <select id="getQualityIndexCompletionStatisticsRate" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
            SELECT
            count(*) as count, wp.dept_id as centerId
            FROM
            wl_patient wp
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <if test="req.completeStatus != null">
                <choose>
                    <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
                    <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
                </choose>
                JOIN (
                <choose>
                    <when test="req.hasMultipleJsonKey == 1">
                        SELECT
                        MAX(CASE WHEN json_key = 97 THEN json_value END) AS json_value1,
                        MAX(CASE WHEN json_key = 98 THEN json_value END) AS json_value2,
                        max( info.id ) AS id,
                        info.create_time,
                        info.project_id,
                        pro.patient_id
                        FROM wl_renal_project_info info
                        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                        WHERE info.deleted = 0 AND pro.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                        <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                        <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                        <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                        <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                        <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY pro.patient_id
                        order by info.id DESC
                    </when>
                    <otherwise>
                        SELECT
                        MAX(json_value) AS json_value,
                        max( info.id ) AS id,
                        info.create_time,
                        info.project_id,
                        pro.patient_id
                        FROM wl_renal_project_info info
                        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                        WHERE info.deleted = 0 AND pro.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                        <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                        <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                        <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                        <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                        <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY pro.patient_id
                        order by info.id DESC
                    </otherwise>
                </choose>
                ) wrp ON wrp.patient_id = wp.id
            </if>
            WHERE
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
            <choose>
                <when test="req.hasMultipleJsonKey == 1">

                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value1 not like '%NaN%' AND wrp.json_value2 not like '%NaN%' </if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR ( wrp.project_id IS NOT NULL AND (wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value2 IS NULL OR wrp.json_value2 = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND (wrp.json_value1 like '%NaN%' or wrp.json_value2 like '%NaN%')</if>
                </when>
                <otherwise>
                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value not like '%NaN%'</if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND (wrp.json_value IS NULL OR wrp.json_value = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value like '%NaN%'</if>
                </otherwise>
            </choose>
            <if test="req.firstReceiveTimeEnd != null">
                AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}
            </if>
            group by centerId
    </select>

    <select id="getCompletionRatePage" resultType="java.util.Map">
        SELECT
            <include refid="selectDataSort"/>
            <include refid="selectPatientInfo"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
            <if test="req.chartType == 4">wp.infect,</if>
            wrp.*,

            <choose>
                <when test="req.hasMultipleJsonKey == 1">
                    CASE
                        WHEN wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value1 not like '%NaN%' AND wrp.json_value2 not like '%NaN%' THEN '已完成'
                        WHEN wrp.project_id IS NULL OR ( wrp.project_id IS NOT NULL AND (wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value2 IS NULL OR wrp.json_value2 = '')) THEN '未完成'
                        WHEN wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND (wrp.json_value1 like '%NaN%' or wrp.json_value2 like '%NaN%') THEN '特殊'
                        ELSE '未完成'
                    END AS completeStatus
                </when>
                <otherwise>
                    CASE
                        WHEN wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' THEN '已完成'
                        WHEN wrp.project_id IS NULL THEN '未完成'
                        ELSE '未完成'
                    END AS completeStatus
                </otherwise>
            </choose>

        FROM wl_patient wp
        <if test="req.chartType == 2">
        LEFT JOIN (
            SELECT info.id,info.project_id,info.project_info,temp.patient_id
            FROM wl_renal_project_info info
            JOIN (
                SELECT  max( info.id ) id,  pro.patient_id
                FROM wl_renal_project_info info
                LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                WHERE info.deleted = 0
                AND info.json_value is not null AND info.json_value != ''
                <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY pro.patient_id
            ) temp ON temp.id = info.id
        ) wrp1 ON wrp1.patient_id = wp.id
        </if>
        <if test="req.completeStatus != null">
            <choose>
                <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
                <when test="req.completeStatus != null and req.completeStatus == 2">LEFT</when>
                <when test="req.chartType == 2">LEFT</when>
                <otherwise>LEFT</otherwise>
            </choose>
        JOIN (

            <choose>
                <when test="req.hasMultipleJsonKey == 1">
                    SELECT
                    MAX(CASE WHEN json_key = 97 THEN json_value END) AS json_value1,
                    MAX(CASE WHEN json_key = 98 THEN json_value END) AS json_value2,
                    max( info.id ) AS id,
                    info.create_time,
                    info.project_id,
                    pro.patient_id
                    FROM wl_renal_project_info info
                    LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                    LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                    WHERE info.deleted = 0 AND pro.deleted = 0
                    AND info.json_value is not null AND info.json_value != ''
                        <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                        <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                        <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                        <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                        <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>

                    GROUP BY pro.patient_id
                    order by info.id DESC
                </when>
                <otherwise>
                    SELECT info.id, info.project_id,temp.patient_id, info.json_value
                    <if test="req.chartType != null and req.chartType == 2">, CAST(JSON_EXTRACT(info.project_info,'$.${req.ktvKey}') AS SIGNED) AS ktv, CAST(JSON_EXTRACT(info.project_info,'$.${req.urrKey}') AS SIGNED) AS urr</if>
                    FROM  wl_renal_project_info info
                    JOIN (

                        SELECT
                        max( info.id ) id, pro.patient_id
                        FROM wl_renal_project_info info
                        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
                        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
                        WHERE info.deleted = 0 AND pro.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                            <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                            <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                            <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                            <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY pro.patient_id
                    ) temp ON temp.id = info.id
                </otherwise>
            </choose>

        ) wrp ON wrp.patient_id = wp.id
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            wp.deleted = 0
            <if test="req.patientStatus == null">AND wp.patient_type_source = '00'</if>
            <include refid="wherePatientConditionByStatus"/>

            <choose>
                <when test="req.hasMultipleJsonKey == 1">
                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND wrp.json_value1 not like '%NaN%' AND wrp.json_value2 not like '%NaN%' </if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR ( wrp.project_id IS NOT NULL AND (wrp.json_value1 IS NULL OR wrp.json_value1 = '' OR wrp.json_value2 IS NULL OR wrp.json_value2 = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value1 IS NOT NULL AND wrp.json_value1 != '' AND wrp.json_value2 IS NOT NULL AND wrp.json_value2 != '' AND (wrp.json_value1 like '%NaN%' or wrp.json_value2 like '%NaN%')</if>
                </when>
                <otherwise>
                    <if test="req.completeStatus != null and req.completeStatus == 1">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value not like '%NaN%'</if>
                    <if test="req.completeStatus != null and req.completeStatus == 2">AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND (wrp.json_value IS NULL OR wrp.json_value = '')))</if>
                    <if test="req.completeStatus != null and req.completeStatus == 3">AND wrp.project_id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' AND wrp.json_value like '%NaN%'</if>
                </otherwise>
            </choose>

            <if test="req.reachStandard != null and req.reachStandard == 1">
                AND wrp.ktv IS NOT NULL<if test="req.searchType != null and req.searchType == 1"> AND </if><if test="req.searchType != null and req.searchType == 2"> OR </if>wrp.urr IS NOT NULL
            </if>
            <if test="req.reachStandard != null and req.reachStandard == 2">
                AND wrp.ktv IS NULL<if test="req.searchType != null and req.searchType == 1"> AND </if><if test="req.searchType != null and req.searchType == 2"> OR </if>wrp.urr IS NULL
            </if>
        </where>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>

    <select id="getKTVURRControlStatisticsRateCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        SELECT
        count(*) AS count,wp.dept_id as centerId
        FROM
        wl_patient wp
        <choose>
            <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
            <when test="req.completeStatus == null and req.completeStatus == 2">LEFT</when>
            <otherwise>LEFT</otherwise>
        </choose>
        JOIN (
        SELECT
        MAX(CASE WHEN json_key = 97 THEN json_value END) AS urrValue,
        MAX(CASE WHEN json_key = 98 THEN json_value END) AS ktvValue,
        max( info.id ) AS id,
        info.create_time,
        info.project_id,
        pro.patient_id
        FROM wl_renal_project_info info
        LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
        LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
        WHERE info.deleted = 0 AND pro.deleted = 0
        AND info.json_value is not null AND info.json_value != ''
        <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
        <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
        <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
        <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY pro.patient_id
        order by info.id DESC
        ) wrp ON wrp.patient_id = wp.id

        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <where>
            wp.deleted = 0
            <include refid="wherePatientConditionByStatus"/>

            <if test="req.completeStatus == 1">
                AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
                AND (
                (wrp.ktvValue &gt; #{req.ktvMinValue,jdbcType=INTEGER}
                <if test="req.ktvMaxValue != null">AND wrp.ktvValue &lt; #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
                <if test="req.searchType == 1 or req.searchType == null">AND</if>
                <if test="req.searchType == 2">OR</if>
                (wrp.urrValue &gt; #{req.urrMinValue,jdbcType=INTEGER}
                <if test="req.urrMaxValue != null">AND wrp.urrValue &lt; #{req.urrMaxValue,jdbcType=INTEGER}</if>)
                ))
            </if>

            <if test="req.completeStatus == 2">
                AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
                AND (
                (wrp.ktvValue &lt;= #{req.ktvMinValue,jdbcType=INTEGER}
                <if test="req.ktvMaxValue != null">AND wrp.ktvValue &gt;= #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
                <if test="req.searchType == 2 or req.searchType == null">AND</if>
                <if test="req.searchType == 1">OR</if>
                (wrp.urrValue &lt;= #{req.urrMinValue,jdbcType=INTEGER}
                <if test="req.urrMaxValue != null">AND wrp.urrValue &lt;= #{req.urrMaxValue,jdbcType=INTEGER}</if>)
                ))

            </if>

            <if test="req.completeStatus == null">
                AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND ( wrp.ktvValue IS NULL OR wrp.ktvValue = '' OR wrp.urrValue IS NULL OR wrp.urrValue = '' )))
            </if>

            <if test="req.completeStatus == 3">
                AND wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '' AND  wrp.urrValue IS NOT NULL AND wrp.urrValue != ''
                AND (wrp.ktvValue like '%NaN%' or wrp.urrValue like '%NaN%')
            </if>
        </where>
        group by centerId
    </select>

    <select id="getKTVURRControlRateCount" resultType="java.lang.Long">
        SELECT
            count(*) AS count
        FROM
            wl_patient wp
        <choose>
            <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
            <when test="req.completeStatus == null and req.completeStatus == 2">LEFT</when>
            <otherwise>LEFT</otherwise>
        </choose>
        JOIN (
            SELECT
            MAX(CASE WHEN json_key = 97 THEN json_value END) AS urrValue,
            MAX(CASE WHEN json_key = 98 THEN json_value END) AS ktvValue,
            max( info.id ) AS id,
            info.create_time,
            info.project_id,
            pro.patient_id
            FROM wl_renal_project_info info
            LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
            LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
            WHERE info.deleted = 0 AND pro.deleted = 0
            AND info.json_value is not null AND info.json_value != ''
                <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY pro.patient_id
            order by info.id DESC
        ) wrp ON wrp.patient_id = wp.id

        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <where>
        wp.deleted = 0
        <include refid="wherePatientConditionByStatus"/>

         <if test="req.completeStatus == 1">
             AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
             AND (
                 (wrp.ktvValue &gt; #{req.ktvMinValue,jdbcType=INTEGER}
                 <if test="req.ktvMaxValue != null">AND wrp.ktvValue &lt; #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
                 <if test="req.searchType == 1 or req.searchType == null">AND</if>
                 <if test="req.searchType == 2">OR</if>
                 (wrp.urrValue &gt; #{req.urrMinValue,jdbcType=INTEGER}
                 <if test="req.urrMaxValue != null">AND wrp.urrValue &lt; #{req.urrMaxValue,jdbcType=INTEGER}</if>)
             ))
         </if>

        <if test="req.completeStatus == 2">
            AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
            AND (
                (wrp.ktvValue &lt;= #{req.ktvMinValue,jdbcType=INTEGER}
                <if test="req.ktvMaxValue != null">AND wrp.ktvValue &gt;= #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
                <if test="req.searchType == 2 or req.searchType == null">AND</if>
                <if test="req.searchType == 1">OR</if>
                (wrp.urrValue &lt;= #{req.urrMinValue,jdbcType=INTEGER}
                <if test="req.urrMaxValue != null">AND wrp.urrValue &lt;= #{req.urrMaxValue,jdbcType=INTEGER}</if>)
            ))

        </if>

        <if test="req.completeStatus == null">
            AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND ( wrp.ktvValue IS NULL OR wrp.ktvValue = '' OR wrp.urrValue IS NULL OR wrp.urrValue = '' )))
        </if>

        <if test="req.completeStatus == 3">
            AND wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '' AND  wrp.urrValue IS NOT NULL AND wrp.urrValue != ''
            AND (wrp.ktvValue like '%NaN%' or wrp.urrValue like '%NaN%')
        </if>
        </where>
    </select>

    <select id="getKTVURRControlRatePage" resultType="java.util.Map">
        SELECT
            <include refid="selectDataSort"/>
            <include refid="selectPatientInfo"/>
            CASE
                WHEN #{req.completeStatus} = 1 THEN '达标'
                WHEN #{req.completeStatus} = 2 THEN '未达标'
                WHEN #{req.completeStatus} = 3 THEN '特殊'
                ELSE '未查'
            END AS completeStatus,
            wrp.ktvValue, wrp.urrValue
        FROM
            wl_patient wp
        <choose>
            <when test="req.completeStatus != null and req.completeStatus == 1">RIGHT</when>
            <when test="req.completeStatus == null and req.completeStatus == 2">LEFT</when>
            <otherwise>LEFT</otherwise>
        </choose>
        JOIN (
            SELECT
            MAX(CASE WHEN json_key = 97 THEN json_value END) AS urrValue,
            MAX(CASE WHEN json_key = 98 THEN json_value END) AS ktvValue,
            max( info.id ) AS id,
            info.create_time,
            info.project_id,
            pro.patient_id
            FROM wl_renal_project_info info
            LEFT JOIN wl_renal_project pro ON info.project_id = pro.id
            LEFT JOIN system_dict_data sd ON ( sd.id = pro.dict_id )
            WHERE info.deleted = 0 AND pro.deleted = 0
            AND info.json_value is not null AND info.json_value != ''
                <if test="req.projectType != null and req.projectType != ''">AND sd.dict_type = #{req.projectType,jdbcType=VARCHAR}</if>
                <if test="req.projectName != null and req.projectName != ''">AND sd.label = #{req.projectName,jdbcType=VARCHAR}</if>
                <if test="req.dictId != null">AND pro.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
                <if test="req.jsonKeys != null">AND info.json_key IN <foreach collection="req.jsonKeys" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY pro.patient_id
            order by info.id DESC
        ) wrp ON wrp.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        WHERE wp.deleted = 0
        <include refid="wherePatientConditionByStatus"/>


        <if test="req.completeStatus == 1">
            AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
            AND (
            (wrp.ktvValue &gt; #{req.ktvMinValue,jdbcType=INTEGER}
            <if test="req.ktvMaxValue != null">AND wrp.ktvValue &lt; #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
            <if test="req.searchType == 1 or req.searchType == null">AND</if>
            <if test="req.searchType == 2">OR</if>
            (wrp.urrValue &gt; #{req.urrMinValue,jdbcType=INTEGER}
            <if test="req.urrMaxValue != null">AND wrp.urrValue &lt; #{req.urrMaxValue,jdbcType=INTEGER}</if>)
            ))
        </if>

        <if test="req.completeStatus == 2">
            AND ((( wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '') AND ( wrp.urrValue IS NOT NULL AND wrp.urrValue != '' ) AND wrp.ktvValue not like '%NaN%' AND wrp.urrValue not like '%NaN%')
            AND (
            (wrp.ktvValue &lt;= #{req.ktvMinValue,jdbcType=INTEGER}
            <if test="req.ktvMaxValue != null">AND wrp.ktvValue &gt;= #{req.ktvMaxValue,jdbcType=INTEGER}</if>)
            <if test="req.searchType == 2 or req.searchType == null">AND</if>
            <if test="req.searchType == 1">OR</if>
            (wrp.urrValue &lt;= #{req.urrMinValue,jdbcType=INTEGER}
            <if test="req.urrMaxValue != null">AND wrp.urrValue &lt;= #{req.urrMaxValue,jdbcType=INTEGER}</if>)
            ))

        </if>

        <if test="req.completeStatus == null">
            AND (wrp.project_id IS NULL OR (wrp.project_id IS NOT NULL AND ( wrp.ktvValue IS NULL OR wrp.ktvValue = '' OR wrp.urrValue IS NULL OR wrp.urrValue = '' )))
        </if>

        <if test="req.completeStatus == 3">
            AND wrp.ktvValue IS NOT NULL AND wrp.ktvValue != '' AND  wrp.urrValue IS NOT NULL AND wrp.urrValue != ''
            AND (wrp.ktvValue like '%NaN%' or wrp.urrValue like '%NaN%')
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>

    <select id="getTotalPatientAnalysisInfoTotal" resultType="java.lang.Long">
        SELECT count(*) as count
        FROM wl_patient wp
<choose>
    <when test="req.analysisType != null">
        LEFT JOIN (
            SELECT DISTINCT ac.patient_id
            FROM wl_arrange_classes ac
            WHERE ac.deleted = 0
            <if test="req.startTime != null and req.endTime != null">AND ac.classes_time BETWEEN  #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        ) temp ON temp.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinAggregatePatientStatus"/>
    </when>
    <otherwise>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinAggregatePatientStatus"/>
    </otherwise>
</choose>
        WHERE wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <if test="req.patientIdList != null and req.patientIdList.size()>0">AND wp.id NOT IN <foreach collection="req.patientIdList" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <if test="req.chartType == 5 and  req.firstReceiveTimeStart == null and req.firstReceiveTimeEnd == null">AND wp.first_receive_time IS NULL </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>

    <select id="getTotalPatientAnalysisInfoPage" resultType="java.util.Map">
        SELECT <include refid="selectDataSort"/> temp.* FROM (
            SELECT
                <include refid="selectPatientInfo"/> DATE(wp.first_receive_time) AS firstReceiveTime, wp.patient_source AS patientSource,
                wor.type AS type, wor.classify AS classify, wor.reason AS reason, DATE(wor.prognosisTime) AS prognosisTime
            FROM wl_patient wp
            <choose>
                <when test="req.analysisType != null">
                    LEFT JOIN (
                        SELECT DISTINCT ac.patient_id
                        FROM wl_arrange_classes ac
                        WHERE ac.deleted = 0
                        <if test="req.startTime != null and req.endTime != null">AND ac.classes_time BETWEEN  #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    ) temp ON temp.patient_id = wp.id
                    <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinAggregatePatientStatus"/>
                </when>
                <otherwise>
                    <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinAggregatePatientStatus"/>
                </otherwise>
            </choose>
            WHERE wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.patientIdList != null and req.patientIdList.size()>0">AND wp.id NOT IN <foreach collection="req.patientIdList" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
            <if test="req.firstReceiveTimeStart != null">AND wp.first_receive_time &gt; #{req.firstReceiveTimeStart,jdbcType=TIMESTAMP}</if>
            <if test="req.firstReceiveTimeEnd != null">AND wp.first_receive_time &lt; #{req.firstReceiveTimeEnd,jdbcType=TIMESTAMP}</if>
            <if test="req.chartType == 4 and req.maxAge == null and req.minAge == null">AND wp.age IS NULL</if>
            <if test="req.patientName != null and req.patientName != ''">AND wp.`name` like concat('%', #{req.patientName,jdbcType=VARCHAR}, '%')</if>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
        ) temp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        order by temp.dialyzeNo
    </select>

    <select id="getPatientInfoJoinReason" resultType="java.util.Map">
        SELECT
        <include refid="selectPatientInfo"/>
        GROUP_CONCAT(wr.name) AS describes,
        wp.infect
        FROM wl_patient wp
        LEFT JOIN wl_disease_reason dr ON wp.id = dr.patient_id
        LEFT JOIN wl_reason wr ON dr.parent_two_id = wr.id
        WHERE 1=1
        <if test="patientId != null">AND wp.id = #{patientId}</if>
    </select>

    <select id="selectTransferoutPatients" resultType="com.thj.boot.module.business.pojo.patient.vo.PatientRespVO">
        SELECT <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                wp.patient_type AS patientType, wp.id
        FROM wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <where>
            <include refid="wherePatientCondition"/>
        </where>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>
    <select id="selectAgeTotal" resultType="java.util.Map">
        select SUM(CASE `name` WHEN '0-17' THEN value ELSE 0 END) as '0-17',
        SUM(CASE `name` WHEN '18-45' THEN value ELSE 0 END) as '18-45',
        SUM(CASE `name` WHEN '46-65' THEN value ELSE 0 END) as '46-65',
        SUM(CASE `name` WHEN '66-79' THEN value ELSE 0 END) as '66-79' ,
        SUM(CASE `name` WHEN '>=80' THEN value ELSE 0 END) as '>=80',
        SUM(CASE `name` WHEN 'total' THEN value ELSE 0 END) as 'total'
        from (
        select '0-17' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
         AND patient_type_source = '00' and DELETEd = '0' and age <![CDATA[ >= ]]>  0 and age <![CDATA[ <= ]]>  17
        union ALL
        select '18-45' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and  classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
         AND patient_type_source = '00' and DELETEd = '0' and age <![CDATA[ >= ]]>  18 and age <![CDATA[ <= ]]>  45
        union ALL
        select '46-65' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
         AND patient_type_source = '00' and DELETEd = '0' and age <![CDATA[ >= ]]>  46 and age <![CDATA[ <= ]]>  65
        union ALL
        select '66-79' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and  classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach> AND patient_type_source = '00' and DELETEd = '0' and age <![CDATA[ >= ]]>  66 and age <![CDATA[ <= ]]>  79
        union ALL
        select '>=80' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and  classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach> AND patient_type_source = '00' and DELETEd = '0' and age <![CDATA[ >= ]]>  80
        union ALL
        select 'total' as name ,count(*) as value from
        wl_patient where id in (
        select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and  classes_time BETWEEN #{startTime} AND #{endTime}
        ) and  dept_id IN
        <foreach collection="deptIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach> AND patient_type_source = '00' and DELETEd = '0') a
    </select>
    <select id="selectPatientFlags" resultType="com.thj.boot.module.business.pojo.patient.vo.PatientFlags">
        SELECT
        p.id AS patient_id,
        MAX(CASE WHEN wl.name LIKE '%带隧道带涤纶套%' THEN 1 ELSE 0 END) AS tunnel_cuffed_flag,
        MAX(CASE WHEN wl.name LIKE '%无涤纶套%' THEN 1 ELSE 0 END) AS no_cuff_flag,
        MAX(CASE WHEN re.parent_three_name LIKE '%糖尿病%' THEN 1 ELSE 0 END) AS diabetes_flag,
        MAX(CASE WHEN op.drug_type_id = 62 THEN 1 ELSE 0 END) AS no_heparin_flag
        FROM wl_patient p
        left join wl_blood_road r
        on p.id = r.patient_id
        left join wl_vascular_access wl on wl.id in(r.type,r.part) and (wl.name like '%无涤纶套%' or wl.name like  '%带隧道带涤纶套%')
        LEFT JOIN wl_disease_reason re ON p.id = re.patient_id and re.parent_three_name LIKE '%糖尿病%'
        LEFT JOIN wl_contradict op ON p.id = op.patient_id and op.drug_type_id = 62 and date_format(hemodialysis_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
        where 1 = 1
           <if test='patientIds != null and patientIds.size() > 0'>
               and p.id IN
               <foreach item='id' collection='patientIds' open='(' separator=',' close=')'>
                       #{id.id}
               </foreach>
           </if>
        GROUP BY p.id
    </select>

    <update id="updateAgeById" parameterType="com.thj.boot.module.business.dal.datado.patient.PatientDO">
        update wl_patient set age = #{age},dialyze_age = #{dialyzeAge} where id = #{id}
    </update>
    <update id="updatePatientType">
        update wl_jk_implementation w join wl_patient p
        on w.patient_id = p.id
            set w.dialyze_total = p.dialyze_total,
                w.patient_type = 1
        where  p.deleted = 0 and w.deleted = 0
          and p.dialyze_total >= 5
          and w.patient_type = 0
    </update>
</mapper>

