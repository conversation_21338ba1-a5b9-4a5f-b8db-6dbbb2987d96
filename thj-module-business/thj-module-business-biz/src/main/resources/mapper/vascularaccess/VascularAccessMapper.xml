<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper">

    <update id="updateDeptChildren" parameterType="java.util.List">
        update wl_vascular_access set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
