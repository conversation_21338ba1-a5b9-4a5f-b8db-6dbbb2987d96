<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper">
    <sql id="selectTotal">
        SELECT COUNT(*) AS total
        FROM `wl_blood_road` br
        LEFT JOIN wl_patient wp ON br.patient_id = wp.id
        RIGHT JOIN (
            SELECT wdr.* FROM wl_dialysis_record wdr
            JOIN (
                SELECT max(wdr2.id) as id  FROM wl_dialysis_record wdr2
                WHERE  wdr2.deleted = 0
                <if test="req.startTime != null and req.endTime != null">
                    AND wdr2.date_week BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
                </if>
                GROUP BY wdr2.patient_id
            ) temp ON temp.id = wdr.id
        ) wdr ON wdr.patient_id = wp.id
        WHERE  br.deleted = 0 AND wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
    </sql>
    <select id="getInternalFistulaSurvivalRate" resultType="java.lang.Long">
        SELECT COUNT(*) AS total
        FROM wl_patient wp
        JOIN (
            SELECT whm.id, whm.patient_id, whm.vascular_access_one, whm.vascular_access_two, whm.hemodialysis_time FROM `wl_hemodialysis_manager` whm
            JOIN (
                SELECT MAX(id) AS maxId FROM `wl_hemodialysis_manager` whm
                WHERE whm.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY whm.patient_id
            ) temp ON maxId = whm.id
        ) whm ON whm.patient_id = wp.id
        LEFT JOIN wl_blood_road br ON br.`patient_id` = wp.`id`
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        WHERE
            wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <if test="req.maxDate!= null">AND br.init_time &lt;= #{req.maxDate}</if>
        <if test="req.minDate != null">AND br.init_time &gt;= #{req.minDate}</if>
    </select>


    <select id="getVascularAccessPage" resultType="java.util.Map">
    SELECT
        DISTINCT(wp.id) AS patientId, wp.`name` AS patientName, wp.dialyze_no AS dialyzeNo, wp.sex, wp.dialyze_age AS dialysisAge, wp.age, wp.medic, wp.nurse,
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <!-- <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/> -->
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
        whm.vascular_access_two as `type`, whm.vascular_access_one AS vascularAccessOne,  wva.`name` AS vascularAccessName, DATE(whm.hemodialysis_time) AS hemodialysisTime
    FROM  wl_patient wp
    JOIN (
        SELECT whm.id, whm.patient_id, whm.vascular_access_one, whm.vascular_access_two, whm.hemodialysis_time FROM `wl_hemodialysis_manager` whm
        JOIN (
            SELECT MAX(id) AS maxId FROM `wl_hemodialysis_manager` whm
            WHERE whm.deleted = 0
            AND whm.vascular_access_two IS NOT NULL
            <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            GROUP BY whm.patient_id
        ) temp ON maxId = whm.id
    ) whm ON whm.patient_id = wp.id
    LEFT JOIN wl_vascular_access wva ON wva.deleted = 0 AND whm.vascular_access_two = wva.id
    <!-- <if test="req.initTime != null">
        LEFT JOIN wl_blood_road br ON br.`patient_id` = wp.`id`
    </if> -->
    <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
    <where>
        wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <!-- <if test="req.initTime != null">
            <if test="req.maxDate != null">AND br.init_time &lt; #{req.maxDate,jdbcType=TIMESTAMP}</if>
            <if test="req.minDate != null">AND br.init_time &gt;= #{req.minDate,jdbcType=TIMESTAMP}</if>
        </if> -->
        <if test="req.roadType != null">AND wva.access_type = #{req.roadType,jdbcType=INTEGER}</if>
        </where>
        ORDER BY whm.hemodialysis_time DESC
    </select>

    <select id="getBloodChannelTypeStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) AS total,
            CAST(wva.access_type AS SIGNED) AS accessType
        FROM
            wl_patient wp
                -- 1. 关联最新血路类型记录（已去重）
                LEFT JOIN (
                SELECT
                    patient_id,
                    type,
                    ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY id DESC) AS rn
                FROM
                    wl_blood_road
                WHERE
                    deleted = 0
                  AND status = 0
            ) wbr ON wp.id = wbr.patient_id AND wbr.rn = 1
                -- 2. 关联血管通路类型表
                LEFT JOIN wl_vascular_access wva
                          ON wbr.type = wva.id
                              AND wva.deleted = 0
                -- 3. 关联最新排班记录（去重后）
                LEFT JOIN (
                SELECT
                    patient_id,
                    classes_time,
                    ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY classes_time DESC) AS rn
                FROM
                    wl_arrange_classes
                WHERE
                    deleted = 0 AND temp_type = 0
                <if test="req.startTime != null and req.endTime != null">AND classes_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            ) wac ON wp.id = wac.patient_id AND wac.rn = 1
        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.roadType != null">AND wva.access_type = #{req.roadType,jdbcType=INTEGER}</if>
            <if test="req.accessState != null">AND wva.access_state = #{req.accessState,jdbcType=INTEGER}</if>
        </where>
        GROUP BY wva.access_type
    </select>


    <select id="getChannelTypeStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) AS total, CAST( wva.access_type AS SIGNED) AS accessType
        FROM  wl_patient wp
        RIGHT JOIN (
            SELECT whm.id, whm.patient_id, whm.vascular_access_two FROM `wl_hemodialysis_manager` whm
            JOIN (
                SELECT MAX(id) AS maxId FROM `wl_hemodialysis_manager` whm
                WHERE whm.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                AND vascular_access_two IS NOT NULL
                GROUP BY whm.patient_id
            ) temp ON maxId = whm.id
        ) whm ON whm.patient_id = wp.id
        LEFT JOIN wl_vascular_access wva ON wva.deleted = 0 AND whm.vascular_access_two = wva.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.roadType != null">AND wva.access_type = #{req.roadType,jdbcType=INTEGER}</if>
            <if test="req.accessState != null">AND wva.access_state = #{req.accessState,jdbcType=INTEGER}</if>
        </where>
        GROUP BY wva.access_type
    </select>

    <select id="getList" resultType="com.thj.boot.module.business.controller.admin.dialysismanager.vo.BloodPassageComplicationRespVO">
        select a.patient_id as patientId,a.patient_name as patientName,a.dialyze_no as dialyzeNo,a.parent_one_name as occurType,a.parent_two_name as symptom,a.parent_three_name, b.type
        from wl_disease_reason a
                 LEFT JOIN  wl_blood_road b on a.patient_id = b.patient_id
        where a.diagnostic_type = 1 and a.reason_time = #{format}  GROUP BY a.patient_id;
    </select>

    <select id="getRepairRegister" resultType="com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO">
        select a.fault_code,a.fault_description as faultDescription,a.cause,a.processing as processing,b.facility_code as faultCode,
               c.`name` as faciltyName,a.troubleshooting,a.repair_time as repairTime,a.phase
        from wl_repair_register  a
            LEFT JOIN wl_facility_manager b on a.manager_id = b.id
            LEFT JOIN wl_facility_name c on b.facility_name_id = c.id
        where a.repair_time =  #{format}
    </select>
    <select id="getVascularAccessPage1" resultType="java.util.Map">
select   <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> a.*from
(select distinct wp.id patientId, wp.`name` AS patientName, wp.dialyze_no AS dialyzeNo, wp.sex,
        wp.dialyze_age AS dialysisAge, wp.age, wp.medic, wp.nurse,
        whm.vascular_access_two as `type`, whm.vascular_access_one AS vascularAccessOne, wva.`name` AS
        vascularAccessName, DATE(whm.hemodialysis_time) AS hemodialysisTime,

        wva.access_Type
        from (
        SELECT wp.*
        FROM wl_patient wp
        LEFT JOIN (
        SELECT
        info.id,
        wrp.patient_id,
        info.json_value
        FROM wl_renal_project wrp
        JOIN (
        SELECT
        *,
        ROW_NUMBER() OVER(PARTITION BY project_id ORDER BY id DESC) rn
        FROM wl_renal_project_info
        WHERE deleted = 0
        AND json_value != ''
        <if test="req.startTime != null and req.endTime != null">
            AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        ) info ON wrp.id = info.project_id AND info.rn = 1
        WHERE 1 = 1
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        ) wrp ON wp.id = wrp.patient_id

        <include
                refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus1"/>
        WHERE wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus2"/>
        ) wp
        JOIN (
        SELECT whm.id, whm.patient_id, whm.vascular_access_one, whm.vascular_access_two, whm.hemodialysis_time,
        row_number() over (partition by patient_id order by id desc) rn FROM
        `wl_hemodialysis_manager` whm
        where whm.deleted = 0
        AND whm.vascular_access_two IS NOT NULL
        <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{
            req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        ) whm ON whm.patient_id = wp.id and whm.rn = 1
        -- 1. 关联最新血路类型记录（已去重）
        LEFT JOIN (
        SELECT
        patient_id,
        type,
        ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY id DESC) AS rn
        FROM
        wl_blood_road
        WHERE
        deleted = 0
        AND status = 0
        ) wbr ON wp.id = wbr.patient_id AND wbr.rn = 1
        -- 2. 关联血管通路类型表
        LEFT JOIN wl_vascular_access wva
        ON wbr.type = wva.id
        AND wva.deleted = 0

        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.roadType != null">AND wva.access_type = #{req.roadType,jdbcType=INTEGER}</if>

        </where>) a    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>

    </select>
    <select id="getVascularAccessPage2" resultType="java.util.Map">
        SELECT
        COUNT(1) AS total,
        CAST(a.access_type AS SIGNED) AS accessType
        FROM (
        select
        distinct wp.id,wva.access_Type
        from (
        SELECT wp.*
        FROM wl_patient wp
        LEFT JOIN (
        SELECT
        info.id,
        wrp.patient_id,
        info.json_value
        FROM wl_renal_project wrp
        JOIN (
        SELECT
        *,
        ROW_NUMBER() OVER(PARTITION BY project_id ORDER BY id DESC) rn
        FROM wl_renal_project_info
        WHERE deleted = 0
        AND json_value != ''
        <if test="req.startTime != null and req.endTime != null">
            AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        ) info ON wrp.id = info.project_id AND info.rn = 1
        WHERE 1 = 1
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        ) wrp ON wp.id = wrp.patient_id

        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus1"/>
        WHERE wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
                <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus2"/>
        ) wp
        JOIN (
        SELECT whm.id, whm.patient_id, whm.vascular_access_one, whm.vascular_access_two, whm.hemodialysis_time,
               row_number() over (partition by patient_id order by id desc) rn FROM
        `wl_hemodialysis_manager` whm
            where whm.deleted = 0
        AND whm.vascular_access_two IS NOT NULL
        <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{
            req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        ) whm ON whm.patient_id = wp.id and whm.rn = 1
        -- 1. 关联最新血路类型记录（已去重）
        LEFT JOIN (
        SELECT
        patient_id,
        type,
        ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY id DESC) AS rn
        FROM
        wl_blood_road
        WHERE
        deleted = 0
        AND status = 0
        ) wbr ON wp.id = wbr.patient_id AND wbr.rn = 1
        -- 2. 关联血管通路类型表
        LEFT JOIN wl_vascular_access wva
        ON wbr.type = wva.id
        AND wva.deleted = 0
        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.roadType != null">AND wva.access_type = #{req.roadType,jdbcType=INTEGER}</if>

        </where>) a
        GROUP BY a.access_type
    </select>
</mapper>
