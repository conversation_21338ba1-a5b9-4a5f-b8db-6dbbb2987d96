<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper">

    <sql id="joinAfterPatientWeight">
        <if test="req.afterPatientWeight != null">
            LEFT JOIN (
                SELECT whm.patient_id, whm.weighing_method AS weighingMethod,
                    ( CAST(IFNULL(whm.dialyze_after_weigh, 0) AS DECIMAL) - CAST(IFNULL(whm.dry_weight, 0) AS DECIMAL) ) AS growWeight
                FROM wl_hemodialysis_manager whm
                JOIN (
                    SELECT MAX(id) AS maxId FROM wl_hemodialysis_manager
                   <where>
                       <if test="req.startTime != null and req.endTime != null">hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                   </where>
                    GROUP BY patient_id
                ) temp on temp.maxId = whm.id
            ) whm ON wp.id = whm.patient_id
        </if>
    </sql>

    <sql id="selectAfterPatientWeight">
        <if test="req.afterPatientWeight != null">,whm.weighingMethod, whm.growWeight</if>
    </sql>

    <sql id="joinDryWeight">
        <if test="req.patientWeight != null">
        JOIN (
            SELECT max( id ) AS id FROM wl_dry_weight wdw
            WHERE patient_id IS NOT NULL AND deleted = '0'
            <if test="req.startTime != null and req.endTime != null">
                AND create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            GROUP BY patient_id
        ) temp ON wdw.id = temp.id
        </if>
    </sql>

    <sql id="selectDryWeight">
        <if test="req.patientWeight != null">,wdw.dry_weight</if>
    </sql>

    <select id="getWeightAnalysisCount" resultType="java.lang.Long">
        SELECT
            COUNT(*) AS count
        FROM
            `wl_dry_weight` wdw
        <include refid="com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper.joinDryWeight"/>
        LEFT JOIN wl_patient wp ON wdw.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        WHERE
            wdw.deleted = 0 AND wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <if test="req.state != null">AND wdw.state is not null</if>
        <if test="req.maxValue != null">AND wdw.dry_weight &lt;= #{req.maxValue,jdbcType=DECIMAL}</if>
        <if test="req.minValue != null">AND wdw.dry_weight &gt;= #{req.minValue,jdbcType=DECIMAL}</if>
    </select>
    <select id="getWeightAnalysisPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
            wdw.patient_id,
            wdw.id,
            wdw.create_time,
            wdw.dry_weight AS dryWeight
        FROM
            `wl_dry_weight` wdw
        <include refid="com.thj.boot.module.business.dal.mapper.dryweight.DryWeightMapper.joinDryWeight"/>
        LEFT JOIN wl_patient wp ON wdw.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <if test="req.isPage != null and req.isPage == 1"><include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/></if>
        WHERE
            wdw.deleted = 0 AND wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <if test="req.startTime != null and req.endTime != null"> AND wdw.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>
        <if test="req.state != null">AND wdw.state is not null</if>
        <if test="req.maxValue != null">AND wdw.dry_weight &lt;= #{req.maxValue,jdbcType=DECIMAL}</if>
        <if test="req.minValue != null">AND wdw.dry_weight &gt;= #{req.minValue,jdbcType=DECIMAL}</if>
        ORDER BY wdw.create_time DESC

    </select>

    <select id="getAfterWeightAnalysisPage" resultType="java.util.Map">
        SELECT <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
            temp.*,
            cast((temp.result / temp.dryWeight * 100) AS DECIMAL ( 10, 0 )) AS percentage  <!--体重增加-->
        FROM (
            SELECT
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
                whm.after_state,
                CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS  dryWeight,<!--干体重-->
                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeBeforeWeight,<!--透前体重-->
                CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) ) AS beforeDialyzeAfterWeight, <!--前次透析后体重-->
                CAST( whm.dialyze_after_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeAfterWeight, <!--透后体重-->
                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) result,
                whm.create_time AS createTime,
                DATE_FORMAT(whm.create_time , '%Y-%m-%d') AS checkTime,
                ( CAST(whm.dialyze_after_weight AS DECIMAL( 10, 2 )) - CAST(whm.dry_weight AS DECIMAL( 10, 2 )) ) AS growWeight,
                whm.weighing_method AS weighingMethod
                FROM wl_patient wp
                LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
                <where>
                    whm.after_state = 1 AND wp.deleted = '0'
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
                    <if test="req.startTime != null">AND whm.hemodialysis_time &gt; #{req.startTime,jdbcType=TIMESTAMP}</if>
                    <if test="req.endTime != null">AND whm.hemodialysis_time &lt; #{req.endTime,jdbcType=TIMESTAMP}</if>
                </where>
            ) temp
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
            <where>
                1=1
                <if test="req.otherGrowth != null">
                    <choose>
                        <when test="req.otherGrowth == 1">
                            AND temp.result  &lt;= 0 OR temp.result IS NULL
                        </when>
                        <otherwise>
                            AND temp.result >= 0
                            <if test="req.maxGrowth != null">AND temp.result &lt;= #{req.maxGrowth,jdbcType=DOUBLE} </if>
                            <if test="req.minGrowth != null">AND temp.result &gt;= #{req.minGrowth,jdbcType=DOUBLE}</if>
                        </otherwise>
                    </choose>
                </if>
                <if test="req.afterPatientWeight != null">
                    <if test="req.afterPatientWeight == 1">AND temp.growWeight &gt; 3 </if>
                    <if test="req.afterPatientWeight == 2">AND temp.growWeight &lt;= 3 </if>
                    <if test="req.afterPatientWeight == 3">AND temp.weighingMethod = 2</if>
                </if>
            </where>
        ORDER BY temp.createTime DESC
    </select>

    <select id="getWeightAnalysisIncreasePage2" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> temp.*
        FROM (
            SELECT
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
                <include refid="selectAfterPatientWeight"/>
                whm.after_state,
                CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS  dryWeight,<!--干体重-->
                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeBeforeWeight,<!--透前体重-->
                CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) ) AS beforeDialyzeAfterWeight, <!--前次透析后体重-->
                CAST( whm.dialyze_after_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeAfterWeight, <!--透后体重-->
                ( CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) )) / whm.dry_weight * 100 AS result, <!--体重增加-->
                whm.create_time AS createTime,
                DATE_FORMAT(whm.create_time , '%Y-%m-%d') AS checkTime,
                ( CAST(whm.dialyze_after_weigh AS DECIMAL) - CAST(whm.dry_weight AS DECIMAL) ) AS growWeight,
                whm.weighing_method AS weighingMethod
            <include refid="selectDryWeight"/>
            FROM `wl_patient` wp
            LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            <include refid="joinAfterPatientWeight"/>
            <include refid="joinDryWeight"/>
        <where>
            whm.deleted = '0' AND wp.deleted = '0'
            <if test="req.startTime != null and req.endTime != null"> AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>
        </where>
        ) temp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            1=1
            <if test="req.otherGrowth != null ">
                <choose>
                    <when test="req.otherGrowth == 1">
                        AND temp.result  &lt;= 0
                    </when>
                    <otherwise>
                        AND temp.result  &gt;= 0
                        <if test="req.maxGrowth != null">AND temp.result &lt;= #{req.maxGrowth,jdbcType=DOUBLE} </if>
                        <if test="req.minGrowth != null">AND temp.result &gt;= #{req.minGrowth,jdbcType=DOUBLE}</if>
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY temp.createTime DESC
    </select>


    <select id="getWeightGainAnalysisCount" resultType="java.lang.Long">
        SELECT COUNT(*) AS count
        FROM (
            SELECT
                wp.id,
                <if test="req.chartType == 2">
                ( CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) )) / whm.before_dialyze_after_weigh * 100 AS result
                </if>
                <if test="req.chartType == 3">
                ( CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) )) / whm.dry_weight * 100 AS result
                </if>
            FROM `wl_patient` wp
            LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            <where>
                wp.deleted = '0'
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
                <if test="req.startTime != null and req.endTime != null"> AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>
            </where>
        ) temp

        <where>
            1=1
            <if test="req.otherGrowth != null ">
                <choose>
                    <when test="req.otherGrowth == 1">
                        AND temp.result  &lt;= 0
                    </when>
                    <otherwise>
                        AND temp.result  &gt;= 0
                        <if test="req.maxGrowth != null">AND temp.result &lt;= #{req.maxGrowth,jdbcType=DOUBLE} </if>
                        <if test="req.minGrowth != null">AND temp.result &gt;= #{req.minGrowth,jdbcType=DOUBLE}</if>
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="getWeightAnalysisIncreasePage1" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> temp.*, cast(result AS DECIMAL ( 10, 0 )) percentage
        FROM (
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
        CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS  dryWeight,
        CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeBeforeWeight,
        CAST( whm.dialyze_after_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeAfterWeight,
        CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) ) AS beforeDialyzeAfterWeight, <!--前次透析后体重-->
        whm.create_time AS createTime,
        DATE_FORMAT(whm.create_time , '%Y-%m-%d') AS checkTime,
        CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS growWeight,
        CASE
            WHEN #{req.chartType} = 2 THEN
                CASE
                WHEN whm.before_dialyze_after_weigh IS NULL OR whm.before_dialyze_after_weigh = '/' OR whm.before_dialyze_after_weigh = 0 THEN NULL
                ELSE ROUND(CAST((CAST(whm.dialyze_before_weight AS DECIMAL(10, 2)) - CAST(whm.before_dialyze_after_weigh AS DECIMAL(10, 2))) * 100 AS DECIMAL(10, 2)) / whm.before_dialyze_after_weigh, 2)
            END
            WHEN #{req.chartType} = 3 THEN
                CASE
                WHEN whm.dry_weight IS NULL OR whm.dry_weight = '/' OR whm.dry_weight = 0 THEN NULL
                ELSE ROUND(CAST((CAST(whm.dialyze_before_weight AS DECIMAL(10, 2)) - CAST(whm.dry_weight AS DECIMAL(10, 2))) * 100 AS DECIMAL(10, 2)) / whm.dry_weight, 2)
            END
        ELSE NULL
        END AS result
        FROM `wl_patient` wp
        LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <where>
            wp.deleted = '0'
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
            <if test="req.startTime != null and req.endTime != null"> AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>
        </where>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
        ) temp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            1=1
            <if test="req.groupWeight != 0">
                <if test="req.maxGrowth != null">
                    <if test="req.minGrowth == null">AND temp.result &lt;= #{req.maxGrowth,jdbcType=DOUBLE}</if>
                    <if test="req.minGrowth != null">AND (temp.result BETWEEN #{req.minGrowth,jdbcType=DOUBLE} AND #{req.maxGrowth,jdbcType=DOUBLE}) AND temp.result != #{req.minGrowth,jdbcType=DOUBLE} AND temp.result != #{req.maxGrowth,jdbcType=DOUBLE}</if>
                </if>
                <if test="req.maxGrowth == null">
                    <if test="req.minGrowth != null">AND temp.result &gt;= #{req.minGrowth,jdbcType=DOUBLE}</if>
                    <if test="req.minGrowth == null">AND temp.result IS null</if>
                </if>
            </if>

            <if test="req.patientWeight != null">
                <choose>
                    <when test="req.patientWeight == 1">
                        AND temp.dryWeight IS NULL
                    </when>
                    <when test="req.patientWeight == 2">
                        AND temp.dryWeight &lt; 40
                    </when>
                    <when test="req.patientWeight == 3">
                        AND temp.dryWeight BETWEEN 40 AND 50
                    </when>
                    <when test="req.patientWeight == 4">
                        AND temp.dryWeight BETWEEN 50 AND 60
                    </when>
                    <when test="req.patientWeight == 5">
                        AND temp.dryWeight BETWEEN 60 AND 70
                    </when>
                    <when test="req.patientWeight == 6">
                        AND temp.dryWeight &gt; 70
                    </when>
                </choose>
            </if>

            <if test="req.afterPatientWeight != null || req.afterPatientWeight != 0 ">
                <choose>
                    <when test="req.afterPatientWeight == 1">
                        AND temp.dialyzeAfterWeight &lt; temp.dryWeight
                    </when>
                    <when test="req.afterPatientWeight == 2">
                        AND temp.dialyzeAfterWeight &gt;= temp.dryWeight
                    </when>
                    <when test="req.afterPatientWeight == 3">
                    </when>
                    <when test="req.afterPatientWeight == 4">
                        AND temp.dialyzeAfterWeight &lt; temp.dryWeight OR temp.dialyzeAfterWeight IS NULL
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY temp.createTime DESC
    </select>


    <!--    <select id="getWeightAnalysisIncreasePage1" resultType="java.util.Map">-->
<!--        SELECT-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> temp.*, cast(result AS DECIMAL ( 10, 0 )) percentage-->
<!--        FROM (-->
<!--            SELECT-->
<!--                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>-->
<!--                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>-->
<!--                CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS  dryWeight,-->
<!--                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeBeforeWeight,-->
<!--                CAST( whm.dialyze_after_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeAfterWeight,-->
<!--                CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) ) AS beforeDialyzeAfterWeight, &lt;!&ndash;前次透析后体重&ndash;&gt;-->
<!--                whm.create_time AS createTime,-->
<!--                DATE_FORMAT(whm.create_time , '%Y-%m-%d') AS checkTime,-->
<!--                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS growWeight,-->
<!--                <if test="req.chartType == 2">-->
<!--                    ( CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) )) / whm.before_dialyze_after_weigh * 100 AS result-->
<!--                </if>-->
<!--                <if test="req.chartType == 3">-->
<!--                    ( CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) )) / whm.dry_weight * 100 AS result-->
<!--                </if>-->
<!--                <include refid="selectAfterPatientWeight"/>-->
<!--                <include refid="selectDryWeight"/>-->
<!--            FROM `wl_patient` wp-->
<!--            LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id-->
<!--            <include refid="joinAfterPatientWeight"/>-->
<!--            <include refid="joinDryWeight"/>-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>-->
<!--        <where>-->
<!--            wp.deleted = '0'-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>-->
<!--            <if test="req.startTime != null and req.endTime != null"> AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>-->
<!--        </where>-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>-->
<!--        ) temp-->
<!--        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>-->
<!--        <where>-->
<!--            1=1-->
<!--            <if test="req.otherGrowth != null">-->
<!--                <choose>-->
<!--                    <when test="req.otherGrowth == 1">-->
<!--                        AND temp.result  &lt;= 0-->
<!--                    </when>-->
<!--                    <otherwise>-->
<!--                        AND temp.result >= 0-->
<!--                        <if test="req.maxGrowth != null">AND temp.result &lt;= #{req.maxGrowth,jdbcType=DOUBLE} </if>-->
<!--                        <if test="req.minGrowth != null">AND temp.result &gt;= #{req.minGrowth,jdbcType=DOUBLE}</if>-->
<!--                    </otherwise>-->
<!--                </choose>-->
<!--            </if>-->
<!--            <if test="req.afterPatientWeight != null">-->
<!--                <if test="req.afterPatientWeight == 1">AND temp.growWeight &gt; 3 </if>-->
<!--                <if test="req.afterPatientWeight == 2">AND temp.growWeight &lt;= 3 </if>-->
<!--                <if test="req.afterPatientWeight == 3">AND temp.weighingMethod = 2</if>-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY temp.createTime DESC-->
<!--    </select>-->

    <select id="getAfterWeightAnalysisCount" resultType="java.lang.Long">
        SELECT COUNT(*) AS count
        FROM (
            SELECT wp.id, whm.after_state,
            ( CAST(whm.dialyze_after_weight AS DECIMAL) - CAST(whm.dry_weight AS DECIMAL) ) AS growWeight,
            whm.weighing_method AS weighingMethod
            FROM wl_patient wp
            LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <where>
            whm.after_state = 1 AND wp.deleted = '0'
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.startTime != null">AND whm.hemodialysis_time &gt; #{req.startTime,jdbcType=TIMESTAMP}</if>
            <if test="req.endTime != null">AND whm.hemodialysis_time &lt; #{req.endTime,jdbcType=TIMESTAMP}</if>
        </where>
        ) temp
        <where>
            1=1
            <if test="req.afterPatientWeight != null">
                <if test="req.afterPatientWeight == 1">AND temp.growWeight &gt; 3 </if>
                <if test="req.afterPatientWeight == 2">AND temp.growWeight &lt;= 3 </if>
                <if test="req.afterPatientWeight == 3">AND temp.weighingMethod = 2</if>
            </if>
        </where>
    </select>
</mapper>
