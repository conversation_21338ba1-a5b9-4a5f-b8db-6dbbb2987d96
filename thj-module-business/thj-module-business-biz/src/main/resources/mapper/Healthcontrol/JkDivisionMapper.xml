<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.jkdivision.JkDivisionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryByList" resultType="com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionCreateReqVO">
        select wp.id,wp.dialyze_no,wp.dialyze_total,wp.`name`,b.dialyze_name,b.date_week,c.nurse_name,b.facility_subarea_id,d.name
            from wl_patient wp
            left join wl_team_patient b on wp.id = b.patient_id
        LEFT JOIN wl_jk_division c on b.patient_id= c.patient_id
        left join wl_facility_subarea d on b.facility_subarea_id = d.id
        where 1=1 and wp.deleted = 0
        <if test="patientState != null">
            and wp.patient_state = #{patientState}
        </if>
        <if test="dateWeek != null">
            and b.date_week = #{dateWeek}
        </if>
        <if test="facilitySubareaId != null">
            and b.facility_subarea_id = #{facilitySubareaId}
        </if>
        <if test="patientState != null">
            and wp.patient_state = 1
        </if>
        <if test="patientName != null">
            and wp.name = #{patientName}
        </if>
    </select>

    <select id="queryByNewList" resultType="com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionCreateReqVO">
        select wp.id,wp.dialyze_no,wp.dialyze_total,wp.`name`,c.nurse_name
            from wl_patient wp
        LEFT JOIN wl_jk_division c on wp.id= c.patient_id
        where wp.patient_state = 1
        <if test="patientName != null">
            and wp.name = #{patientName}
        </if>
    </select>

    <select id="queryNewCount" resultType="int">
        SELECT count(*)
        FROM wl_jk_education
        where 1 = 1
          and patient_id = #{patientId}
    </select>


    <select id="queryNurse" resultType="com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionRespVO">
        select id,nickname as name from system_users
        where 1=1 and role_id_str like '%107%' or role_id_str like '%108%'
        <if test="name != null and name != ''">
            and nickname like concat("%",#{name},"%")
        </if>
            and dept_ids like concat("%",#{deptId},"%")
    </select>

    <select id="queryPatient" resultType="com.thj.boot.module.business.pojo.jkdivision.vo.PatientDivisionVo">

        select id as id, patient_name as name,patient_id as patientId
        from wl_jk_division
        where nurse_id = #{nurseId}

    </select>


    <select id="queryEstimate" resultType="com.thj.boot.module.business.controller.admin.gk.vo.EstimateVo">
        SELECT wp.name, wp.id as patientId,wp.dialyze_no, b.*, c.nurse_name
        FROM wl_patient wp
        LEFT JOIN (SELECT MAX(id) AS estimateId, patient_id, create_time
        FROM wl_jk_estimate
        GROUP BY patient_id
        ORDER BY MAX(id) DESC) b ON wp.id = b.patient_id
        LEFT JOIN wl_jk_division c on wp.id = c.patient_id
        where 1=1
        <if test="state != null">
            and wp.state = #{state}
        </if>
        <if test="nurseId != null">
            and c.nurse_id = #{nurseId}
        </if>
        <if test="eduState != null">
            and b.state = #{eduState}
        </if>
        <if test="keyword !=null">
            and (wp.name like concat("%", #{keyword}, "%")
            or wp.spell_name like concat("%", #{keyword}, "%")
            or wp.dialyze_no = concat("%", #{keyword}, "%")
        </if>
    </select>

    <select id="queryUserInfo" resultType="java.util.Map">
        select * from system_users where id = #{userId}
    </select>


</mapper>
