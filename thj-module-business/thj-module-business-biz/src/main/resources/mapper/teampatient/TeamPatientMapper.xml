<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.teampatient.TeamPatientMapper">



    <select id="queryByList" resultType="com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO">
        select c.* from wl_team_patient a
            LEFT JOIN wl_facility b on a.facility_id = b.id
            LEFT JOIN wl_facility_manager c on b.`code` = c.facility_code
        where 1=1
        <if test="createTime != null">
            and a.weeks = #{createTime}
        </if>
        <if test="timeSlot != null">
            and a.week_day = #{timeSlot}
        </if>
        <if test="ward != null">
            and a.facility_subarea_id = #{ward}
        </if>
    </select>

</mapper>
