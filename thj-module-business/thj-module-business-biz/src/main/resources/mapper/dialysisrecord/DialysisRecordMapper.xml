<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.dialysisrecord.DialysisRecordMapper">
    <select id="getLastByAll" resultType="com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO">
        select * from wl_dialysis_record where id in (select max(id) from wl_dialysis_record where patient_id in
        <foreach item="item" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0 group by patient_id)
    </select>
</mapper>
