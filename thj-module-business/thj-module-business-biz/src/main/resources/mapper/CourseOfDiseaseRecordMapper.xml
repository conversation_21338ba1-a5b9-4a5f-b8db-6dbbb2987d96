<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.CourseOfDiseaseRecordMapper">

    <resultMap id="BaseResultMap" type="com.thj.boot.module.business.dal.datado.CourseOfDiseaseRecord">
            <id property="id" column="id" />
            <result property="patientId" column="patient_id" />
            <result property="deptId" column="dept_id" />
            <result property="admissionDate" column="admission_date" />
            <result property="recordTime" column="record_time" />
            <result property="chiefComplaint" column="chief_complaint" />
            <result property="presentIllness" column="present_illness" />
            <result property="ecg" column="ecg" />
            <result property="preliminaryDiagnosis" column="preliminary_diagnosis" />
            <result property="caseFeatures" column="case_features" />
            <result property="diagnosticBasis" column="diagnostic_basis" />
            <result property="differentialDiagnosis" column="differential_diagnosis" />
            <result property="treatmentPlan" column="treatment_plan" />
            <result property="attendingPhysician" column="attending_physician" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,patient_id,dept_id,admission_date,record_time,chief_complaint,
        present_illness,ecg,preliminary_diagnosis,case_features,diagnostic_basis,
        differential_diagnosis,treatment_plan,attending_physician,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
