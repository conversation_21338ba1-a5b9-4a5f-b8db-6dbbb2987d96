<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.mottoadvice.MottoAdviceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="queryList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.f_drug_name ,b.f_enabled_mark,b.f_drug_number,b.f_drug_spec,b.f_prepara_unit,b.f_prepara_unit,b.f_drug_form_count,b.f_once_using,b.f_spec_unit, b.f_dmin
        from  wl_motto_advice a LEFT join wl_his_drug b on a.drug_id = b.f_drug_id
        where   a.deleted = 0 and b.f_enabled_mark=1 and  a.template_id  = #{templateId}
    </select>

    <update id="updateByPid">
        update wl_motto_advice set advice_type = #{adviceType} where pid = #{pid}
    </update>

    <update id="deleteByParentId">
        update wl_motto_advice  set deleted = 1   where pid = #{pid}
    </update>

    <select id="queryLists" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.f_drug_name,b.f_enabled_mark ,b.f_drug_number,b.f_drug_spec,b.f_prepara_unit,b.f_prepara_unit,b.f_drug_form_count,b.f_once_using,b.f_spec_unit, b.f_dmin
        from  wl_motto_advice a LEFT join wl_his_drug b on a.drug_id = b.f_drug_id
        where   a.deleted = 0 and b.f_enabled_mark=1 and  a.template_id  in
        <foreach collection="templateIds" item="templateId" separator="," open="(" close=")">
            #{templateId}
        </foreach>
    </select>


    <select id="queryDrugList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.name as fDrugName,b.spec as fDrugSpec,b.min_pack as fDrugDormCount
        from  wl_motto_advice a LEFT join wl_drug b on a.drug_id = b.id
        where   a.deleted = 0 and b.deleted = 0 and  a.template_id  = #{templateId}
    </select>

    <select id="queryConsumeList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.consum_name as fDrugName,b.enabled_mark as fEnabledMark ,b.consum_number as fDrugNumber,b.consum_spec as fDrugSpec,b.unit as fPreparaUnit,b.unit as fSpecUnit
        from  wl_motto_advice a LEFT join wl_his_consumables b on a.drug_id = b.consum_id
        where   a.deleted = 0 and b.enabled_mark=1 and  a.template_id  =
            #{templateIds}

    </select>

    <select id="queryComboList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.package_name as fDrugName,b.enabled_mark as fEnabledMark
        from  wl_motto_advice a LEFT join wl_his_combo b on a.drug_id = b.package_id
        where   a.deleted = 0 and b.enabled_mark=1 and  a.template_id  =
            #{templateIds}

    </select>

    <select id="queryInfoList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.item_name as fDrugName,b.enabled_mark as fEnabledMark ,b.unit as fPreparaUnit,b.unit as fSpecUnit
        from  wl_motto_advice a LEFT join wl_his_information b on a.drug_id = b.item_id
        where   a.deleted = 0 and b.enabled_mark=1 and  a.template_id  =
            #{templateIds}

    </select>

    <select id="queryProjectList" resultType="com.thj.boot.module.business.pojo.mottoadvice.vo.MottoAdviceRespVO">
        select a.*,b.name as fDrugName,b.status as fEnabledMark ,b.unit as fPreparaUnit,b.unit as fSpecUnit
        from  wl_motto_advice a LEFT join wl_project b on a.drug_id = b.id
        where   a.deleted = 0 and b.status=0 and  a.template_id  =
                                                        #{templateId}

    </select>
</mapper>
