<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.jkvaluation.JkValuationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getList" resultType="com.thj.boot.module.business.pojo.jkvaluation.vo.JkValuationVO">

        SELECT
        t.patient_id AS id,t.machine_num, t.patient_name AS name,t.dialyze_no AS dialyzeNo,t.nurse_id ,t.nurse_name AS nurse,t.dialyze_total
        ,t.Edu_methods AS
        way,t.last_time AS
        wayDate,t1.qualified AS qualified,t1.questions_total AS questionsTotal,t1.correct_total AS correctTotal
        FROM wl_jk_implementation t LEFT JOIN wl_jk_valuation t1 ON (t1.patient_id = t.patient_id)
        WHERE 1=1
        <if test="nurseId !=null">
            and t1.nurse_id = #{nurseId}
        </if>
        <if test="qualified !=null">
            and t1.qualified = #{qualified}
        </if>
        <if test="state !=null">
            and t.binding = #{state}
        </if>
        <if test="patientState !=null">
            and t.patient_type = #{patientState}
        </if>
        <if test="keyword !=null">
            and (patient_name.name like #{keyWord} or t.pinyin like #{keyWord} or t.dialyze_no like #{keyWord} )
        </if>
        order by t.patient_id
    </select>

    <select id="getPatientEdu" resultType="com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo">
        select patient_id,patient_name as name,dialyze_no,last_time as education_time,cunt as eduTotal,dialyze_total from wl_jk_implementation
        <if test="beginTime != null">
            and education_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and education_time &lt;= #{endTime}
        </if>
        <if test="keyword != null">
            and (a.patient_name like concat("%", #{keyword}, "%")
            or pinyin like concat("%", #{keyword}, "%")
            or dialyze_no like concat("%", #{keyword}, "%") )
        </if>
        order by patient_id
    </select>

    <select id="getPatientValuationList" resultType="com.thj.boot.module.business.dal.datado.jkvaluation.JkValuationDO">
        select * from wl_jk_valuation where 1=1
        <if test="createMouth != null">
            and create_mouth in (createMouth)
        </if>
        <if test="createYear != null">
            and create_year in (createYear)
        </if>
    </select>

    <select id="queryEstimate" resultType="com.thj.boot.module.business.controller.admin.gk.vo.EstimateVo">
        select a.id, a.dialyze_no, a.`name`, a.dialyze_total, b.*
        from wl_patient a
                 LEFT JOIN wl_jk_estimate b on a.id = b.patient_id
        where 1=1
        <if test="estimateId != null">
           and  b.estimate_id = #{}
        </if>
        <if test="state != null">
            and  b.state = #{state}
        </if>
        <if test="wechatState != null">
            and  b.state = #{wechatState}
        </if>
        <if test="keyword !=null">
            and (a.name like concat("%", #{keyword}, "%")
            or a.spell_name like concat("%", #{keyword}, "%")
            or a.dialyze_no = concat("%", #{keyword}, "%")
        </if>
    </select>

    <select id="queryNews" resultType="com.thj.boot.module.business.dal.datado.jkeducation.JkEducationDO">
        select a.*,b.nickname as educationNurseName,c.nickname as evaluateNurseName from wl_jk_education a
            left join system_users b on a.education_nurse = b.id
            LEFT JOIN system_users c on a.evaluate_nurse = c.id
            where a.patient_id = #{patientId}
    </select>

    <select id="getKnowledge" resultType="java.util.Map">
        select   COUNT(1) as patient  , sum(questions_total) as  questionsTotal,sum(correct_total)as correctTotal   from wl_jk_valuation
    </select>

</mapper>
