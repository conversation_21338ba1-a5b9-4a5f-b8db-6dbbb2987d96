<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.jkimplementation.JkImplementationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getcollectList" resultType="com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo">
        select patient_id   as patient_id,
               patient_name as name,
               patient_type as patientState,
               dialyze_no,
               dialyze_total,
               Edu_methods  as educationalForms,
               last_time    as educationTime
        from wl_jk_implementation
        where 1 = 1
        <if test="keyword != null and keyword != ''" >
            AND (patient_name LIKE  concat('%',#{keyword},'%') OR pinyin LIKE  concat('%',#{keyword},'%') OR dialyze_no LIKE  concat('%',#{keyword},'%'))
        </if>
    </select>

    <select id="queryEstimate" resultType="com.thj.boot.module.business.controller.admin.gk.vo.EstimateVo">
        select patient_id,patient_name as name ,dialyze_no,nurse_name,last_time as createTime from wl_jk_implementation
        where 1 = 1
        <if test="nurseId != null and nurseId != ''" >
            AND nurse_id = #{nurseId}
        </if>
<!--        <if test="state != null and state != ''" >-->
<!--            AND binding = #{state}-->
<!--        </if>-->
<!--        <if test="data != null" >-->
<!--            AND state = #{data}-->
<!--        </if>-->
        <if test="keyword != null and keyword != ''" >
            AND (patient_name LIKE  concat('%',#{keyword},'%') OR pinyin LIKE  concat('%',#{keyword},'%') OR dialyze_no LIKE  concat('%',#{keyword},'%'))
        </if>
        order by patient_id
    </select>


</mapper>
