<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper">

    <select id="getHbvPositive" resultType="com.thj.boot.module.business.pojo.renalproject.vo.ProjectInfoAnalysis">
        SELECT wrp.id, wrp.dict_id AS dictId, wrp.patient_id AS patientId,info.id as infoId, info.check_time as checkTime
        FROM
            wl_renal_project wrp
        RIGHT JOIN wl_renal_project_info info ON info.project_id = wrp.id
        WHERE
        info.deleted = '0'
        AND wrp.dict_id = (
            SELECT ID FROM system_dict_data sd WHERE sd.label = '传染病四项'
        )
        <if test="req.startTime != null and req.endTime != null">
            AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY wrp.patient_id DESC
    </select>

    <select id="getHbvPositiveBefore" resultType="com.thj.boot.module.business.pojo.renalproject.vo.ProjectInfoAnalysis">
        SELECT
            wrp.id, wrp.dict_id AS dictId, wrp.patient_id AS patientId,info.id as infoId, info.project_info AS projectInfo, info.check_time as checkTime
        FROM
            wl_renal_project_info info
        JOIN (
            SELECT
                MAX(info.id) as id
            FROM
                wl_renal_project wrp
            RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
            JOIN (SELECT ID FROM system_dict_data sd WHERE sd.label = '传染病四项') sd1 ON wrp.dict_id = sd1.id
            <where>
                info.deleted = '0'
                <if test="req.startTime != null and req.endTime != null">
                    AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
                </if>
            </where>
            GROUP BY info.project_id
        ) temp on info.id = temp.id
        LEFT JOIN wl_renal_project wrp ON info.project_id = wrp.id
        where wrp.deleted = '0'
    </select>

    <select id="getRenalProjectStatistics" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        SELECT COUNT(*) AS count,wp.dept_id as centerId
        FROM wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT MAX(info.id) as id, wrp.patient_id, info.json_value
        FROM wl_renal_project wrp
        RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        <if test="req.projectName != null">
            JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
        </if>
        <if test="req.secondProjectName != null or req.thirdProjectName != null">
            JOIN wl_renal_project_item item ON (item.id = info.json_key
            <if test="req.secondProjectName != null">AND item.label = #{req.secondProjectName,jdbcType=VARCHAR}</if>
            <if test="req.thirdProjectName != null">OR item.label = #{req.thirdProjectName,jdbcType=VARCHAR}</if>
            )
        </if>
        WHERE
        info.deleted = 0
        AND info.json_value is not null AND info.json_value != ''
        <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        <if test="req.startTime != null and req.endTime != null">
            AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY wrp.patient_id
        ) wrp ON wp.id = wrp.patient_id
        LEFT JOIN wl_renal_project_info info ON info.id = wrp.id
        WHERE wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        <if test="req.completeStatus != null">
            <if test="req.completeStatus == 1">
                AND wrp.id IS NOT NULL
                <if test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                    AND info.json_value IS NOT NULL AND info.json_value != ''
                </if>
            </if>
            <if test="req.completeStatus == 2">
                AND (wrp.id IS NULL
                <choose>
                    <when test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                        OR (info.project_id IS NOT NULL AND (info.json_value IS NULL OR info.json_value = '')))
                    </when>
                    <otherwise>
                        )
                    </otherwise>
                </choose>
            </if>
        </if>
        group by centerId
    </select>

    <select id="getRenalProjectTotal" resultType="java.lang.Long">
        SELECT COUNT(*) AS count
        FROM wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
            SELECT MAX(info.id) as id, wrp.patient_id, info.json_value
            FROM wl_renal_project wrp
            RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        <if test="req.projectName != null">
            JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
        </if>
        <if test="req.secondProjectName != null or req.thirdProjectName != null">
            JOIN wl_renal_project_item item ON (item.id = info.json_key
            <if test="req.secondProjectName != null">AND item.label = #{req.secondProjectName,jdbcType=VARCHAR}</if>
            <if test="req.thirdProjectName != null">OR item.label = #{req.thirdProjectName,jdbcType=VARCHAR}</if>
            )
        </if>
            WHERE
            info.deleted = 0
            AND info.json_value is not null AND info.json_value != ''
            <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
            <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
            <if test="req.startTime != null and req.endTime != null">
                AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            GROUP BY wrp.patient_id
        ) wrp ON wp.id = wrp.patient_id
        LEFT JOIN wl_renal_project_info info ON info.id = wrp.id
        WHERE wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        <if test="req.completeStatus != null">
            <if test="req.completeStatus == 1">
                AND wrp.id IS NOT NULL
                <if test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                    AND info.json_value IS NOT NULL AND info.json_value != ''
                </if>
            </if>
            <if test="req.completeStatus == 2">
                AND (wrp.id IS NULL
                <choose>
                    <when test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                    OR (info.project_id IS NOT NULL AND (info.json_value IS NULL OR info.json_value = '')))
                    </when>
                    <otherwise>
                        )
                    </otherwise>
                </choose>
            </if>
        </if>
    </select>

    <select id="getInfectiousDiseasesPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        wp.deleted
        FROM wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        where wp.id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="getRenalProjectPage" resultType="java.util.Map">
        SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
             <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            <if test="req.isSummary != null and req.isSummary == 1">
             wp.sex as sex, info.id as infoId, info.project_id as projectId, info.project_info as projectInfo
            </if>
        CASE
            WHEN
                <choose>
                    <when test="req.hasDataIsComplete == 0">
                        wrp.id IS NOT NULL AND wrp.json_value IS NOT NULL AND wrp.json_value != '' THEN '已完成'
                    </when>
                    <otherwise>
                        wrp.id IS NOT NULL THEN '已完成'
                    </otherwise>
                </choose>
            WHEN wrp.id IS NULL THEN '未完成'
            ELSE '未完成'
        END AS patientStatus
        FROM wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
            SELECT info.id, temp.patient_id, info.json_value, info.project_id
            FROM wl_renal_project_info info
            JOIN (
                SELECT MAX(info.id) as id, wrp.patient_id
                FROM wl_renal_project wrp
                RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                <if test="req.projectName != null">
                    JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
                </if>
                <if test="req.secondProjectName != null or req.thirdProjectName != null">
                    JOIN wl_renal_project_item item ON (item.id = info.json_key
                    <if test="req.secondProjectName != null">AND item.label = #{req.secondProjectName,jdbcType=VARCHAR}</if>
                    <if test="req.thirdProjectName != null">OR item.label = #{req.thirdProjectName,jdbcType=VARCHAR}</if>
                    )
                </if>
                WHERE
                info.deleted = 0
                AND info.json_value is not null AND info.json_value != ''
                <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                <if test="req.startTime != null and req.endTime != null">
                    AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
                </if>
                GROUP BY wrp.patient_id
            ) AS temp
            WHERE info.id in (temp.id)
        ) wrp ON wp.id = wrp.patient_id
        LEFT JOIN wl_renal_project_info info ON info.id = wrp.id
         <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        WHERE wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        <if test="req.completeStatus != null">
            <if test="req.completeStatus == 1">
                AND wrp.id IS NOT NULL
                <if test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                    AND info.json_value IS NOT NULL AND info.json_value != ''
                </if>
            </if>
            <if test="req.completeStatus == 2">
                AND (wrp.id IS NULL
                <choose>
                    <when test="req.hasDataIsComplete == null || req.hasDataIsComplete != 1">
                        OR (info.project_id IS NOT NULL AND (info.json_value IS NULL OR info.json_value = '')))
                    </when>
                    <otherwise>
                        )
                    </otherwise>
                </choose>
            </if>
        </if>
    </select>

    <select id="getRenalProjectList" resultType="java.util.Map">
        SELECT
            wp.id as patientId,
            wp.dialyze_no as dialyzeNo,
            wp.`name` as patientName,
            wp.dialyze_age as dialyzeAge,
            wrp.dict_id AS dictId,
            info.json_key AS jsonKey,
            info.json_value AS jsonValue,
            info.project_id AS projectId, info.project_type,
            info.check_time AS checkTime
        FROM wl_patient wp
        LEFT JOIN wl_renal_project wrp ON wrp.patient_id = wp.id
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        WHERE wrp.deleted = 0 AND wp.deleted = 0
        <if test="patientId != null and patientId != ''"> AND wp.id = #{patientId,jdbcType=BIGINT}</if>
        ORDER BY info.create_time DESC
    </select>

    <select id="getStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        <if test="req.statisticType != null and req.statisticType == 1">
        SELECT
            count(*) count,
            dept_id as centerId
        FROM
        (
            SELECT wp.name, info.id, info.patient_id, info.json_value AS  jsonValue,wp.dept_id
            FROM wl_patient wp
            LEFT JOIN (
                SELECT info.id, temp.patient_id, info.json_value, info.project_id
                FROM wl_renal_project_info info
                JOIN (
                    SELECT MAX(info.id) as id, wrp.patient_id
                    FROM wl_renal_project wrp
                    RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                    <if test="req.projectName != null">
                        JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
                    </if>
                    WHERE
                        info.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                        <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                        <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                        <if test="req.startTime != null and req.endTime != null">
                            AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
                        </if>
                    GROUP BY wrp.patient_id
                ) AS temp
                WHERE info.id in (temp.id)
            ) info ON wp.id = info.patient_id

            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <where>
                wp.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
                <if test="req.range == 1">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &gt;=  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                        AND (
                        <if test="req.standardValueMax1 != null">wp.sex = '1' AND info.json_value &gt;= #{req.standardValueMax1}</if>
                        <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> wp.sex = '2' AND info.json_value &gt; #{req.standardValueMax2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 2">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &lt;  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin != null">AND info.json_value &gt;=  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                        AND (
                        <if test="req.standardValueMax1 != null">wp.sex = '1' AND info.json_value &lt; #{req.standardValueMax1}</if>
                        <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> wp.sex = '2' AND info.json_value &lt; #{req.standardValueMax2}</if>
                        )
                    </if>
                    <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                        AND (
                        <if test="req.standardValueMin1 != null">wp.sex = '1' AND info.json_value &gt;= #{req.standardValueMin1}</if>
                        <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> wp.sex = '2' AND info.json_value &gt;= #{req.standardValueMin2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 3">
                    AND info.json_value != ''
                    <if test="req.standardValueMin != null">AND info.json_value &lt;  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                        AND (
                        <if test="req.standardValueMin1 != null">wp.sex = '1' AND info.json_value &lt; #{req.standardValueMin1}</if>
                        <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> wp.sex = '2' AND info.json_value &lt; #{req.standardValueMin2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 4">AND (info.json_value IS NULL OR info.json_value = '')</if>
            </where>
        ) info group by centerId
        </if>
    </select>




    <select id="getComplicationResultCount" resultType="java.lang.Long">
        <if test="req.statisticType != null and req.statisticType == 1">
            SELECT
            count(*) count
            FROM
            (
            SELECT wp.name, info.id, info.patient_id, info.json_value AS  jsonValue
            FROM wl_patient wp
            LEFT JOIN (
            SELECT info.id, temp.patient_id, info.json_value, info.project_id
            FROM wl_renal_project_info info
            JOIN (
            SELECT MAX(info.id) as id, wrp.patient_id
            FROM wl_renal_project wrp
            RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
            <if test="req.projectName != null">
                JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
            </if>
            WHERE
            info.deleted = 0
            AND info.json_value is not null AND info.json_value != ''
            <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
            <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
            <if test="req.startTime != null and req.endTime != null">
                AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            GROUP BY wrp.patient_id
            ) AS temp
            WHERE info.id in (temp.id)
            ) info ON wp.id = info.patient_id

            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <where>
                wp.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
                <if test="req.range == 1">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &gt;=  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                        AND (
                        <if test="req.standardValueMax1 != null">wp.sex = '1' AND info.json_value &gt;= #{req.standardValueMax1}</if>
                        <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> wp.sex = '2' AND info.json_value &gt; #{req.standardValueMax2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 2">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &lt;  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin != null">AND info.json_value &gt;=  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                        AND (
                        <if test="req.standardValueMax1 != null">wp.sex = '1' AND info.json_value &lt; #{req.standardValueMax1}</if>
                        <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> wp.sex = '2' AND info.json_value &lt; #{req.standardValueMax2}</if>
                        )
                    </if>
                    <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                        AND (
                        <if test="req.standardValueMin1 != null">wp.sex = '1' AND info.json_value &gt;= #{req.standardValueMin1}</if>
                        <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> wp.sex = '2' AND info.json_value &gt;= #{req.standardValueMin2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 3">
                    AND info.json_value != ''
                    <if test="req.standardValueMin != null">AND info.json_value &lt;  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                        AND (
                        <if test="req.standardValueMin1 != null">wp.sex = '1' AND info.json_value &lt; #{req.standardValueMin1}</if>
                        <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> wp.sex = '2' AND info.json_value &lt; #{req.standardValueMin2}</if>
                        )
                    </if>
                </if>
                <if test="req.range == 4">AND (info.json_value IS NULL OR info.json_value = '')</if>
            </where>
            ) info
        </if>
        <if test="req.statisticType != null and req.statisticType == 2">
            SELECT
            count(*) count
            FROM
            (
            SELECT
            a.json_value  jsonValue,
            wp.id AS patient_id, wp.sex
            FROM wl_patient wp
            RIGHT JOIN (
            SELECT
            wrp.id,
            wrp.patient_id,
            info.json_value
            FROM
            `wl_renal_project` wrp
            JOIN wl_renal_project_info info ON info.project_id = wrp.id
            WHERE info.deleted = 0
            <if test="req.dictId != null">AND wrp.dict_id = #{req.dictId}</if>
            <if test="req.itemId != null">AND info.json_key = #{req.itemId,jdbcType=BIGINT}</if>
            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            ) a ON  a.patient_id = wp.id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            <where>
                wp.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
            </where>
            ) temp
            <where>
                <if test="req.range != null">
                    <if test="req.range == 1">
                        AND temp.jsonValue != ''
                        <if test="req.standardValueMax != null">AND temp.jsonValue &gt;= #{req.standardValueMax,jdbcType=DOUBLE}</if>
                        <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                            AND (
                            <if test="req.standardValueMax1 != null">temp.sex = '1' AND temp.jsonValue &gt;= #{req.standardValueMax1}</if>
                            <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> temp.sex = '2' AND temp.jsonValue &gt;= #{req.standardValueMax2}</if>
                            )
                        </if>
                    </if>
                    <if test="req.range == 2">
                        AND temp.jsonValue != ''
                        <if test="req.standardValueMax != null">AND temp.jsonValue &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
                        <if test="req.standardValueMin != null">AND temp.jsonValue &gt;= #{req.standardValueMin,jdbcType=DOUBLE}</if>

                        <if test="req.standardValueMax1 != null or req.standardValueMax2 != null">
                            AND (
                            <if test="req.standardValueMax1 != null">temp.sex = '1' AND info.jsonValue &lt; #{req.standardValueMax1}</if>
                            <if test="req.standardValueMax2 != null"><if test="req.standardValueMax1 != null">OR</if> temp.sex = '2' AND temp.jsonValue &lt; #{req.standardValueMax2}</if>
                            )
                        </if>
                        <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                            AND (
                            <if test="req.standardValueMin1 != null">temp.sex = '1' AND info.jsonValue &gt;= #{req.standardValueMin1}</if>
                            <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> temp.sex = '2' AND temp.jsonValue &gt;= #{req.standardValueMin2}</if>
                            )
                        </if>
                    </if>
                    <if test="req.range == 3">
                        AND temp.jsonValue != ''
                        <if test="req.standardValueMin != null">AND temp.jsonValue &lt; #{req.standardValueMin,jdbcType=DOUBLE}</if>

                        <if test="req.standardValueMin1 != null or req.standardValueMin2 != null">
                            AND (
                            <if test="req.standardValueMin1 != null">temp.sex = '1' AND temp.jsonValue &lt; #{req.standardValueMin1}</if>
                            <if test="req.standardValueMin2!= null"><if test="req.standardValueMin1 != null">OR</if> temp.sex = '2' AND temp.jsonValue &lt; #{req.standardValueMin2}</if>
                            )
                        </if>
                    </if>
                    <if test="req.range == 4"> AND (temp.jsonValue IS NULL OR temp.jsonValue = '')</if>
                </if>
            </where>
        </if>
    </select>

    <select id="getComplicationResultPage" resultType="java.util.Map">
        <if test="req.statisticType != null and req.statisticType == 1">
            SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
            info.id, info.json_value AS jsonValue, info.project_id AS projectId, DATE_FORMAT(info.check_time , '%Y-%m-%d') AS createTime
            FROM wl_patient wp
            LEFT JOIN (

                SELECT info.id, temp.patient_id, info.json_value, info.project_id, info.check_time
                FROM wl_renal_project_info info
                JOIN (
                    SELECT MAX(info.id) as id, wrp.patient_id
                    FROM wl_renal_project wrp
                    RIGHT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                    <if test="req.projectName != null">
                        JOIN system_dict_data sd ON (wrp.dict_id = sd.id AND sd.label = #{req.projectName,jdbcType=VARCHAR})
                    </if>
                    WHERE
                        info.deleted = 0
                        AND info.json_value is not null AND info.json_value != ''
                        <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
                        <if test="req.deptIds != null and req.deptIds.length > 0">AND wrp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                        <if test="req.startTime != null and req.endTime != null">
                            AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
                        </if>
                    GROUP BY wrp.patient_id
                ) AS temp
                WHERE info.id in (temp.id)

            ) info ON wp.id = info.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
            <where>
                1=1
                AND wp.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
                <if test="req.range == 1">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &gt;=  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                </if>
                <if test="req.range == 2">
                    AND info.json_value != ''
                    <if test="req.standardValueMax != null">AND info.json_value &lt;  #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin != null">AND info.json_value &gt;=  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                </if>
                <if test="req.range == 3">
                    AND info.json_value != ''
                    <if test="req.standardValueMin != null">AND info.json_value &lt;  #{req.standardValueMin,jdbcType=DOUBLE}</if>
                </if>
                <if test="req.range == 4">AND (info.json_value IS NULL OR info.json_value = '')</if>
            </where>
            ORDER BY createTime DESC
        </if>
        <if test="req.statisticType != null and req.statisticType == 2">
            SELECT  <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
                temp.*
            FROM
            (
                SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
                info.id, info.json_value AS jsonValue, info.project_id AS projectId, DATE_FORMAT(info.check_time , '%Y-%m-%d') AS createTime
                FROM wl_patient wp
                RIGHT JOIN (
                    SELECT
                        wrp.id,  wrp.patient_id,  info.json_value, info.project_id, info.check_time
                    FROM
                    `wl_renal_project` wrp
                    JOIN wl_renal_project_info info ON info.project_id = wrp.id
                    WHERE  wrp.deleted = 0 AND info.deleted = 0
                    <if test="req.dictId != null">AND wrp.dict_id = #{req.dictId}</if>
                    <if test="req.itemId != null">AND info.json_key = #{req.itemId,jdbcType=BIGINT}</if>
                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                ) info ON  info.patient_id = wp.id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
                <where>
                    wp.deleted = 0
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                </where>
            ) temp
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
            <where>
                1=1
                <if test="req.range != null">
                    <if test="req.range == 1">
                        AND temp.jsonValue != ''
                        <if test="req.standardValueMax != null">AND temp.jsonValue &gt;= #{req.standardValueMax,jdbcType=DOUBLE}</if>
                    </if>
                    <if test="req.range == 2">
                        AND temp.jsonValue != ''
                        <if test="req.standardValueMax != null">AND temp.jsonValue &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
                        <if test="req.standardValueMin != null">AND temp.jsonValue &gt;= #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    </if>
                    <if test="req.range == 3">
                        AND temp.jsonValue != ''
                     <if test="req.standardValueMin != null">AND temp.jsonValue &lt; #{req.standardValueMin,jdbcType=DOUBLE}</if>
                    </if>
                    <if test="req.range == 4"> AND (temp.jsonValue IS NULL OR temp.jsonValue = '')</if>
                </if>
            </where>
            ORDER BY temp.createTime DESC
        </if>
    </select>

    <select id="getCkdMbdStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        SELECT count(*) count,
        dept_id as centerId
        FROM
        (
        SELECT
        info1.json_value As json_value1, info1.json_key AS json_key1,
        info2.json_value AS json_value2, info2.json_key AS json_key2,
        info3.json_value AS json_value3, info3.json_key AS json_key3,
        wp.dept_id
        FROM
        wl_patient wp
        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info1 on info1.patient_id = wp.id

        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info2 on info2.patient_id = wp.id

        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info
        WHERE deleted = 0
        <if test="req.itemId3 != null">AND json_key = #{req.itemId3,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info3 on info3.patient_id = wp.id
        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        </where>
        ) info
        <where>
            <if test="req.range == 2">
                <if test="req.standardValueMax1 != null">AND info.json_value1 &lt; #{req.standardValueMax1,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin1 != null">AND info.json_value1 &gt; #{req.standardValueMin1,jdbcType=DOUBLE}</if>

                <if test="req.standardValueMax2 != null">AND info.json_value2 &lt; #{req.standardValueMax2,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin2 != null">AND info.json_value2 &gt; #{req.standardValueMin2,jdbcType=DOUBLE}</if>

                <if test="req.standardValueMax3 != null">AND info.json_value3 &lt; #{req.standardValueMax3,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin3 != null">AND info.json_value3 &gt; #{req.standardValueMin3,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 4">
                OR info.json_value1 IS NULL or info.json_value1 = ''
                OR info.json_value2 IS NULL or info.json_value2 = ''
                OR info.json_value3 IS NULL or info.json_value3 = ''
            </if>
            <if test="req.range == 5 ">
                <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &gt; #{req.standardValueMax1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &lt; #{req.standardValueMin1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &gt; #{req.standardValueMax2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &lt; #{req.standardValueMin2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &gt; #{req.standardValueMax3,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &lt; #{req.standardValueMin3,jdbcType=DOUBLE})</if>
            </if>
        </where>
        group by centerId
    </select>

    <select id="getDialysisComplicationsCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM wl_patient wp
        LEFT JOIN wl_hemodialysis_manager wh ON wp.id = wh.patient_id
        WHERE wh.deleted = 0 and  wh.heal_state = 1
        <if test="isDialysisComplications">
            <if test="req.range == 2">
                AND wh.dialysis_complications_type is not null and wh.dialysis_complications_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.dialysis_complications_type is null or wh.dialysis_complications_type = '')
            </if>
        </if>
        <if test="isSeriousDialysisAccident">
            <if test="req.range == 2">
                AND wh.serious_dialysis_accident_type is not null and wh.serious_dialysis_accident_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.serious_dialysis_accident_type is null or wh.serious_dialysis_accident_type = '')
            </if>
        </if>
        <if test="req.startTime != null and req.endTime != null">AND wh.hemodialysis_time BETWEEN
            #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getDialysisComplicationsPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        <if test="isDialysisComplications">
            wh.dialysis_complications_type as jsonValue
        </if>
        <if test="isSeriousDialysisAccident">
            wh.serious_dialysis_accident_type as jsonValue
        </if>
        FROM wl_patient wp
        LEFT JOIN wl_hemodialysis_manager wh ON wp.id = wh.patient_id
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        WHERE wh.deleted = 0 and wh.heal_state = 1
        <if test="isDialysisComplications">
            <if test="req.range == 2">
                AND wh.dialysis_complications_type is not null and wh.dialysis_complications_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.dialysis_complications_type is null or wh.dialysis_complications_type = '')
            </if>
        </if>
        <if test="isSeriousDialysisAccident">
            <if test="req.range == 2">
                AND wh.serious_dialysis_accident_type is not null and wh.serious_dialysis_accident_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.serious_dialysis_accident_type is null or wh.serious_dialysis_accident_type = '')
            </if>
        </if>
        <if test="req.startTime != null and req.endTime != null">AND wh.hemodialysis_time BETWEEN
            #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getDialysisComplicationsStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        SELECT COUNT(*) as count ,wp.dept_id as centerId FROM wl_patient wp
        LEFT JOIN wl_hemodialysis_manager wh ON wp.id = wh.patient_id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        WHERE wh.deleted = 0 and wh.heal_state = 1
        <if test="isDialysisComplications">
            <if test="req.range == 2">
                AND wh.dialysis_complications_type is not null and wh.dialysis_complications_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.dialysis_complications_type is null or wh.dialysis_complications_type = '')
            </if>
        </if>
        <if test="isSeriousDialysisAccident">
            <if test="req.range == 2">
                AND wh.serious_dialysis_accident_type is not null and wh.serious_dialysis_accident_type != ''
            </if>
            <if test="req.range == 5">
                AND (wh.serious_dialysis_accident_type is null or wh.serious_dialysis_accident_type = '')
            </if>
        </if>
        AND wp.patient_type = '1'
        AND wp.deleted = 0
        <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN
            <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
            </foreach>
        </if>
        group by centerId
    </select>


    <select id="getCkdMbdRateCount" resultType="java.lang.Long">
            SELECT count(*) count
            FROM
                (
                SELECT
                info1.json_value As json_value1, info1.json_key AS json_key1,
                info2.json_value AS json_value2, info2.json_key AS json_key2,
                info3.json_value AS json_value3, info3.json_key AS json_key3
                FROM
                wl_patient wp
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
                LEFT JOIN (
                    SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id
                    FROM `wl_renal_project` wrp
                    LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                    JOIN (
                        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
                        AND json_value is not null AND json_value != ''
                        <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
                        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY project_id, json_key
                    ) temp ON temp.maxId = info.id
                    GROUP BY wrp.patient_id, info.json_key
                ) info1 on info1.patient_id = wp.id

                LEFT JOIN (
                SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
                FROM `wl_renal_project` wrp
                LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                JOIN (
                    SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
                    AND json_value is not null AND json_value != ''
                <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
                <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY project_id, json_key
                    ) temp ON temp.maxId = info.id
                GROUP BY wrp.patient_id, info.json_key
                ) info2 on info2.patient_id = wp.id

                LEFT JOIN (
                    SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
                    FROM `wl_renal_project` wrp
                    LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                    JOIN (
                        SELECT max( id ) maxId FROM wl_renal_project_info
                        WHERE deleted = 0
                        AND json_value is not null AND json_value != ''
                        <if test="req.itemId3 != null">AND json_key = #{req.itemId3,jdbcType=BIGINT}</if>
                        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                        GROUP BY project_id, json_key
                    ) temp ON temp.maxId = info.id
                    GROUP BY wrp.patient_id, info.json_key
                ) info3 on info3.patient_id = wp.id
                <where>
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
                </where>
            ) info
            <where>
                <if test="req.range == 2">
                    <if test="req.standardValueMax1 != null">AND info.json_value1 &lt;= #{req.standardValueMax1,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin1 != null">AND info.json_value1 &gt;= #{req.standardValueMin1,jdbcType=DOUBLE}</if>

                    <if test="req.standardValueMax2 != null">AND info.json_value2 &lt;= #{req.standardValueMax2,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin2 != null">AND info.json_value2 &gt;= #{req.standardValueMin2,jdbcType=DOUBLE}</if>

                    <if test="req.standardValueMax3 != null">AND info.json_value3 &lt;= #{req.standardValueMax3,jdbcType=DOUBLE}</if>
                    <if test="req.standardValueMin3 != null">AND info.json_value3 &gt;= #{req.standardValueMin3,jdbcType=DOUBLE}</if>
                </if>
                <if test="req.range == 4">
                    OR info.json_value1 IS NULL or info.json_value1 = ''
                    OR info.json_value2 IS NULL or info.json_value2 = ''
                    OR info.json_value3 IS NULL or info.json_value3 = ''
                </if>
                <if test="req.range == 5 ">
                    <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &gt;= #{req.standardValueMax1,jdbcType=DOUBLE})</if>
                    <if test="req.standardValueMin1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &lt;= #{req.standardValueMin1,jdbcType=DOUBLE})</if>
                    <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &gt;= #{req.standardValueMax2,jdbcType=DOUBLE})</if>
                    <if test="req.standardValueMin2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &lt;= #{req.standardValueMin2,jdbcType=DOUBLE})</if>
                    <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &gt;= #{req.standardValueMax3,jdbcType=DOUBLE})</if>
                    <if test="req.standardValueMin3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &lt;= #{req.standardValueMin3,jdbcType=DOUBLE})</if>
                </if>
            </where>
    </select>

    <select id="getCkdMbdRatePage" resultType="java.util.Map">
        SELECT <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> info.*
        FROM
            (
            SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
             info1.json_value As json_value1, info1.json_key AS json_key1, DATE_FORMAT( info1.check_time, '%Y-%m-%d' ) AS checkTime1
            , info2.json_value AS json_value2,  info2.json_key AS json_key2, DATE_FORMAT( info2.check_time, '%Y-%m-%d' ) AS checkTime2
            , info3.json_value AS json_value3,  info3.json_key AS json_key3, DATE_FORMAT( info3.check_time, '%Y-%m-%d' ) AS checkTime3
            FROM wl_patient wp
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            LEFT JOIN (
                SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id, info.check_time
                FROM
                `wl_renal_project` wrp
                LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                JOIN (
                    SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
                    AND json_value is not null AND json_value != ''
            <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
            <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY project_id, json_key
                ) temp ON temp.maxId = info.id
                GROUP BY wrp.patient_id, info.json_key
            ) info1 on info1.patient_id = wp.id

            LEFT JOIN (
            SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id, info.check_time
            FROM `wl_renal_project` wrp
            LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
            JOIN (
                SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
                AND json_value is not null AND json_value != ''
            <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
            <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                GROUP BY project_id, json_key
                ) temp ON temp.maxId = info.id
            GROUP BY wrp.patient_id, info.json_key
            ) info2 on info2.patient_id = wp.id

            LEFT JOIN (
                SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id, info.check_time
                FROM `wl_renal_project` wrp
                LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
                JOIN (
                    SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
                    AND json_value is not null AND json_value != ''
            <if test="req.itemId3 != null">AND json_key = #{req.itemId3,jdbcType=BIGINT}</if>
            <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                    GROUP BY project_id, json_key
                ) temp ON temp.maxId = info.id
                GROUP BY wrp.patient_id, info.json_key
            ) info3 on info3.patient_id = wp.id
            <where>
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
            </where>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
        ) info
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <if test="req.range == 2">
                <if test="req.standardValueMax1 != null">AND info.json_value1 &lt;= #{req.standardValueMax1,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin1 != null">AND info.json_value1 &gt;= #{req.standardValueMin1,jdbcType=DOUBLE}</if>

                <if test="req.standardValueMax2 != null">AND info.json_value2 &lt;= #{req.standardValueMax2,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin2 != null">AND info.json_value2 &gt;= #{req.standardValueMin2,jdbcType=DOUBLE}</if>

                <if test="req.standardValueMax3 != null">AND info.json_value3 &lt;= #{req.standardValueMax3,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMin3 != null">AND info.json_value3 &gt;= #{req.standardValueMin3,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 4">
                OR info.json_value1 IS NULL or info.json_value1 = ''
                OR info.json_value2 IS NULL or info.json_value2 = ''
                OR info.json_value3 IS NULL or info.json_value3 = ''
            </if>
            <if test="req.range == 5 ">
                <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &gt;= #{req.standardValueMax1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value1 &lt;= #{req.standardValueMin1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &gt;= #{req.standardValueMax2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value2 &lt;= #{req.standardValueMin2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &gt;= #{req.standardValueMax3,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMin3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.json_value3 IS NOT NULL and info.json_value3 != '' and info.json_value3 &lt;= #{req.standardValueMin3,jdbcType=DOUBLE})</if>
            </if>
        </where>
    </select>

    <sql id="preComprehensiveIndicators">
        WITH RankedWeights AS (
            SELECT
                patient_id,-- 转换为数值类型并处理异常值
                CAST(
                        NULLIF( dialyze_before_weight, '' ) AS DECIMAL ( 10, 2 )) AS before_weight,
                CAST(
                        NULLIF( dialyze_after_weight, '' ) AS DECIMAL ( 10, 2 )) AS after_weight,
                register_time,-- 按时间倒序编号（最新记录rn=1）
                ROW_NUMBER() OVER ( PARTITION BY patient_id ORDER BY register_time DESC ) AS rn,-- 获取前次透后体重（当次记录的上一条）
                    LAG( CAST( NULLIF( dialyze_after_weight, '' ) AS DECIMAL ( 10, 2 )), 1 ) OVER ( PARTITION BY patient_id ORDER BY register_time ASC ) AS prev_after_weight
            FROM
                wl_reach_weigh
            WHERE
                deleted = 0 -- 严格过滤数字格式
        <if test="req.endTime != null">
           and register_time <![CDATA[ <=]]> #{req.endTime,jdbcType=TIMESTAMP}
        </if>
              AND dialyze_before_weight REGEXP '^[0-9]+(\.[0-9]+)?$'
            AND dialyze_after_weight REGEXP '^[0-9]+(\.[0-9]+)?$'
            )
    </sql>

    <select id="getComprehensiveIndicatorsStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT count(*) count,
        dept_id as centerId
        FROM
        (
        SELECT
        info1.json_value As json_value1, info1.json_key AS json_key1,
        info2.json_value AS json_value2, info2.json_key AS json_key2,
        a.growth_percent,wp.dept_id
        FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info1 on info1.patient_id = wp.id

        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info2 on info2.patient_id = wp.id

        LEFT JOIN (
        SELECT
        b.patient_id,
        b.growth_percent
        FROM
        (SELECT
        patient_id,
        before_weight,
        prev_after_weight,-- 计算增长率（带安全校验）
        ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent
        FROM
        RankedWeights
        WHERE
        rn = 1 -- 取最新两条记录
        )b) a ON a.patient_id = wp.id

        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        </where>
        ) info
        <where>
            <if test="req.range == 2">
                <if test="req.standardValueMax1 != null">AND info.json_value1 &gt; #{req.standardValueMax1,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMax2 != null">AND REPLACE(info.json_value2, '%', '') &gt;= #{req.standardValueMax2,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMax3 != null">AND info.growth_percent &lt; #{req.standardValueMax3,jdbcType=DOUBLE}</if>
            </if>

            <if test="req.range == 4">
                OR info.json_value1 IS NULL or info.json_value1 = ''
                OR info.json_value2 IS NULL or info.json_value2 = ''
                OR info.growth_percent IS NULL or info.growth_percent = ''
            </if>
            <if test="req.range == 5 ">
                <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and info.json_value1 &lt;= #{req.standardValueMax1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and REPLACE(info.json_value2, '%', '') &lt; #{req.standardValueMax2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and info.growth_percent &gt;= #{req.standardValueMax3,jdbcType=DOUBLE})</if>
            </if>
            <if test="req.range == 6">
                AND (info.json_value1 like '%NaN%' or info.json_value2 like '%NaN%')
            </if>
        </where>
        group by centerId
    </select>

    <select id="getWeightGainCount"  resultType="java.lang.Long">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT count(*)
        FROM
        ( select a.growth_percent FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
            SELECT
            patient_id,
            before_weight,
            prev_after_weight,-- 计算增长率（带安全校验）
            ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent FROM
            RankedWeights
            WHERE
            rn = 1 -- 取最新两条记录
            )a ON a.patient_id = wp.id
            where wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        ) info
        <where>
            <if test="req.range == 1">
                <if test="req.standardValueMax != null">AND info.growth_percent &gt;= #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 2">
                <if test="req.standardValueMax != null">AND info.growth_percent &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 4">
                OR info.growth_percent IS NULL or info.growth_percent = ''
            </if>
        </where>
    </select>

    <select id="getWeightGainPage"  resultType="java.util.Map">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        info.*
        FROM
        (
        select
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        a.growth_percent as jsonValue FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT
        patient_id,
        before_weight,
        prev_after_weight,-- 计算增长率（带安全校验）
        ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent FROM
        RankedWeights
        WHERE
        rn = 1 -- 取最新两条记录
        )a ON a.patient_id = wp.id
        where wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        ) info
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <if test="req.range == 1">
                <if test="req.standardValueMax != null">AND info.jsonValue &gt;= #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 2">
                <if test="req.standardValueMax != null">AND info.jsonValue &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 4">
                OR info.jsonValue IS NULL or info.jsonValue = ''
            </if>
        </where>
    </select>

    <select id="getWeightGainStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT count(*) as count,
        dept_id as centerId
        FROM
        ( select wp.dept_id,a.growth_percent FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT
        patient_id,
        before_weight,
        prev_after_weight,-- 计算增长率（带安全校验）
        ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent FROM
        RankedWeights
        WHERE
        rn = 1 -- 取最新两条记录
        )a ON a.patient_id = wp.id
        where wp.deleted = 0
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        ) info
        <where>
            <if test="req.range == 1">
                <if test="req.standardValueMax != null">AND info.growth_percent &gt;= #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 2">
                <if test="req.standardValueMax != null">AND info.growth_percent &lt; #{req.standardValueMax,jdbcType=DOUBLE}</if>
            </if>
            <if test="req.range == 4">
                OR info.growth_percent IS NULL or info.growth_percent = ''
            </if>
        </where>
        group by centerId
    </select>

    <select id="getComprehensiveIndicatorsCount" resultType="java.lang.Long">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT count(*) count
        FROM
        (
        SELECT
        info1.json_value As json_value1, info1.json_key AS json_key1,
        info2.json_value AS json_value2, info2.json_key AS json_key2,
        a.growth_percent
        FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info1 on info1.patient_id = wp.id

        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info2 on info2.patient_id = wp.id

        LEFT JOIN (
        SELECT
        b.patient_id,
        b.growth_percent
        FROM
        (SELECT
        patient_id,
        before_weight,
        prev_after_weight,-- 计算增长率（带安全校验）
        ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent
        FROM
        RankedWeights
        WHERE
        rn = 1 -- 取最新两条记录
        )b) a ON a.patient_id = wp.id

        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        </where>
        ) info
        <where>
            <if test="req.range == 2">
                <if test="req.standardValueMax1 != null">AND info.json_value1 &gt; #{req.standardValueMax1,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMax2 != null">AND REPLACE(info.json_value2, '%', '') &gt;= #{req.standardValueMax2,jdbcType=DOUBLE}</if>
                <if test="req.standardValueMax3 != null">AND info.growth_percent &lt; #{req.standardValueMax3,jdbcType=DOUBLE}</if>
            </if>

            <if test="req.range == 4">
                OR info.json_value1 IS NULL or info.json_value1 = ''
                OR info.json_value2 IS NULL or info.json_value2 = ''
                OR info.growth_percent IS NULL or info.growth_percent = ''
            </if>
            <if test="req.range == 5 ">
                <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and info.json_value1 &lt;= #{req.standardValueMax1,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and REPLACE(info.json_value2, '%', '') &lt; #{req.standardValueMax2,jdbcType=DOUBLE})</if>
                <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like '%NaN%' and info.growth_percent &gt;= #{req.standardValueMax3,jdbcType=DOUBLE})</if>
            </if>
            <if test="req.range == 6">
                AND (info.json_value1 like '%NaN%' or info.json_value2 like '%NaN%')
            </if>
        </where>
    </select>

    <select id="getComprehensiveIndicatorsPage" resultType="java.util.Map">
        <include refid="com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper.preComprehensiveIndicators"/>
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        info.*
        FROM
        (
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        info1.json_value As json_value1, info1.json_key AS json_key1, DATE_FORMAT( info1.check_time, '%Y-%m-%d' ) AS
        checkTime1
        , info2.json_value AS json_value2, info2.json_key AS json_key2, DATE_FORMAT( info2.check_time, '%Y-%m-%d' ) AS
        checkTime2,a.growth_percent
        FROM
        wl_patient wp
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_value, info.json_key, info.project_id, info.check_time
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId1 != null">AND json_key = #{req.itemId1,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN
            #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info1 on info1.patient_id = wp.id

        LEFT JOIN (
        SELECT wrp.id, wrp.patient_id, info.json_key, info.json_value, info.project_id, info.check_time
        FROM `wl_renal_project` wrp
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        JOIN (
        SELECT max( id ) maxId FROM wl_renal_project_info WHERE deleted = 0
        AND json_value is not null AND json_value != ''
        <if test="req.itemId2 != null">AND json_key = #{req.itemId2,jdbcType=BIGINT}</if>
        <if test="req.startTime != null and req.endTime != null">AND check_time BETWEEN
            #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        GROUP BY wrp.patient_id, info.json_key
        ) info2 on info2.patient_id = wp.id

        LEFT JOIN (
        SELECT
        b.patient_id,
        b.growth_percent
        FROM
        (SELECT
        patient_id,
        before_weight,
        prev_after_weight,-- 计算增长率（带安全校验）
        ROUND( ( before_weight - prev_after_weight ) * 100.0 / NULLIF( before_weight, 0 ), 2 ) AS growth_percent
        FROM
        RankedWeights
        WHERE
        rn = 1 -- 取最新两条记录
        )b) a ON a.patient_id = wp.id

        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        </where>
        ) info
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <if test="req.range == 2">
                <if test="req.standardValueMax1 != null">AND info.json_value1 &gt;
                    #{req.standardValueMax1,jdbcType=DOUBLE}
                </if>
                <if test="req.standardValueMax2 != null">AND REPLACE(info.json_value2, '%', '') &gt;=
                    #{req.standardValueMax2,jdbcType=DOUBLE}
                </if>
                <if test="req.standardValueMax3 != null">AND info.growth_percent &lt;
                    #{req.standardValueMax3,jdbcType=DOUBLE}
                </if>
            </if>

            <if test="req.range == 4">
                OR info.json_value1 IS NULL or info.json_value1 = ''
                OR info.json_value2 IS NULL or info.json_value2 = ''
                OR info.growth_percent IS NULL or info.growth_percent = ''
            </if>
            <if test="req.range == 5 ">
                <if test="req.standardValueMax1 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and
                    info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and
                    info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like
                    '%NaN%' and info.json_value1 &lt;= #{req.standardValueMax1,jdbcType=DOUBLE})
                </if>
                <if test="req.standardValueMax2 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and
                    info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and
                    info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like
                    '%NaN%' and REPLACE(info.json_value2, '%', '') &lt; #{req.standardValueMax2,jdbcType=DOUBLE})
                </if>
                <if test="req.standardValueMax3 != null">or (info.json_value1 IS NOT NULL and info.json_value1 != '' and
                    info.json_value2 IS NOT NULL and info.json_value2 != '' and info.growth_percent IS NOT NULL and
                    info.growth_percent != '' and info.json_value1 not like '%NaN%' and info.json_value2 not like
                    '%NaN%' and info.growth_percent &gt;= #{req.standardValueMax3,jdbcType=DOUBLE})
                </if>
            </if>
            <if test="req.range == 6">
                AND (info.json_value1 like '%NaN%' or info.json_value2 like '%NaN%')
            </if>
        </where>
    </select>

    <select id="getDecisionAnalysis" resultType="java.util.Map">
        SELECT
            wp.`name` AS patientName, GROUP_CONCAT( info.json_value) AS result
            <if test="req.type != null">, GROUP_CONCAT(DATE_FORMAT(info.`create_time`, '%Y-%m-%d')) AS time</if>
        FROM
            wl_patient wp
        LEFT JOIN wl_renal_project wrp ON wp.id = wrp.patient_id
        LEFT JOIN wl_renal_project_info info ON wrp.id = info.project_id
        <where>
            wp.deleted = '0' AND info.deleted = '0'
            <if test="req.patientName != null and req.patientName != ''"> AND wp.`name` LIKE %#{req.patientName}% </if>
            <if test="req.patientId != null">AND wp.id = #{req.patientId,jdbcType=BIGINT}</if>
            <if test="req.dictId != null">AND wrp.dict_id = #{req.dictId,jdbcType=BIGINT}</if>
            <if test="req.jsonKey != null">AND info.json_key = #{req.jsonKey,jdbcType=BIGINT}</if>
        </where>
        GROUP BY wrp.patient_id
    </select>

    <select id="getCheckByIds" resultType="java.util.Map">
        SELECT wrp.patient_id, GROUP_CONCAT( item.label ) AS labels, GROUP_CONCAT( item.prop ) AS props, GROUP_CONCAT(
        info.json_value ) AS jsonValues, MAX( info.check_time) AS checkTime
        FROM
        wl_renal_project wrp
        LEFT JOIN wl_renal_project_info info ON info.project_id = wrp.id
        LEFT JOIN wl_renal_project_item item ON info.json_key = item.id
        JOIN (
        SELECT max( id ) AS maxId
        FROM wl_renal_project_info info
        <where>
            AND info.json_value is not null AND info.json_value != ''
            <if test="req.itemIdList != null and req.itemIdList.size() > 0">
                AND info.json_key IN
                <foreach collection="req.itemIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN
                #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        GROUP BY project_id, json_key
        ) temp ON temp.maxId = info.id
        <where>
            <if test="idList != null and idList.size() > 0">
                wrp.patient_id IN
                <foreach collection="idList" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY
        wrp.patient_id
    </select>

    <select id="getAfterCheckByIds" resultType="java.util.Map">
        SELECT wrp.patient_id, GROUP_CONCAT( item.label ) AS labels, GROUP_CONCAT( item.prop ) AS props, GROUP_CONCAT(
        info.json_value ) AS jsonValues, MAX( info.check_time) AS checkTime
        FROM
        wl_renal_project wrp
        LEFT JOIN wl_renal_project_info info ON info.project_id = wrp.id
        LEFT JOIN wl_renal_project_item item ON info.json_key = item.id
        JOIN ( select id,check_time
        FROM wl_renal_project_info info
        <where>
            <if test="req.itemIdList != null and req.itemIdList.size() > 0">
                AND info.json_key IN
                <foreach collection="req.itemIdList" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        ) temp ON temp.id = info.id
        <where>
            <foreach item="checkItemDTO" collection="checkItemDTOList" separator=" OR ">
                (
                <if test="checkItemDTO.id != null">wrp.patient_id = #{checkItemDTO.id}</if>
                AND temp.check_time BETWEEN #{checkItemDTO.startTime} AND #{checkItemDTO.endTime}
                )
            </foreach>
        </where>
        GROUP BY
        wrp.patient_id
    </select>


    <select id="getPatienSummaryResultPage" resultType="java.util.Map">
        SELECT  <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                temp.*
        FROM wl_patient wp
        LEFT JOIN (
            SELECT wrp.patient_id, GROUP_CONCAT( item.label ) AS labels, GROUP_CONCAT( item.prop ) AS props, GROUP_CONCAT( info.json_value ) AS jsonValues, MAX( info.check_time) AS checkTime
            FROM
                wl_renal_project wrp
            LEFT JOIN wl_renal_project_info info ON info.project_id = wrp.id
            LEFT JOIN wl_renal_project_item item ON info.json_key = item.id
            JOIN (
                SELECT max( id ) AS maxId
                FROM wl_renal_project_info info
                <where>
                    AND info.json_value is not null AND info.json_value != ''
                    <if test="req.itemIdList != null and req.itemIdList.size() > 0">
                        AND info.json_key IN
                        <foreach collection="req.itemIdList" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach>
                    </if>
                    <if test="req.startTime != null and req.endTime != null">AND info.check_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                </where>
                GROUP BY project_id, json_key
            ) temp ON temp.maxId = info.id
            GROUP BY
                wrp.patient_id
        ) temp ON temp.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
        </where>
        ORDER BY wp.dialyze_no
    </select>
    <select id="getProjectItem" resultType="java.util.Map">
        SELECT rpi.*
        FROM wl_renal_project_info rpi
        WHERE rpi.project_id = (
            SELECT MAX(rp.id) AS id
            FROM wl_renal_project rp
                     JOIN system_dict_data sdd ON rp.dict_id = sdd.id
            WHERE sdd.label = #{req.projectName}
              AND rp.patient_id = #{patientId}
              AND rp.deleted = 0
        )
            AND rpi.project_type = (
            SELECT MAX(rpi_inner.project_type)
            FROM wl_renal_project_info rpi_inner
                WHERE rpi_inner.project_id = (
                    SELECT MAX(rp_inner.id) AS id
                    FROM wl_renal_project rp_inner
                             JOIN system_dict_data sdd_inner ON rp_inner.dict_id = sdd_inner.id
                    WHERE sdd_inner.label = #{req.projectName}
                      AND rp_inner.patient_id = #{patientId}
                      AND rp_inner.deleted = 0
                )
                AND rpi_inner.check_time BETWEEN #{req.startTime} AND #{req.endTime}
            )
          AND rpi.check_time BETWEEN #{req.startTime} AND #{req.endTime}
        </select>
    <select id="selectTimeList" resultType="com.thj.boot.module.business.dal.datado.renalproject.RenalProjectDO">
        SELECT
            w.id,
            MAX(info.min_check_time) AS last_Check_Time  -- 直接取最大检查时间
        FROM wl_renal_project w
                 LEFT JOIN (
            SELECT
                project_id,
                MIN(check_time) AS min_check_time  -- 每类的最小检查时间
            FROM wl_renal_project_info
            WHERE project_id IN (
                SELECT id FROM wl_renal_project WHERE patient_id = #{patientId}  -- 先过滤患者项目
            )
            GROUP BY project_id, project_type
        ) info ON w.id = info.project_id
        WHERE w.patient_id = #{patientId}
        GROUP BY w.id;
    </select>

</mapper>

