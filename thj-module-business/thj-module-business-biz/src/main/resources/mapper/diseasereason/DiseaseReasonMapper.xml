<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.diseasereason.DiseaseReasonMapper">

    <select id="getPatientPathologicDiagnosis" resultType="java.util.Map">
        SELECT
        COUNT( wdr.id ) AS count, parentId, parentTwoId
        FROM
        (
            SELECT wdr.id, wdr.parent_id AS parentId, wdr.parent_two_id AS parentTwoId
            FROM wl_patient wp
            LEFT JOIN `wl_disease_reason` wdr ON wp.id = wdr.patient_id
            <where>
                wp.deleted = 0 AND wdr.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                <if test="req.startTime != null and req.endTime != null">AND wdr.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <if test="req.parentOneId != null and req.parentOneId != ''">AND wdr.parent_id = #{req.parentOneId,jdbcType=BIGINT}</if>
                <if test="req.parentTwoId != null and req.parentTwoId != ''">AND wdr.parent_two_id = #{req.parentTwoId,jdbcType=BIGINT}</if>
            </where>
            GROUP BY wdr.parent_id, wdr.patient_id
        ) wdr
        GROUP BY parentId
    </select>
    <select id="getPatientProtopathyDiagnosis" resultType="java.util.Map">
        SELECT
        COUNT( wdr.id ) AS count, parentId, parentTwoId
        FROM
        (
            SELECT wdr.id, wdr.parent_id AS parentId, wdr.parent_two_id AS parentTwoId
            FROM wl_patient wp
            LEFT JOIN `wl_disease_reason` wdr ON wp.id = wdr.patient_id
            <where>
                wp.deleted = 0 AND wdr.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
                <if test="req.startTime != null and req.endTime != null">AND wdr.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <if test="req.parentOneId != null and req.parentOneId != ''">AND wdr.parent_id = #{req.parentOneId,jdbcType=BIGINT}</if>
                <if test="req.parentTwoId != null and req.parentTwoId != ''">AND wdr.parent_two_id = #{req.parentTwoId,jdbcType=BIGINT}</if>
            </where>
            GROUP BY wdr.parent_two_id, wdr.patient_id
        ) wdr
        GROUP BY parentTwoId
    </select>

    <select id="getInfoPageResult" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        wdr.*, GROUP_CONCAT( DISTINCT wdr.parentId ) AS parentIdList, GROUP_CONCAT(DISTINCT  wdr.parentOneName ) AS parentOneNames, GROUP_CONCAT( DISTINCT wdr.parentTwoId ) AS parentTwoIdList, GROUP_CONCAT( DISTINCT wdr.parentTwoName ) AS parentTwoNames
        FROM
        (
            SELECT
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
<!--                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>-->
                wdr.parent_id AS parentId, wr1.`name` parentOneName, wdr.parent_two_id AS parentTwoId, wr2.`name` AS parentTwoName,
                CASE
                WHEN wp.patient_type_source = '00' THEN '留治'
                WHEN wp.patient_type_source = '01' THEN '转出'
                ELSE wp.patient_type_source
                END AS patientStatus
            FROM wl_patient wp
            LEFT JOIN `wl_disease_reason` wdr ON wp.id = wdr.patient_id
            LEFT JOIN wl_reason wr1 ON wr1.id = wdr.parent_id
            LEFT JOIN wl_reason wr2 ON wr2.id = wdr.parent_two_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            <where>
                wp.deleted = 0 AND wdr.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                <if test="req.startTime != null and req.endTime != null">AND wdr.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <if test="req.parentOneId != null and req.parentOneId != ''">AND wdr.parent_id = #{req.parentOneId,jdbcType=BIGINT}</if>
                <if test="req.parentTwoId != null and req.parentTwoId != ''">AND wdr.parent_two_id = #{req.parentTwoId,jdbcType=BIGINT}</if>
            </where>
        ) wdr
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        GROUP BY patientId
    </select>
</mapper>
