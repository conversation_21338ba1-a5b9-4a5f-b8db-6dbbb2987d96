<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper">


    <select id="getDialysisTotalTime" resultType="com.thj.boot.module.business.pojo.departmentControl.DialysisTotalTimeVo">
        SELECT
            wdm.*
        FROM
            ( SELECT patient_id as patientId, max( create_time ) AS maxTime, min( create_time ) AS minTime FROM wl_dialysis_manager WHERE deleted = 0 GROUP BY patient_id ) wdm
        LEFT JOIN wl_patient wp ON wdm.patientId = wp.id
        <where>
            <if test="req.startTime != null and req.endTime != null">AND wdm.maxTime BETWEEN #{req.startTime} AND #{req.endTime}</if>
        </where>
    </select>

    <select id="getBpResultPage" resultType="java.util.Map">
        SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
            wdm.id as id, wdm.create_time as createTime, wdm.before_evaluate as beforeEvaluate, wdm.patient_id as patientId, wp.age, wp.dialyze_no as dialyzeNo, wp.dialyze_age as dialyzeAge
        <if test="req.chartType != null and req.chartType == 3">
            , json_extract(wdm.before_evaluate, '$.dryWeight') as dryWeight,
            json_extract(wdm.before_evaluate, '$.ultrafiltrationTotal') as ultrafiltrationTotal,
            json_extract(wdm.before_evaluate, '$.actualUltrafiltrationCapacity') as actualUltrafiltrationCapacity,
            json_extract(wdm.before_evaluate, '$.bpNoOne') as bpNoOne,
            json_extract(wdm.before_evaluate, '$.bpNoTwo') as bpNoTwo,
            temp4.ultrafiltrationRate
        </if>
        FROM wl_dialysis_manager wdm
        LEFT JOIN wl_patient wp ON wdm.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <if test="req.chartType != null and req.chartType == 3">
            LEFT JOIN (
                SELECT wdd.id, wdd.patient_id AS patientId, wdd.ultrafiltration_rate AS ultrafiltrationRate FROM wl_dialysis_detection wdd
                JOIN (
                    SELECT  MAX(id) as id FROM wl_dialysis_detection  GROUP BY patient_id
                ) temp3 ON temp3.id = wdd.id
            ) temp4 ON wdm.patient_id = temp4.patientId
        </if>
         <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        where
        wdm.deleted = '0' AND wp.deleted = '0'
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
        <if test="req.bpStatus != null and req.bpStatus == 1">
            AND (wp.age &lt; 60 AND json_extract(wdm.before_evaluate, '$.bpNoOne') &lt; 140 AND json_extract(wdm.before_evaluate, '$.bpNoTwo') &lt; 90)
            AND (wp.age &gt;= 60 AND json_extract(wdm.before_evaluate, '$.bpNoOne') &lt; 160 AND json_extract(wdm.before_evaluate, '$.bpNoTwo') &lt; 90)
        </if>
        <if test="req.bpStatus != null and req.bpStatus == 2">
            AND (wp.age &lt; 60 AND json_extract(wdm.before_evaluate, '$.bpNoOne') &lt; 140 AND json_extract(wdm.before_evaluate, '$.bpNoTwo') &lt; 90)
            OR (wp.age &gt;= 60 AND json_extract(wdm.before_evaluate, '$.bpNoOne') &lt; 160 AND json_extract(wdm.before_evaluate, '$.bpNoTwo') &lt; 90)
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>
</mapper>
