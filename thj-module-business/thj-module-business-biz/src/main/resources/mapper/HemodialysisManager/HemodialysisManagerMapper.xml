<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper">


    <select id="getBeforeCount" resultType="com.thj.boot.module.business.service.departmentControl.vo.HemodialysisManagerCountVo">
        SELECT JSON_EXTRACT(${req.sql}) as before_info,whm.hemodialysis_time, whm.patient_id
        FROM  wl_hemodialysis_manager whm
        LEFT JOIN  wl_patient wp ON wp.id = whm.patient_id
        <if test="req.roadType != null and req.roadType != 0">
            JOIN wl_blood_road wbr on wp.id = wbr.patient_id
            JOIN wl_vascular_access wva ON wva.id = wbr.type
        </if>

        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        WHERE 1=1
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time &gt;= #{req.startTime} AND whm.hemodialysis_time &lt;= #{req.endTime}</if>
        <if test="req.roadType != null and req.roadType != 0">and wva.access_state = #{req.roadType}</if>
    </select>

    <select id="getDuringSymptomCount"
            resultType="com.thj.boot.module.business.service.departmentControl.vo.HemodialysisManagerCountVo">
        SELECT
        temp.*
        FROM
        (
            SELECT
                wdd.id, JSON_EXTRACT( wdd.symptom, '${req.sql}' ) AS beforeInfo, whm.patient_id AS patientId
            FROM wl_patient wp
            LEFT JOIN wl_hemodialysis_manager whm ON (wp.id = whm.patient_id)
            LEFT JOIN wl_dialysis_detection wdd ON (wdd.patient_id = whm.patient_id AND DATE( wdd.date_week ) = DATE( whm.hemodialysis_time ))
            <if test="req.roadType != null and req.roadType != 0">
                LEFT JOIN wl_blood_road wbr on wp.id = wbr.patient_id
                LEFT JOIN wl_vascular_access wva ON wva.id = wbr.type
            </if>
            WHERE whm.deleted = 0 AND wdd.deleted = 0
            <if test="req.startTime != null and req.endTime != null">
                AND whm.hemodialysis_time &gt;= #{req.startTime}
                AND whm.hemodialysis_time &lt;= #{req.endTime}
            </if>
            <if test="req.roadType != null and req.roadType != 0">and wva.access_state = #{req.roadType}</if>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        ) temp
        WHERE  JSON_LENGTH( temp.beforeInfo ) > 0
    </select>

    <select id="getDuringSymptomPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            JSON_EXTRACT( wdd.symptom, '${req.sql}' ) AS motto,
            whm.dialyze_way_value AS dialyzeWayValue,whm.child_dialyze_way_value AS childDialyzeWayValue,DATE(whm.hemodialysis_time) AS hemodialysisTime,
            wac.state
        FROM wl_patient wp
        LEFT JOIN wl_hemodialysis_manager whm ON (wp.id = whm.patient_id)
        LEFT JOIN wl_dialysis_detection wdd ON (wdd.patient_id = whm.patient_id AND DATE( wdd.date_week ) = DATE( whm.hemodialysis_time ))
        LEFT JOIN wl_arrange_classes wac ON (wac.patient_id = whm.patient_id AND DATE(wac.classes_time) = DATE(whm.hemodialysis_time))
        <if test="req.roadType != null and req.roadType != 0">
            LEFT JOIN wl_blood_road wbr on wp.id = wbr.patient_id
            LEFT JOIN wl_vascular_access wva ON wva.id = wbr.type
        </if>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        WHERE whm.deleted = 0 AND wdd.deleted = 0
        <if test="req.startTime != null and req.endTime != null">
            AND whm.hemodialysis_time &gt;= #{req.startTime,jdbcType=TIMESTAMP}
            AND whm.hemodialysis_time &lt;= #{req.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="req.chartName != null and req.chartName != ''">AND JSON_EXTRACT( wdd.symptom, '${req.sql}' ) like CONCAT('%',#{req.chartName},'%')</if>
        <if test="req.roadType != null and req.roadType != 0">and wva.access_state = #{req.roadType}</if>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
    </select>


    <select id="getDialyzeOptionList" resultType="java.util.Map">
<!--        select IFNULL(dialyze_dict_value ,0) as dialyze_dict_value,-->
<!--               IFNULL(cycle_dict_value ,0) as cycle_dict_value,-->
<!--               IFNULL(number_dict_value ,0) as number_dict_value,-->
<!--               COUNT(*) as count-->
<!--        from wl_dialyze_option-->
<!--        LEFT JOIN wl_patient wp ON wp.id = wdo.patient_id-->
<!--        <where>-->
<!--            wp.deleted = '0' AND wdo.deleted = '0'-->
<!--            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientDepts"/>-->
<!--        </where>-->
<!--        group by dialyze_dict_value, cycle_dict_value,number_dict_value-->

        SELECT
            COUNT(*) AS count, wdo.cycleDictValue, numberDictValue
        FROM wl_patient wp
        RIGHT JOIN (
            SELECT wdo.patient_id, wdo.dialyze_dict_value AS dialyzeDictValue, wdo.cycle_dict_value AS cycleDictValue, wdo.number_dict_value AS numberDictValue
            FROM wl_dialyze_option wdo
            JOIN (
                SELECT MAX(wdo.id) AS maxId FROM wl_dialyze_option wdo
                <where>
                    wdo.deleted = 0
                </where>
                GROUP BY wdo.patient_id
            ) temp ON wdo.id = temp.maxId
        ) wdo ON wdo.patient_id = wp.id
        <where>
            wp.deleted = 0 AND wdo.cycleDictValue IS NOT NULL AND wdo.numberDictValue IS NOT NULL
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        </where>
        GROUP BY wdo.cycleDictValue, wdo.numberDictValue
    </select>

    <select id="getDialyzeOptionPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        wdo.*
        FROM wl_patient wp
        RIGHT JOIN (
            SELECT wdo.patient_id, wdo.dialyze_dict_value AS dialyzeDictValue, wdo.cycle_dict_value AS cycleDictValue, wdo.number_dict_value AS numberDictValue
            FROM wl_dialyze_option wdo
            JOIN (
                SELECT MAX(wdo.id) AS maxId FROM wl_dialyze_option wdo
                WHERE
                    wdo.deleted = 0
                GROUP BY wdo.patient_id
            ) temp ON wdo.id = temp.maxId
        <where>

            <if test="req.cycleDictValue != null and req.cycleDictValue != ''">AND wdo.cycle_dict_value = #{req.cycleDictValue}</if>
            <if test="req.numberDictValue != null and req.numberDictValue != ''">AND wdo.number_dict_value = #{req.numberDictValue}</if>
        </where>
        ) wdo ON wdo.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        </where>
    </select>

    <select id="getContradictCount" resultType="com.thj.boot.module.business.service.departmentControl.vo.ContradictCountVo">
        SELECT wc.drug_type_id, count(drug_type_id) as cunt
        FROM  wl_contradict wc
        LEFT JOIN wl_patient wp ON (wp.id = wc.patient_id)
        JOIN
        (
            SELECT
                whm.id, whm.patient_id, whm.vascular_access_two, whm.hemodialysis_time, whm.dialyze_way_value, wva.access_type
        FROM `wl_hemodialysis_manager` whm
            JOIN
            (
                SELECT whm.id FROM `wl_hemodialysis_manager` whm
                WHERE
                    whm.deleted = 0
                    <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            ) temp
            ON temp.id = whm.id
            LEFT JOIN wl_vascular_access wva ON wva.deleted = 0 AND whm.vascular_access_two = wva.id
        ) whm
        ON (wp.id = whm.patient_id AND DATE(wc.hemodialysis_time) = DATE(whm.hemodialysis_time))
        WHERE wc.protocol_type = 2
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        <if test="req.startTime != null and req.endTime != null">AND wc.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        <if test="req.roadType != null">
            <choose>
                <when test="req.roadType == 1">AND whm.access_type IN (3, 4)</when>
                <when test="req.roadType == 2">AND whm.access_type IN (1, 2)</when>
            </choose>
        </if>
        GROUP BY wc.drug_type_id
    </select>

    <select id="getContradictPage" resultType="java.util.Map">

        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        wc.drug_type_id AS drugTypeId, wc.first_dose_value AS firstDoseValue, wc.first_method_value, CONCAT(IFNULL(wc.first_dose_value, 0), '/', IFNULL( wc.first_method_number, 0), '/', IFNULL( wc.opportunity, 0), '/', IFNULL( wc.total_value, 0)) result, DATE(wc.hemodialysis_time) AS hemodialysisTime,
        whm.dialyze_way_value AS dialyzeWayValue, whm.access_type
        FROM wl_contradict wc
        LEFT JOIN wl_patient wp ON (wp.id = wc.patient_id)
        JOIN
        (
            SELECT
                whm.id, whm.patient_id, whm.vascular_access_two, whm.hemodialysis_time, wva.access_type, whm.dialyze_way_value
            FROM `wl_hemodialysis_manager` whm
            JOIN
            (
                SELECT whm.id FROM `wl_hemodialysis_manager` whm
                WHERE
                whm.deleted = 0
                <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            ) temp
            ON temp.id = whm.id
            LEFT JOIN wl_vascular_access wva ON wva.deleted = 0 AND whm.vascular_access_two = wva.id
        ) whm
        ON (wp.id = whm.patient_id AND DATE(wc.hemodialysis_time) = DATE(whm.hemodialysis_time))
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        WHERE wc.protocol_type = 2
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
        <if test="req.drugTypeId != null">AND wc.drug_type_id = #{req.drugTypeId}</if>
        <if test="req.startTime != null and req.endTime != null">AND wc.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        <if test="req.roadType != null">
            <choose>
                <when test="req.roadType == 1">AND whm.access_type IN (3, 4)</when>
                <when test="req.roadType == 2">AND whm.access_type IN (1, 2)</when>
            </choose>
        </if>
    </select>



    <select id="getBeforeSymptomPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/> temp.*
        FROM (
            SELECT
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
                whm.dialyze_way_value AS dialyzeWayValue,whm.child_dialyze_way_value AS childDialyzeWayValue, JSON_EXTRACT(${req.sql}) AS motto,DATE(whm.hemodialysis_time) AS hemodialysisTime
            FROM  wl_hemodialysis_manager whm
            LEFT JOIN wl_patient wp on whm.patient_id = wp.id
            <if test="req.roadType != null and req.roadType != 0">
                JOIN wl_blood_road wbr on wp.id = wbr.patient_id
                JOIN wl_vascular_access wva ON wva.id = wbr.type
            </if>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            WHERE 1=1
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
            <if test="req.sql != null and req.chartName !=null">
                and JSON_EXTRACT(${req.sql}) like CONCAT('%',#{req.chartName},'%')
            </if>
            <if test="req.startTime != null">and whm.hemodialysis_time &gt;= #{req.startTime}</if>
            <if test="req.endTime != null">and whm.hemodialysis_time &lt;= #{req.endTime}</if>
    <!--        /*通路类型*/-->
            <if test="req.roadType != null and req.roadType != 0">and wva.access_state = #{req.roadType}</if>
            ORDER BY whm.hemodialysis_time DESC
        ) temp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
    </select>

    <select id="getVolumeAnalysis" resultType="java.util.Map">
        <if test="req.dataSource == null || req.dataSource == 1">
            SELECT
            whm.*, COUNT(whm.dialysisWayValue) AS count
            FROM (
                SELECT
                    whm.dialyze_way_value as dialysisWayValue, whm.patient_id AS patientId,
                    DATE_FORMAT( whm.hemodialysis_time, #{req.format,jdbcType=VARCHAR} ) fomate,
                    whm.hemodialysis_time AS hemodialysisTime
                FROM
                    wl_hemodialysis_manager whm
                LEFT JOIN wl_patient wp ON whm.patient_id = wp.id
                <if test="req.dataSource  == 2">RIGHT JOIN wl_arrange_classes wac ON (wac.patient_id = wp.id AND DATE_FORMAT( wac.classes_time, '%Y-%m-%d' ) = DATE_FORMAT( whm.hemodialysis_time, '%Y-%m-%d' ))</if>
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
                WHERE whm.deleted = 0 AND whm.dialyze_way_value IS NOT NULL AND whm.heal_state =1
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus1"/>
                    <if test="req.startTime != null and req.endTime != null">AND (whm.create_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
                    <if test="req.dialyzeWay != null and req.dialyzeWay != ''">AND whm.dialyze_way_value = #{req.dialyzeWay}</if>
                    <if test="req.dataSource  == 2">AND wac.start_dialyze_time IS NOT NULL</if>
                    <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
            ) whm
            GROUP BY whm.dialysisWayValue
            <if test="req.dialyzeWay != null">, whm.fomate</if>
        </if>

        <if test="req.dataSource == 2">
            SELECT
            whm.*, COUNT(whm.dialysisWayValue) AS count
            FROM (
                SELECT
                    wac.dialysis_value as dialysisWayValue, wac.patient_id AS patientId,
                    DATE_FORMAT( wac.classes_time, #{req.format,jdbcType=VARCHAR} ) fomate,
                    wac.classes_time AS hemodialysisTime
                FROM
                    wl_arrange_classes wac
                LEFT JOIN wl_patient wp ON wac.patient_id = wp.id
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
                WHERE wac.deleted = 0 AND wp.deleted = 0 AND wac.dialysis_value IS NOT NULL
                    <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus1"/>
                    <if test="req.startTime != null and req.endTime != null">AND (wac.classes_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
                    <if test="req.dialyzeWay != null and req.dialyzeWay != ''">AND wac.dialysis_value = #{req.dialyzeWay}</if>
                    <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
            ) whm
            GROUP BY whm.dialysisWayValue
        </if>

    </select>
    <select id="getVolumeAnalysisPage" resultType="java.util.Map">
        <if test="req.dataSource  == 1">
        SELECT
        hemodialysisTime,
        GROUP_CONCAT(fomate) AS timeList,
        GROUP_CONCAT(dialysisWay) AS labelList,
        GROUP_CONCAT(count) AS countList
        FROM (
            SELECT
                COUNT( whm.dialyze_way_value ) AS count,
                whm.dialyze_way_value AS dialysisWay,
                DATE_FORMAT( whm.hemodialysis_time, #{req.format,jdbcType=VARCHAR} ) fomate,
                whm.hemodialysis_time AS hemodialysisTime
            FROM
                wl_hemodialysis_manager whm
            LEFT JOIN wl_patient wp ON whm.patient_id = wp.id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            WHERE whm.deleted = 0 AND wp.deleted = 0 AND whm.dialyze_way_value IS NOT NULL and whm.heal_state = 1
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus1"/>
            <if test="req.startTime != null and req.endTime != null">AND (whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
            <if test="req.dialyzeWay != null and req.dialyzeWay != ''">AND whm.dialyze_way_value = #{req.dialyzeWay}</if>
            <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
            GROUP BY fomate, whm.dialyze_way_value
        ) whm
        GROUP BY whm.fomate
        </if>
        <if test="req.dataSource == 2">
        SELECT
        hemodialysisTime,
        GROUP_CONCAT(fomate) AS timeList,
        GROUP_CONCAT(dialysisWay) AS labelList,
        GROUP_CONCAT(count) AS countList
        FROM (
            SELECT
                COUNT( wac.dialysis_value ) AS count,
                wac.dialysis_value AS dialysisWay,
                DATE_FORMAT( wac.classes_time, #{req.format,jdbcType=VARCHAR} ) fomate,
                wac.classes_time AS hemodialysisTime
            FROM
                wl_arrange_classes wac
            LEFT JOIN wl_patient wp ON wac.patient_id = wp.id
            <if test="req.userId != null">LEFT JOIN wl_hemodialysis_manager whm ON wac.patient_id = whm.patient_id AND whm.hemodialysis_time = wac.classes_time</if>
            WHERE wac.deleted = 0 AND wp.deleted = 0 AND wac.dialysis_value IS NOT NULL
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus1"/>
            <if test="req.startTime != null and req.endTime != null">AND (wac.classes_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
            <if test="req.dialyzeWay != null and req.dialyzeWay != ''">AND wac.dialysis_value = #{req.dialyzeWay}</if>
            <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
            GROUP BY fomate, wac.dialysis_value
        ) wac
        GROUP BY wac.fomate
        </if>
    </select>


    <select id="getWorkloadPage" resultType="java.util.Map">
        <if test="req.dataSource  == 1">
        SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            whm.start_dialyze_time AS startDialyzeTime,
            whm.end_dialyze_time AS endDialyzeTime,
            whm.dialyze_way_value AS dialyzeWayValue,
            whm.duration_min AS durationMin,
            whm.facility_id AS facilityId,
            wfn.code AS facilityCode,
            whm.treatment_nurse AS treatmentNurse,
            whm.treatment_doctor AS treatmentDoctor,
            DATE(whm.hemodialysis_time) AS createTime
            <if test="req.chartName == 'key2'">, IFNULL( whm.summary_content, '') AS summaryContent</if>
        FROM wl_hemodialysis_manager whm
        LEFT JOIN wl_facility wfn ON whm.facility_id = wfn.id
        JOIN wl_patient wp ON wp.id = whm.patient_id
            <where>
                whm.deleted = 0 AND wp.deleted = 0
                <if test="req.customerSql != null and req.customerSql != ''"> AND whm.${req.customerSql} IS NOT NULL</if>
                <if test="req.userId != null and req.customerSql != null and req.customerSql != ''"> AND whm.${req.customerSql} = #{req.userId,jdbcType=BIGINT}</if>
                <if test="req.dialyzeWay != null">
                    AND whm.dialyze_way_value = #{req.dialyzeWay}
                </if>
                <if test="req.startTime != null and req.endTime != null">AND (whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
                <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
            </where>
        </if>
        <if test="req.dataSource  == 2">
        SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            wac.dialysis_value AS dialyzeWayValue,
            wac.facility_id AS facilityId,
            wfn.code AS facilityCode
        FROM wl_patient wp
        JOIN wl_arrange_classes wac ON wp.id = wac.patient_id
        LEFT JOIN wl_facility wfn ON wac.facility_id = wfn.id
        <where>
            wac.deleted = 0 AND wac.dialysis_value = #{req.dialyzeWay} AND wp.deleted = 0
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.startTime != null and req.endTime != null">AND (wac.classes_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
            <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
        </where>
        </if>
    </select>


    <select id="getWorkloadMapInfo" resultType="java.util.Map">
        SELECT COUNT(whm.${req.customerSql}) AS count,
            whm.${req.customerSql} AS userId,
            su.nickname AS nickName,
            DATE_FORMAT( whm.hemodialysis_time, #{req.format,jdbcType=VARCHAR} ) format,
            DATE_FORMAT( whm.hemodialysis_time, '%Y-%m-%d' ) formatDate
        FROM  wl_hemodialysis_manager whm
        LEFT JOIN system_users su ON su.id = whm.${req.customerSql}
        WHERE whm.deleted = 0 AND su.deleted = 0
            AND whm.${req.customerSql} IS NOT NULL
        <if test="req.userId != null"> AND whm.${req.customerSql} = #{req.userId}</if>
        <if test="req.startTime != null and req.endTime != null">AND (whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
        <if test="req.userIdList != null and req.userIdList.size() >0 ">
            AND whm.${req.customerSql} IN <foreach collection="req.userIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
        <if test="req.type != null and req.type == 1">AND whm.blood_filter IS NOT NULL </if>
        <if test="req.type != null and req.type == 2">AND whm.perfumer IS NOT NULL </if>
        GROUP BY whm.${req.customerSql}
        <if test="req.userId != null">
            ,format
        </if>
    </select>
    <select id="getRecentlyDialysisList" resultType="com.thj.boot.module.business.controller.admin.gk.vo.RecentlyDialysisListVo">
        select hemodialysis_time,dialyze_way_value,duration,dry_weight,
               dialyze_before_weigh,dialyze_after_weigh,before_ultrafiltrationTotal,
               treatment_hour,treatment_min,bp_no_one,after_bp_one,vascular_access_one
        from  wl_hemodialysis_manager  where patient_id =#{patientId}  ORDER BY id DESC limit 12
    </select>


    <select id="getPatient" resultType="com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO">
        select * from wl_hemodialysis_manager where patient_id = #{patientId} ORDER BY id DESC limit 1
    </select>

    <select id="getTodayRecord" resultType="com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO">
        select * from wl_hemodialysis_manager where patient_id = #{patientId} and hemodialysis_time = current_date ORDER BY id DESC limit 1
    </select>


    <select id="getLastRecord" resultType="com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO">
        select wac.id,wac.day_state,wac.dialysis_name,wac.classes_time,wac.patient_name,wac.state,wac.end_dialyze_time from wl_arrange_classes wac
        left join wl_facility wf on wac.facility_id = wf.id
        where wac.patient_id =  #{patientId}
            and wac.classes_time like concat("%",#{month},"%") AND wac.temp_type=0 AND wac.deleted=0
    </select>

    <select id="getHemodialysisList" resultType="com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO">
        select * from wl_hemodialysis_manager wac
        where wac.patient_id =  #{patientId}
          and wac.hemodialysis_time like concat("%",#{month},"%") AND wac.prescription_state=1 AND wac.deleted=0
    </select>

    <select id="getBefore" resultType="java.util.Map">
        select dialyze_after_weight  AS beforeDialyzeAfterWeigh
        from wl_hemodialysis_manager where patient_id = #{patientId} and end_dialyze_time != '' ORDER BY id desc limit 1
    </select>

    <select id="getMyPatient" resultType="com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO">
        select a.* from wl_arrange_classes a LEFT JOIN wl_hemodialysis_manager b on a.patient_id = b.patient_id
        where  1=1 /*and a.state = 2*/ and a.deleted = 0
        <if test="days != null">
            and a.classes_time  like concat("%",#{days},"%")
            and b.hemodialysis_time  like concat("%",#{days},"%")
        </if>
        <if test="computerNurse != null">
          and b.computer_nurse = #{computerNurse}
        </if>
        <if test="dayState != null">
            and a.day_state IN
            <foreach collection="dayState" item="item" separator="," open="(" close=")">
                #{item}
           </foreach>
        </if>
    </select>

    <select id="getBloodPressureDetailPage" resultType="java.util.Map">
        SELECT
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
        whm.dry_weight AS dryWeight,whm.before_ultrafiltrationTotal AS beforeUltrafiltrationTotal, whm.actual_ultrafiltration_capacity AS actualUltrafiltrationCapacity,DATE(whm.hemodialysis_time) AS hemodialysisTime,
        CONCAT( whm.bp_no_one, '/', whm.bp_no_two) as beforeBp, CONCAT(whm.after_bp_one, '/', whm.after_bp_two) AS afterBp
        FROM  wl_hemodialysis_manager whm
        JOIN wl_patient wp ON whm.patient_id = wp.id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        <where>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
            <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
        </where>
        ORDER BY wp.dialyze_no
    </select>

    <select id="getNeilou" resultType="int">
        select count(1) from wl_hemodialysis_manager where hemodialysis_time = #{format}  and  vascular_access_two in (select id from
        wl_vascular_access where name = '自体动静脉内瘘')
    </select>

    <select id="getDialysisCounts" resultType="java.util.Map">
        SELECT dialyze_way_value as dialysisName , COUNT(dialyze_way_value) AS count
        FROM wl_hemodialysis_manager
        where hemodialysis_time = #{format} and end_dialyze_time is not NULL
        GROUP BY dialyze_way_value
    </select>

    <select id="getPuncture" resultType="java.util.Map">
        SELECT dressing_change as types , COUNT(dressing_change) AS count
        FROM wl_hemodialysis_manager
        where  hemodialysis_time = #{format} and  end_dialyze_time is not null
        GROUP BY dressing_change
    </select>

    <select id="getDisease" resultType="com.thj.boot.module.business.controller.admin.dialysismanager.vo.PatientConditionVo">
        select b.`name` as patientName,b.dialyze_no as dialyzeNo,a.dialyze_way_value, a.summary_content,a.remark,c.nickname as nurseName,d.nickname as doctorName,
               f.`name` as facilityName from wl_hemodialysis_manager a
                   LEFT JOIN wl_patient b on a.patient_id = b.id
                   LEFT JOIN system_users c on a.treatment_nurse = c.id
                   LEFT JOIN system_users d on a.treatment_doctor = d.id
                   LEFT JOIN wl_facility e on a.facility_id = e.id
                   LEFT JOIN wl_facility_subarea f on e.subarea_id = f.id
        where  a.hemodialysis_time = #{format} and  a.end_dialyze_time is not null
    </select>


    <insert id="saveLog">

        insert into wl_handover_log
        (
        `create_time`,
        `type`,
        `remarks`
        )values
        (
        #{createTime},
        <if test="type != null">
            #{type},
        </if>
        <if test="remarks != null">
            #{remarks}
        </if>
        )
    </insert>
    <select id="getNewRemarks" resultType="com.thj.boot.module.business.controller.admin.dialysismanager.vo.HandoverLog">
        select * from wl_handover_log
        where 1=1
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="format != null">
            and create_time = #{format}
        </if>
        ORDER BY id DESC limit 1
    </select>

    <select id="getBeforeBloodPressureCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM wl_patient wp
        LEFT JOIN wl_hemodialysis_manager whm  ON wp.id = whm.patient_id
        <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
        <where>
            wp.deleted = 0 AND whm.deleted = 0
            <if test="req.age == 1">AND wp.age &lt; 60</if>
            <if test="req.age == 2">AND wp.age &gt;= 60</if>
            <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
            <if test="req.bpStatus != null">
                <choose>
                    <when test="req.bpStatus == 1">
                        AND (
                        <if test="req.maxOne != null and req.minOne == null">whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne == null and req.minOne != null">whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne != null and req.minOne != null">(whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER} AND whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER})</if>
                        <if test="req.searchType == 1 or req.searchType == null">AND</if>
                        <if test="req.searchType == 2">OR</if>
                        <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER} AND whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER})</if>
                        )
                    </when>
                    <when test="req.bpStatus == 2">
                        AND (
                        <if test="req.maxOne != null and req.minOne == null"> whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne == null and req.minOne != null"> whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne != null and req.minOne != null"> (whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER} OR whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER})</if>
                        <if test="req.searchType == 2 or req.searchType == null">AND</if>
                        <if test="req.searchType == 1">OR</if>
                        <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER} OR whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER})</if>
                        )
                    </when>
                    <when test="req.bpStatus == 3">
                        AND (
                        (whm.bp_no_one IS NULL OR whm.bp_no_one = '')
                        OR (whm.bp_no_two IS NULL OR whm.bp_no_two = '')
                        )
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getBeforeBloodPressureStatisticsCount" resultType="com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO">
        SELECT count(*) AS count , wp.dept_id as centerId
            FROM wl_patient wp
            LEFT JOIN wl_hemodialysis_manager whm  ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <where>
                wp.deleted = 0 AND whm.deleted = 0
                <if test="req.age == 1">AND wp.age &lt; 60</if>
                <if test="req.age == 2">AND wp.age &gt;= 60</if>
                <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
                <if test="req.bpStatus != null">
                    <choose>
                        <when test="req.bpStatus == 1">
                            AND (
                            <if test="req.maxOne != null and req.minOne == null">whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER}</if>
                            <if test="req.maxOne == null and req.minOne != null">whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER}</if>
                            <if test="req.maxOne != null and req.minOne != null">(whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER} AND whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER})</if>
                            <if test="req.searchType == 1 or req.searchType == null">AND</if>
                            <if test="req.searchType == 2">OR</if>
                            <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER}</if>
                            <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER}</if>
                            <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER} AND whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER})</if>
                            )
                        </when>
                        <when test="req.bpStatus == 2">
                            AND (
                            <if test="req.maxOne != null and req.minOne == null"> whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER}</if>
                            <if test="req.maxOne == null and req.minOne != null"> whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER}</if>
                            <if test="req.maxOne != null and req.minOne != null"> (whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER} OR whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER})</if>
                            <if test="req.searchType == 2 or req.searchType == null">AND</if>
                            <if test="req.searchType == 1">OR</if>
                            <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER}</if>
                            <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER}</if>
                            <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER} OR whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER})</if>
                            )
                        </when>
                        <when test="req.bpStatus == 3">
                            AND (
                            (whm.bp_no_one IS NULL OR whm.bp_no_one = '')
                            OR (whm.bp_no_two IS NULL OR whm.bp_no_two = '')
                            )
                        </when>
                    </choose>
                </if>
            </where>
        group by centerId
    </select>

    <sql id="whereLess60">
        <if test="req.bpStatus != null">
            <choose>
                <when test="req.bpStatus == 1">
                    AND (
                    wp.age &lt; 60  <!--小于60-->
                    AND (
                        (whm.bp_no_one &lt;= #{req.bpOneLargeLess60,jdbcType=INTEGER}
                        <if test="req.bpOneSmallLess60 != null">AND whm.bp_no_one &gt;= #{req.bpOneSmallLess60,jdbcType=INTEGER}</if>)
                        <if test="req.searchType == 1 or req.searchType == null">AND</if>
                        <if test="req.searchType == 2">OR</if>
                        (whm.bp_no_two &lt;= #{req.bpTwoLargeLess60,jdbcType=INTEGER}
                        <if test="req.bpTwoSmallLess60 != null">AND whm.bp_no_two &gt;= #{req.bpTwoSmallLess60,jdbcType=INTEGER}</if>)
                        )
                    )
                </when>
                <when test="req.bpStatus == 2">
                    AND (
                        wp.age &lt; 60
                        AND (
                            whm.bp_no_one &gt; #{req.bpOneLargeLess60,jdbcType=INTEGER}
                            <if test="req.bpOneSmallLess60 != null">AND whm.bp_no_one &lt; #{req.bpOneSmallLess60,jdbcType=INTEGER}</if>
                            <if test="req.searchType == 2 or req.searchType == null">AND</if>
                            <if test="req.searchType == 1">OR</if>
                            whm.bp_no_two &gt; #{req.bpTwoLargeLess60,jdbcType=INTEGER}
                            <if test="req.bpTwoSmallLess60 != null">AND whm.bp_no_two &lt; #{req.bpTwoSmallLess60,jdbcType=INTEGER}</if>
                        )
                    )
                </when>
                <when test="req.bpStatus == 3">
                    AND  wp.age &lt; 60 <!--大于60-->
                    AND (
                        (whm.bp_no_one IS NULL OR whm.bp_no_one = '')
                        OR (whm.bp_no_two IS NULL OR whm.bp_no_two = '')
                    )
                </when>
            </choose>
        </if>
    </sql>

    <sql id="whereLarge60">
        <if test="req.bpStatus != null">
            <choose>
                <when test="req.bpStatus == 1">
                    AND (
                        wp.age &gt;= 60 <!--大于60-->
                        AND (
                            ( whm.bp_no_one &lt;= #{req.bpOneLargeOver60}
                            <if test="req.bpOneSmallOver60 != null">AND whm.bp_no_one &gt;= #{req.bpOneSmallOver60,jdbcType=INTEGER}</if>)
                            <if test="req.searchType == 1 or req.searchType == null">AND</if>
                            <if test="req.searchType == 2">OR</if>
                            (whm.bp_no_two &lt;= #{req.bpTwoLargeOver60,jdbcType=INTEGER}
                            <if test="req.bpTwoSmallOver60 != null">AND whm.bp_no_two &gt;= #{req.bpTwoSmallOver60,jdbcType=INTEGER}</if>
                            )
                        )
                    )
                </when>
                <when test="req.bpStatus == 2">
                    AND (
                        wp.age &gt;= 60
                        AND (
                            (whm.bp_no_one &gt; #{req.bpOneLargeOver60}
                            <if test="req.bpOneSmallOver60 != null">AND whm.bp_no_one &lt; #{req.bpOneSmallOver60,jdbcType=INTEGER}</if>)
                            <if test="req.searchType == 2 or req.searchType == null">AND</if>
                            <if test="req.searchType == 1">OR</if>
                            (whm.bp_no_two &gt; #{req.bpTwoLargeOver60,jdbcType=INTEGER}
                            <if test="req.bpTwoSmallOver60 != null">AND whm.bp_no_two &lt; #{req.bpTwoSmallOver60,jdbcType=INTEGER}</if>)
                        )
                    )
                </when>
                <when test="req.bpStatus == 3">
                    AND  wp.age &gt;= 60 <!--大于60-->
                    AND (
                        (whm.bp_no_one IS NULL OR whm.bp_no_one = '')
                        OR (whm.bp_no_two IS NULL OR whm.bp_no_two = '')
                    )
                </when>
            </choose>
        </if>
    </sql>

    <select id="getBeforeBloodPressurePage" resultType="java.util.Map">
        SELECT <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectDataSort"/>
            temp.* FROM (
            SELECT
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
            whm.bp_no_one AS beforeBpNoOne, whm.bp_no_two AS beforeBpNoTwo, DATE(whm.hemodialysis_time) AS hemodialysisTime, CONCAT(whm.bp_no_one, '/', whm.bp_no_two) AS beforeBloodPressure
            FROM wl_patient wp
            LEFT JOIN wl_hemodialysis_manager whm  ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientByStatus"/>
            <where>
            wp.deleted = 0 AND whm.deleted = 0
            <if test="req.age == 1">AND wp.age &lt; 60</if>
            <if test="req.age == 2">AND wp.age &gt;= 60</if>
            <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientConditionByStatus"/>
            <if test="req.bpStatus != null">
                <choose>
                    <when test="req.bpStatus == 1">
                        AND (
                        <if test="req.maxOne != null and req.minOne == null">whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne == null and req.minOne != null">whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne != null and req.minOne != null">(whm.bp_no_one &lt;= #{req.maxOne,jdbcType=INTEGER} AND whm.bp_no_one &gt;= #{req.minOne,jdbcType=INTEGER})</if>
                        <if test="req.searchType == 1 or req.searchType == null">AND</if>
                        <if test="req.searchType == 2">OR</if>
                        <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &lt;= #{req.maxTwo,jdbcType=INTEGER} AND whm.bp_no_two &gt;= #{req.minTwo,jdbcType=INTEGER})</if>
                        )
                    </when>
                    <when test="req.bpStatus == 2">
                        AND (
                        <if test="req.maxOne != null and req.minOne == null"> whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne == null and req.minOne != null"> whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER}</if>
                        <if test="req.maxOne != null and req.minOne != null"> (whm.bp_no_one &gt; #{req.maxOne,jdbcType=INTEGER} OR whm.bp_no_one &lt; #{req.minOne,jdbcType=INTEGER})</if>
                        <if test="req.searchType == 2 or req.searchType == null">AND</if>
                        <if test="req.searchType == 1">OR</if>
                        <if test="req.maxTwo != null and req.minTwo == null">whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo == null and req.minTwo != null">whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER}</if>
                        <if test="req.maxTwo != null and req.minTwo != null">(whm.bp_no_two &gt; #{req.maxTwo,jdbcType=INTEGER} OR whm.bp_no_two &lt; #{req.minTwo,jdbcType=INTEGER})</if>
                        )
                    </when>
                    <when test="req.bpStatus == 3">
                        AND (
                        (whm.bp_no_one IS NULL OR whm.bp_no_one = '')
                        OR (whm.bp_no_two IS NULL OR whm.bp_no_two = '')
                        )
                    </when>
                </choose>
            </if>
            </where>
        ) temp
        <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.joinItable"/>
        ORDER BY temp.hemodialysisTime DESC
    </select>

    <select id="getDialyzeConsumablesDetails" resultType="java.util.Map">
        SELECT temp.format, GROUP_CONCAT(temp.patient_id) AS patientIds, GROUP_CONCAT(temp.id) AS ids
        <if test="req.consumablesType == 0">
            ,GROUP_CONCAT(temp.hemodialysis_device) AS homedialysisDevices, GROUP_CONCAT(temp.blood_filter) AS
            bloodFilters, GROUP_CONCAT(temp.perfumer) AS perfumers
        </if>
        <if test="req.consumablesType == 2">
            ,GROUP_CONCAT(temp.hemodialysis_device) AS homedialysisDevices
        </if>
        <if test="req.consumablesType == 3">,GROUP_CONCAT(temp.blood_filter) AS bloodFilters</if>
        <if test="req.consumablesType == 4">,GROUP_CONCAT(temp.perfumer) AS perfumers</if>
        FROM (
        SELECT whm.id, whm.patient_id, DATE_FORMAT(whm.hemodialysis_time, #{req.format}) AS format
        <choose>
            <when test="req.consumablesType == 0">
                , whm.hemodialysis_device, whm.blood_filter, whm.perfumer
            </when>
            <when test="req.consumablesType == 2">, whm.hemodialysis_device, whm.blood_filter</when>
            <when test="req.consumablesType == 3">, whm.blood_filter</when>
            <when test="req.consumablesType == 4">, whm.perfumer</when>
        </choose>
        FROM wl_hemodialysis_manager whm
        <where>
            <if test="req.startTime != null and req.endTime != null">AND whm.hemodialysis_time BETWEEN
                #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="req.deptIds != null and req.deptIds.length > 0">AND whm.dept_id IN
                <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        ) temp
        GROUP BY temp.format
    </select>

    <update id="updateFacility" >
        update wl_hemodialysis_manager set facility_id = #{facilityId} ,facility_type_id = #{facilityTypeId}, facility_subarea_id = #{facilitySubareaId}  where
            hemodialysis_time = #{hemodialysisTime} and patient_id = #{patientId}
    </update>

    <update id="updateTypeById" parameterType="com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO">
        update wl_hemodialysis_manager set substitute_todal = #{substituteTodal},substitute_model = #{substituteModel},
                                           hemodialysis_device = #{hemodialysisDevice},blood_filter = #{bloodFilter},
                                           perfumer = #{perfumer} where id = #{id}
    </update>

    <select id="selectListByParams" resultType="java.util.Map">
        SELECT
            temp.*
        FROM (
            SELECT
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.selectPatientInfo"/>
                <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.selectPatientStatus"/>
                CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS  dryWeight,
                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeBeforeWeight,
                CAST( whm.dialyze_after_weight AS DECIMAL ( 10, 2 ) ) AS dialyzeAfterWeight,
                CAST( whm.before_dialyze_after_weigh AS DECIMAL ( 10, 2 ) ) AS beforeDialyzeAfterWeight, <!--前次透析后体重-->
                whm.create_time AS createTime,
                DATE_FORMAT(whm.create_time , '%Y-%m-%d') AS checkTime,
                CAST( whm.dialyze_before_weight AS DECIMAL ( 10, 2 ) ) - CAST( whm.dry_weight AS DECIMAL ( 10, 2 ) ) AS growWeight,
                CASE
                    WHEN #{req.chartType} = 2 THEN
                        CASE
                        WHEN whm.before_dialyze_after_weigh IS NULL OR whm.before_dialyze_after_weigh = '/' OR whm.before_dialyze_after_weigh = 0 THEN NULL
                        ELSE ROUND(CAST((CAST(whm.dialyze_before_weight AS DECIMAL(10, 2)) - CAST(whm.before_dialyze_after_weigh AS DECIMAL(10, 2))) * 100 AS DECIMAL(10, 2)) / whm.before_dialyze_after_weigh, 2)
                    END
                    WHEN #{req.chartType} = 3 THEN
                        CASE
                        WHEN whm.dry_weight IS NULL OR whm.dry_weight = '/' OR whm.dry_weight = 0 THEN NULL
                        ELSE ROUND(CAST((CAST(whm.dialyze_before_weight AS DECIMAL(10, 2)) - CAST(whm.dry_weight AS DECIMAL(10, 2))) * 100 AS DECIMAL(10, 2)) / whm.dry_weight, 2)
                    END
                ELSE NULL
                END AS result
            FROM `wl_patient` wp
            LEFT JOIN wl_hemodialysis_manager whm ON wp.id = whm.patient_id
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.joinPatientStatus"/>
            <where>
                wp.deleted = 0
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition"/>
                <if test="req.startTime != null and req.endTime != null"> AND whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP} </if>
            </where>
            <include refid="com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper.orderByDescPatientStatus"/>
        ) temp
        <where>
            1=1
        </where>








    </select>

    <select id="getSelectListAll"
            resultType="com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO"
            parameterType="com.thj.boot.module.business.service.departmentControl.param.WorkloadStatParams">


        <if test="req.dataSource  == 1">
            SELECT
                *
            FROM
                wl_hemodialysis_manager whm
            LEFT JOIN wl_patient wp ON whm.patient_id = wp.id
            <where>
                whm.deleted = 0 AND wp.deleted = 0 AND whm.dialyze_way_value IS NOT NULL and whm.heal_state = 1
                <include refid="com.thj.boot.module.business.dal.mapper.patient.PatientMapper.wherePatientCondition1"/>
                <if test="req.startTime != null and req.endTime != null">AND (whm.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
                <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
            </where>
        </if>
        <if test="req.dataSource == 2">
            SELECT
                *,
                wac.dialysis_value as dialyze_way_value
            FROM
                wl_arrange_classes wac
            LEFT JOIN wl_patient wp ON wac.patient_id = wp.id
            LEFT JOIN wl_hemodialysis_manager whm ON wac.patient_id = whm.patient_id AND whm.hemodialysis_time = wac.classes_time
            <where>
                wac.deleted = 0 AND wp.deleted = 0 AND wac.dialysis_value IS NOT NULL
                <if test="req.startTime != null and req.endTime != null">AND (wac.classes_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP})</if>
                <if test="req.deptIds != null and req.deptIds.length > 0">AND wp.dept_id IN <foreach collection="req.deptIds" item="item" separator="," open="(" close=")">#{item,jdbcType=BIGINT}</foreach></if>
                <if test="req.userId != null">AND whm.treatment_nurse = #{req.userId,jdbcType=BIGINT}</if>
                <if test="req.patientStatus != null">
                    <choose>
                        <when test="req.patientStatus == 1">AND wp.patient_type_source = '00'</when>
                        <when test="req.patientStatus == 2">AND wp.patient_type_source = '01'</when>
                    </choose>
                </if>
            </where>
        </if>

    </select>
</mapper>
