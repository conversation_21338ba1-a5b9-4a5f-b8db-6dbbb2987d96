<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.admissionrecord.AdmissionRecordMapper">

    <resultMap id="BaseResultMap" type="com.thj.boot.module.business.dal.datado.admissionrecord.AdmissionRecord">
            <id property="id" column="id" />
            <result property="patientId" column="patient_Id" />
            <result property="nativePlace" column="native_Place" />
            <result property="birthPlace" column="birth_Place" />
            <result property="historyCollector" column="history_Collector" />
            <result property="pastHistory" column="past_History" />
            <result property="personalHistory" column="personal_History" />
            <result property="marriageHistory" column="marriage_History" />
            <result property="familyHistory" column="family_History" />
            <result property="bloodRoutine" column="blood_Routine" />
            <result property="bloodBiochemical" column="blood_Biochemical" />
            <result property="specialExam" column="special_Exam" />
            <result property="admissionDiagnosis" column="admission_Diagnosis" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,patient_Id,native_Place,birth_Place,history_Collector,past_History,
        personal_History,marriage_History,family_History,blood_Routine,blood_Biochemical,
        special_Exam,admission_Diagnosis,create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
