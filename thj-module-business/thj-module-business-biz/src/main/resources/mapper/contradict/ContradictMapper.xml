<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thj.boot.module.business.dal.mapper.contradict.ContradictMapper">

    <select id="selectContradictByTime" resultType="com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO">
        SELECT wc.*, DATE_FORMAT(wc.hemodialysis_time, #{req.format,jdbcType=VARCHAR}) format
        FROM wl_contradict wc
        <where>
            wc.deleted = 0
            <if test="req.startTime != null and req.endTime != null">AND wc.hemodialysis_time BETWEEN #{req.startTime,jdbcType=TIMESTAMP} AND #{req.endTime,jdbcType=TIMESTAMP}</if>
            <if test="req.patientIdList != null and req.patientIdList.size() > 0">
                AND wc.patient_id IN <foreach collection="req.patientIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </where>
    </select>

</mapper>
