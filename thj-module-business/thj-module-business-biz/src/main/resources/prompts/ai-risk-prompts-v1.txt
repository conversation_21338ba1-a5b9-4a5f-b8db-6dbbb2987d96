{
  "prompt": "请根据患者病历内容与化验指标，对患者进行专业且严谨的风险等级评估，并生成结构化报告。
  "rules": [
     "严格按照下方 output_format 所定义的结构和键名输出结果，但全部内容必须以标准 markdown 格式排版（如列表、表格等），确保层级清晰、可读性强。",
     "如果某个字段在文本中缺失或无法判断，不予以显示。",
     "所有字段数据类型须与 output_format 保持一致，例如年龄必须为数字类型（Number），但在 markdown 中以合适的方式展示。",
     "只返回最终的 markdown 格式报告，不要输出任何多余的解释、说明或附加文本。"

  ],
  "input_variables": [
     "病人信息":"{patientInfoData}",
     "诊疗信息": "{treatmentInfo}",
     "化验信息": "{renalCheckout}",
     "病历内容": "{medicalRecord}"
  ],
  "output_markdown_format": {
    "1.患者信息": {
      "姓名": "string",
      "年龄": "number",
      "核心风险因素": "string",
    },
    "2.风险维度评分详情": [
      {
        "风险分类维度": "string",
        "评分依据(来自病历)": "string",
        "风险等级": "string (低危/中危/高危)",
        "得分": "number"
      }
    ],
    "3.总分与风险等级": {
      "总分": "number",
      "风险等级": "string (如 高危、橙色标签)"
    },
    "4.建议管理策略": [
      "string"
    ]
  },
  "style": "专业医学风格、结构清晰、适合电子病历集成与医生快速阅读"
}
