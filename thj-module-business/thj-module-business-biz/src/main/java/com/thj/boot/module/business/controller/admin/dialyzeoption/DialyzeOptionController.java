package com.thj.boot.module.business.controller.admin.dialyzeoption;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionPageReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionUpdateReqVO;
import com.thj.boot.module.business.service.dialyzeoption.DialyzeOptionService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 透析方案
 */
@RestController
@RequestMapping("/business/dialyze-option")
@Validated
public class DialyzeOptionController {

    @Resource
    private DialyzeOptionService dialyzeOptionService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialyzeOption(@RequestBody DialyzeOptionCreateReqVO createReqVO) {
        return success(dialyzeOptionService.createDialyzeOption(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialyzeOption(@RequestBody DialyzeOptionUpdateReqVO updateReqVO) {
        dialyzeOptionService.updateDialyzeOption(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialyzeOption(@RequestParam("id") Long id) {
        dialyzeOptionService.deleteDialyzeOption(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DialyzeOptionRespVO> getDialyzeOption(@RequestParam("id") Long id, String child) {
        return success(dialyzeOptionService.getDialyzeOption(id, child));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DialyzeOptionRespVO>> getDialyzeOptionList(DialyzeOptionCreateReqVO createReqVO) {
        return success(dialyzeOptionService.getDialyzeOptionList(createReqVO));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialyzeOptionRespVO>> getDialyzeOptionPage(@RequestBody DialyzeOptionPageReqVO pageVO) {
        return success(dialyzeOptionService.getDialyzeOptionPage(pageVO));
    }

    /**
     * 根据透析方案字典值获取对应的详情和抗凝剂信息
     */
    @GetMapping("/getDialyzeInfo")
    public CommonResult<DialyzeOptionRespVO> getDialyzeInfo(String dialyzeDictValue, Long patientId, @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date hemodialysisTime) {
        return success(dialyzeOptionService.getDialyzeInfo(dialyzeDictValue,patientId,hemodialysisTime));
    }

    /**
     * 根据患者id查询透析模式父子级信息
     */
    @PostMapping("/getDialyzeList")
    public CommonResult<DialyzeOptionRespVO> getDialyzeList(@RequestBody DialyzeOptionCreateReqVO createReqVO) {
        return success(dialyzeOptionService.getDialyzeList(createReqVO));
    }

    /**
     * 检测患者是否有透析方案
     */
    @GetMapping("/checkDialysis")
    public void checkDialysisProtocol(String ids) {
        dialyzeOptionService.checkDialysisProtocol(ids);
    }


    /**
     * 批量新增透析方案
     */
    @PostMapping("/batchSaveDialyze")
    public CommonResult<Boolean> batchSaveDialyze(@RequestBody DialyzeOptionCreateReqVO createReqVO) {
        dialyzeOptionService.batchSaveDialyze(createReqVO);
        return success(true);
    }



}
