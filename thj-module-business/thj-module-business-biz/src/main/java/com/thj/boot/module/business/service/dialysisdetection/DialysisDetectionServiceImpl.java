package com.thj.boot.module.business.service.dialysisdetection;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.thj.boot.common.enums.BgColorEnum;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.common.utils.redis.RedisUtils;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.BloodPressureVO;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.DialysisDetectionWarnVO;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.DialysisTimeVo;
import com.thj.boot.module.business.convert.detectionwarn.DetectionWarnConvert;
import com.thj.boot.module.business.convert.dialysisdetection.DialysisDetectionConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.detectionwarn.DetectionWarnDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.detectionwarn.DetectionWarnMapper;
import com.thj.boot.module.business.dal.mapper.dialysisdetection.DialysisDetectionMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.AnalysisData;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionPageReqVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionUpdateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 透析检测 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DialysisDetectionServiceImpl implements DialysisDetectionService {

    @Resource
    private DialysisDetectionMapper dialysisDetectionMapper;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private DetectionWarnMapper detectionWarnMapper;

    @Autowired
    private FacilityManagerMapper facilityManagerMapper;

    @Autowired
    private ArrangeClassesMapper arrangeClassesMapper;

    @Value("${deviceUrl}")
    private String deviceUrl;

    @Override
    public Long createDialysisDetection(DialysisDetectionCreateReqVO createReqVO, HttpServletRequest request) {
        // 插入
        String systemdeptid = request.getHeader("systemdeptid");
        String format = DateUtil.format(createReqVO.getDateWeek(), DatePattern.NORM_DATE_PATTERN);
        if (org.springframework.util.StringUtils.isEmpty(format)) {
            Date date = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            format = simpleDateFormat.format(date);
        }
        String format1 = DateUtil.format(createReqVO.getDetectionMin(), DatePattern.NORM_TIME_PATTERN);
        String str = format + " " + format1;
        DateTime parse = DateUtil.parse(str, DatePattern.NORM_DATETIME_PATTERN);
        createReqVO.setDetectionMin(parse);
        createReqVO.setSymptom(JSONUtil.toJsonStr(createReqVO.getSymptoms()));
        DialysisDetectionDO dialysisDetection = DialysisDetectionConvert.INSTANCE.convert(createReqVO);
        if (org.springframework.util.StringUtils.isEmpty(dialysisDetection.getDeptId()) && !StringUtils.isEmpty(systemdeptid)) {
            dialysisDetection.setDeptId(Long.valueOf(systemdeptid));
        }
        dialysisDetectionMapper.insert(dialysisDetection);
        //静脉压、动脉压、跨膜压、超滤率、超滤量、钠浓度、电导度、透析液温度、脱水量
        Map<String, Object> cacheMap = RedisUtils.getCacheMap("detection:" + createReqVO.getPatientId());
        if (CollectionUtils.isEmpty(cacheMap)) {
            cacheMap = new HashMap<>();
        }

        cacheMap.put("venousPressure", "");
        cacheMap.put("arterialPressure", "");
        cacheMap.put("transmembranePressure", "");
        /*if (StrUtil.isNotEmpty(createReqVO.getBloodFlow()) &&!"/".equals(createReqVO.getBloodFlow())) {
            cacheMap.put("bloodFlow",createReqVO.getBloodFlow());
        }*/
        if (StrUtil.isNotEmpty(createReqVO.getUltrafiltrationRate()) && !"/".equals(createReqVO.getUltrafiltrationRate())) {
            cacheMap.put("ultrafiltrationRate", createReqVO.getUltrafiltrationRate());
        }
        if (StrUtil.isNotEmpty(createReqVO.getUltrafiltrationCapacity()) && !"/".equals(createReqVO.getUltrafiltrationCapacity())) {
            cacheMap.put("ultrafiltrationCapacity", createReqVO.getUltrafiltrationCapacity());
        }
        if (StrUtil.isNotEmpty(createReqVO.getSodiumConcentration()) && !"/".equals(createReqVO.getSodiumConcentration())) {
            cacheMap.put("sodiumConcentration", createReqVO.getSodiumConcentration());
        }
        if (StrUtil.isNotEmpty(createReqVO.getConductance()) && !"/".equals(createReqVO.getConductance())) {
            cacheMap.put("conductance", createReqVO.getConductance());
        }
        if (StrUtil.isNotEmpty(createReqVO.getDialysateTemperature()) && !"/".equals(createReqVO.getDialysateTemperature())) {
            cacheMap.put("dialysateTemperature", createReqVO.getDialysateTemperature());
        }
        /*cacheMap.put("ultrafiltrationRate", StrUtil.isNotEmpty(createReqVO.getUltrafiltrationRate()) &&!"/".equals(createReqVO.getUltrafiltrationRate()) ? createReqVO.getUltrafiltrationRate() : "");
        cacheMap.put("ultrafiltrationCapacity", StrUtil.isNotEmpty(createReqVO.getUltrafiltrationCapacity()) &&!"/".equals(createReqVO.getUltrafiltrationCapacity()) ? createReqVO.getUltrafiltrationCapacity() : "");
        cacheMap.put("sodiumConcentration", StrUtil.isNotEmpty(createReqVO.getSodiumConcentration())  &&!"/".equals(createReqVO.getSodiumConcentration()) ? createReqVO.getSodiumConcentration() : "");
        cacheMap.put("conductance", StrUtil.isNotEmpty(createReqVO.getConductance())&&!"/".equals(createReqVO.getConductance())  ? createReqVO.getConductance() : "");
        cacheMap.put("dialysateTemperature", StrUtil.isNotEmpty(createReqVO.getDialysateTemperature())&&!"/".equals(createReqVO.getDialysateTemperature()) ? createReqVO.getDialysateTemperature() : "");*/
        RedisUtils.setCacheMap("detection:" + createReqVO.getPatientId(), cacheMap);

        return dialysisDetection.getId();
    }

    private Date getThisTime(HemodialysisManagerCreateReqVO createReqVO) {
        String format = DateUtil.format(createReqVO.getDateWeek(), DatePattern.NORM_DATE_PATTERN);
        String format1 = DateUtil.format(createReqVO.getDetectionMin(), DatePattern.NORM_TIME_PATTERN);
        String str = format + " " + format1;
        return DateUtil.parse(str, DatePattern.NORM_DATETIME_PATTERN);
    }

    @Override
    public void updateDialysisDetection(DialysisDetectionUpdateReqVO updateReqVO) {
        // 更新
        String format = DateUtil.format(updateReqVO.getDateWeek(), DatePattern.NORM_DATE_PATTERN);
        String format1 = DateUtil.format(updateReqVO.getDetectionMin(), DatePattern.NORM_TIME_PATTERN);
        String str = format + " " + format1;
        DateTime parse = DateUtil.parse(str, DatePattern.NORM_DATETIME_PATTERN);
        updateReqVO.setDetectionMin(parse);
        updateReqVO.setSymptom(JSONUtil.toJsonStr(updateReqVO.getSymptoms()));
        DialysisDetectionDO updateObj = DialysisDetectionConvert.INSTANCE.convert(updateReqVO);
        //静脉压、动脉压、跨膜压、超滤率、超滤量、钠浓度、电导度、透析液温度、脱水量
        Map<String, Object> cacheMap = RedisUtils.getCacheMap("detection:" + updateReqVO.getPatientId());
        if (CollectionUtils.isEmpty(cacheMap)) {
            cacheMap = new HashMap<>();
        }
/*        if (StrUtil.isNotEmpty(updateReqVO.getBloodFlow()) &&!"/".equals(updateReqVO.getBloodFlow())) {
            cacheMap.put("bloodFlow",updateReqVO.getBloodFlow());
        }*/
        if (StrUtil.isNotEmpty(updateReqVO.getUltrafiltrationRate()) && !"/".equals(updateReqVO.getUltrafiltrationRate())) {
            cacheMap.put("ultrafiltrationRate", updateReqVO.getUltrafiltrationRate());
        }
        if (StrUtil.isNotEmpty(updateReqVO.getUltrafiltrationCapacity()) && !"/".equals(updateReqVO.getUltrafiltrationCapacity())) {
            cacheMap.put("ultrafiltrationCapacity", updateReqVO.getUltrafiltrationCapacity());
        }
        if (StrUtil.isNotEmpty(updateReqVO.getSodiumConcentration()) && !"/".equals(updateReqVO.getSodiumConcentration())) {
            cacheMap.put("sodiumConcentration", updateReqVO.getSodiumConcentration());
        }
        if (StrUtil.isNotEmpty(updateReqVO.getConductance()) && !"/".equals(updateReqVO.getConductance())) {
            cacheMap.put("conductance", updateReqVO.getConductance());
        }
        if (StrUtil.isNotEmpty(updateReqVO.getDialysateTemperature()) && !"/".equals(updateReqVO.getDialysateTemperature())) {
            cacheMap.put("dialysateTemperature", updateReqVO.getDialysateTemperature());
        }
        cacheMap.put("venousPressure", "");
        cacheMap.put("arterialPressure", "");
        cacheMap.put("transmembranePressure", "");
/*        cacheMap.put("ultrafiltrationRate", StrUtil.isNotEmpty(updateReqVO.getUltrafiltrationRate()) &&!"/".equals(updateReqVO.getUltrafiltrationRate()) ? updateReqVO.getUltrafiltrationRate() : "");
        cacheMap.put("ultrafiltrationCapacity", StrUtil.isNotEmpty(updateReqVO.getUltrafiltrationCapacity()) &&!"/".equals(updateReqVO.getUltrafiltrationCapacity()) ? updateReqVO.getUltrafiltrationCapacity() : "");
        cacheMap.put("sodiumConcentration", StrUtil.isNotEmpty(updateReqVO.getSodiumConcentration())  &&!"/".equals(updateReqVO.getSodiumConcentration()) ? updateReqVO.getSodiumConcentration() : "");
        cacheMap.put("conductance", StrUtil.isNotEmpty(updateReqVO.getConductance())&&!"/".equals(updateReqVO.getConductance())  ? updateReqVO.getConductance() : "");
        cacheMap.put("dialysateTemperature", StrUtil.isNotEmpty(updateReqVO.getDialysateTemperature())&&!"/".equals(updateReqVO.getDialysateTemperature()) ? updateReqVO.getDialysateTemperature() : "");*/
        RedisUtils.setCacheMap("detection:" + updateReqVO.getPatientId(), cacheMap);
        dialysisDetectionMapper.updateById(updateObj);
    }

    @Override
    public void deleteDialysisDetection(Long id, String state) {
        // 删除
        //开始透析后才可以确认
        if ("2".equals(state) || "3".equals(state)) {
            throw new ServiceException(GlobalErrorCodeConstants.START_DIALYSIS_DEL);
        }
        dialysisDetectionMapper.deleteById(id);
    }


    @Override
    public DialysisDetectionRespVO getDialysisDetection(Long id) {
        DialysisDetectionDO dialysisDetectionDO = dialysisDetectionMapper.selectById(id);
        return DialysisDetectionConvert.INSTANCE.convert(dialysisDetectionDO);
    }

    @Override
    public List<DialysisDetectionDO> getDialysisDetectionList(Collection<Long> ids) {
        return dialysisDetectionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DialysisDetectionDO> getDialysisDetectionPage(DialysisDetectionPageReqVO pageReqVO) {
        return dialysisDetectionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DialysisDetectionDO> getDialysisDetectionList(DialysisDetectionCreateReqVO createReqVO) {
        List<DialysisDetectionDO> dialysisDetectionDOList = dialysisDetectionMapper.selectList(createReqVO);
        if (CollectionUtil.isNotEmpty(dialysisDetectionDOList)) {
            dialysisDetectionDOList = dialysisDetectionDOList.stream().peek(dialysisDetectionDO -> {
                //处理
                StringBuilder stringBuilder = new StringBuilder();
                JSONObject entries = JSONUtil.parseObj(dialysisDetectionDO.getSymptom());
                /*JSONArray text = entries.getJSONObject("50").getJSONArray("text");
                if (text.size() > 0) {
                    String collect = text.stream().map(String::valueOf).collect(Collectors.joining(","));
                    stringBuilder.append(collect).append(",");
                }*/
                if (ObjectUtil.isNotEmpty(entries.getJSONObject("50"))) {
                    String remark = entries.getJSONObject("50").getStr("remark");
                    if (StrUtil.isNotEmpty(remark)) {
                        stringBuilder.append(remark).append(",");
                    }
                }
                /*JSONArray text2 = entries.getJSONObject("51").getJSONArray("text");
                if (text2.size() > 0) {
                    String collect = text2.stream().map(String::valueOf).collect(Collectors.joining(","));
                    stringBuilder.append(collect).append(",");
                }*/
                if (ObjectUtil.isNotEmpty(entries.getJSONObject("51"))){
                    String remark2 = entries.getJSONObject("51").getStr("remark");
                    if (StrUtil.isNotEmpty(remark2)) {
                        stringBuilder.append(remark2).append(",");
                    }
                }
                /*JSONArray text3 = entries.getJSONObject("52").getJSONArray("text");
                if (text3.size() > 0) {
                    String collect = text3.stream().map(String::valueOf).collect(Collectors.joining(","));
                    stringBuilder.append(collect).append(",");
                }*/
                if (ObjectUtil.isNotEmpty(entries.getJSONObject("52"))){
                    String remark3 = entries.getJSONObject("52").getStr("remark");
                    if (StrUtil.isNotEmpty(remark3)) {
                        stringBuilder.append(remark3).append(",");
                    }
                }

                dialysisDetectionDO.setDispose(stringBuilder.toString().replaceAll(",+$", ""));
            }).collect(Collectors.toList());
        }
        return dialysisDetectionDOList;
    }


    @Override
    public DialysisDetectionRespVO getUltrafiltration(HemodialysisManagerCreateReqVO createReqVO) {
        //本次时间
        Date parse = getThisTime(createReqVO);
        DialysisDetectionRespVO dialysisDetectionRespVO = new DialysisDetectionRespVO();
        Map<String, Object> cacheMap = RedisUtils.getCacheMap("detection:" + createReqVO.getPatientId());
        if (MapUtil.isNotEmpty(cacheMap)) {
            DialysisDetectionRespVO dialysisDetectionRespVO1 = JSONUtil.toBean(JSONUtil.toJsonStr(cacheMap), DialysisDetectionRespVO.class);
            BeanUtil.copyProperties(dialysisDetectionRespVO1, dialysisDetectionRespVO);
            dialysisDetectionRespVO.setBloodFlow("");
        }
        //超滤率(ml/h)默认值=超滤总量/透析时长，可编辑
        //上次监测记录时间=小于本次检测记录时间
        //上次检测时间
        boolean flagReplace = false;
        DialysisDetectionDO dialysisDetectionDO = getLastTime(createReqVO);
        HemodialysisManagerDO hemodialysisManagerOne = getHemodialysisManagerOne(createReqVO);
        if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerOne) && (org.springframework.util.StringUtils.isEmpty(hemodialysisManagerOne.getDurationMin()) || "00".equals(hemodialysisManagerOne.getDurationMin()))) {
            hemodialysisManagerOne.setDurationMin("0");
        }
        if (hemodialysisManagerOne != null) {
            if (StrUtil.isNotEmpty(hemodialysisManagerOne.getBeforeUltrafiltrationtotal()) && StrUtil.isNotEmpty(hemodialysisManagerOne.getDuration()) && StrUtil.isNotEmpty(hemodialysisManagerOne.getDurationMin())) {
                //透析时长
                BigDecimal duration = Convert.toBigDecimal(hemodialysisManagerOne.getDurationMin()).divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP).add(Convert.toBigDecimal(hemodialysisManagerOne.getDuration()));
                BigDecimal bigDecimal = Convert.toBigDecimal(hemodialysisManagerOne.getBeforeUltrafiltrationtotal()).multiply(Convert.toBigDecimal("1000")).divide(duration, 0, BigDecimal.ROUND_HALF_UP);
                dialysisDetectionRespVO.setUltrafiltrationRate(bigDecimal.toString());
                if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerOne.getBloodFlow())) {
                    dialysisDetectionRespVO.setBloodFlow(hemodialysisManagerOne.getBloodFlow());
                }

                //
                if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerOne.getSubstituteTodal()) && !"0".equals(hemodialysisManagerOne.getSubstituteTodal()) && !"/".equals(hemodialysisManagerOne.getSubstituteTodal())) {
                    flagReplace = true;
                    BigDecimal substituteTodal = Convert.toBigDecimal(hemodialysisManagerOne.getSubstituteTodal());
                    BigDecimal divide = substituteTodal.multiply(new BigDecimal("1000")).divide(new BigDecimal("60"), 2, BigDecimal.ROUND_HALF_UP).divide(duration, 0, BigDecimal.ROUND_HALF_UP);
                    dialysisDetectionRespVO.setReplacementRate(divide.toString());
                }
            }
            //查询小于本次时间的检测记录=上次检测时间和上一条超滤量
            if (dialysisDetectionDO != null) {
                dialysisDetectionRespVO.setDetectionMinLast(dialysisDetectionDO.getDetectionMin());
                //超滤量(ml)默认值=（本次检测记录时间-上次监测记录时间/60）*超滤率+上一条监测记录的超滤量，可编辑
                if (StrUtil.isNotEmpty(dialysisDetectionRespVO.getUltrafiltrationRate()) && StrUtil.isNotEmpty(dialysisDetectionDO.getUltrafiltrationCapacity()) && !"/".equals(dialysisDetectionDO.getUltrafiltrationCapacity())) {
                    long minute = DateUtil.between(parse, dialysisDetectionDO.getDetectionMin(), DateUnit.MINUTE);
                    int add = (Convert.toBigDecimal(minute).divide(Convert.toBigDecimal("60"), 0, RoundingMode.DOWN).multiply(Convert.toBigDecimal(dialysisDetectionRespVO.getUltrafiltrationRate())).add(Convert.toBigDecimal(dialysisDetectionDO.getUltrafiltrationCapacity()))).intValue();
                    dialysisDetectionRespVO.setUltrafiltrationCapacity(String.valueOf(add));
                }
            }
        }
        /*//获取第一条有值的超滤量
        List<DialysisDetectionDO> detectionDOS = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eq(DialysisDetectionDO::getPatientId, createReqVO.getPatientId())
                .eq(DialysisDetectionDO::getDateWeek, createReqVO.getHemodialysisTime())
                .orderByAsc(DialysisDetectionDO::getDetectionMin));
        if (CollectionUtil.isNotEmpty(detectionDOS)) {
            for (DialysisDetectionDO detectionDO : detectionDOS) {
                if (StrUtil.isNotEmpty(detectionDO.getUltrafiltrationCapacity())) {
                    if (!"/".equals(detectionDO.getUltrafiltrationCapacity()) && !"0".equals(detectionDO.getUltrafiltrationCapacity())) {
                        dialysisDetectionRespVO.setUltrafiltrationCapacity(detectionDO.getUltrafiltrationCapacity());
                        break;
                    }
                }
            }
        }*/

        //获取检测记录最后一条
        List<DialysisDetectionDO> detectionDOSLast = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eq(DialysisDetectionDO::getPatientId, createReqVO.getPatientId())
                .eq(DialysisDetectionDO::getDateWeek, createReqVO.getHemodialysisTime())
                .orderByDesc(DialysisDetectionDO::getDetectionMin));
        if (CollectionUtil.isNotEmpty(detectionDOSLast)) {
            dialysisDetectionRespVO.setBloodFlow(CollectionUtil.isNotEmpty(detectionDOSLast) ? detectionDOSLast.get(0) != null ? detectionDOSLast.get(0).getBloodFlow() : null : null);
            // 开始监测记录
            List<DialysisDetectionDO> collect = detectionDOSLast.stream().filter(dialysisDetectionDO1 -> !org.springframework.util.StringUtils.isEmpty(dialysisDetectionDO1.getDialyzeState()) && 0 == dialysisDetectionDO1.getDialyzeState()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                DialysisDetectionDO dialysisDetectionDO1 = collect.get(0);

                if (!org.springframework.util.StringUtils.isEmpty(dialysisDetectionDO1.getUltrafiltrationCapacity()) && !"/".equals(dialysisDetectionDO1.getUltrafiltrationCapacity()) && "0".equals(dialysisDetectionDO1.getUltrafiltrationCapacity())) {
                    Date detectionMinLast = dialysisDetectionRespVO.getDetectionMinLast();
                    if (!org.springframework.util.StringUtils.isEmpty(detectionMinLast) && !org.springframework.util.StringUtils.isEmpty(createReqVO.getCommand()) && 0 != createReqVO.getCommand()) {
                        /*Integer command = createReqVO.getCommand();
                        Calendar instance = Calendar.getInstance();
                        instance.setTime(detectionMinLast);
                        instance.add(Calendar.MINUTE,command);
                        Date time = instance.getTime();*/
                        createReqVO.setDetectionMin(detectionMinLast);
                    }
                    int spendHours = (createReqVO.getDetectionMin().getHours() - dialysisDetectionDO1.getDetectionMin().getHours()) * 60;
                    int spendMinute = createReqVO.getDetectionMin().getMinutes() - dialysisDetectionDO1.getDetectionMin().getMinutes() + spendHours;
                    BigDecimal spendTime = new BigDecimal((createReqVO.getDetectionMin().getMinutes() - dialysisDetectionDO1.getDetectionMin().getMinutes() + spendHours + 0.0) / 60).setScale(2, RoundingMode.HALF_UP);
                    if (!org.springframework.util.StringUtils.isEmpty(spendTime) && spendTime.compareTo(new BigDecimal("0")) > 0) {
                        int result = spendTime.multiply(BigDecimal.valueOf(Integer.valueOf(dialysisDetectionRespVO.getUltrafiltrationRate()))).intValue();
                        //result = Math.round(result/10) *10;
                        dialysisDetectionRespVO.setUltrafiltrationCapacity(String.valueOf(result));
                    } else if (spendTime.compareTo(new BigDecimal("0")) < 0) {
                        dialysisDetectionRespVO.setBloodFlow(null);
                        dialysisDetectionRespVO.setUltrafiltrationCapacity(null);
                        dialysisDetectionRespVO.setDisplacementQuantity(null);
                        dialysisDetectionRespVO.setReplacementRate(null);
                        dialysisDetectionRespVO.setUltrafiltrationRate(null);
                        dialysisDetectionRespVO.setSodiumConcentration(null);
                        dialysisDetectionRespVO.setConductance(null);
                        dialysisDetectionRespVO.setDialysateTemperature(null);
                    }
                    if (flagReplace && !org.springframework.util.StringUtils.isEmpty(dialysisDetectionDO1.getDisplacementQuantity()) && !"/".equals(dialysisDetectionDO1.getDisplacementQuantity()) && spendMinute > 0) {
                        BigDecimal replaceResult = (Convert.toBigDecimal(spendMinute).multiply(Convert.toBigDecimal(dialysisDetectionRespVO.getReplacementRate())).add(Convert.toBigDecimal(dialysisDetectionDO1.getDisplacementQuantity()).multiply(new BigDecimal("1000")))).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP);
                        dialysisDetectionRespVO.setDisplacementQuantity(replaceResult.toString());
                    }
                } else {
                    //dialysisDetectionRespVO.setBloodFlow(null);
                    dialysisDetectionRespVO.setUltrafiltrationCapacity("0");
                    if (!StringUtils.isEmpty(dialysisDetectionRespVO.getReplacementRate())) {
                        dialysisDetectionRespVO.setDisplacementQuantity("0");
                    }
                    /*dialysisDetectionRespVO.setReplacementRate(null);
                    dialysisDetectionRespVO.setUltrafiltrationRate(null);*/
                    /*dialysisDetectionRespVO.setSodiumConcentration(null);
                    dialysisDetectionRespVO.setConductance(null);
                    dialysisDetectionRespVO.setDialysateTemperature(null);*/
                }
                dialysisDetectionDO1 = detectionDOSLast.get(0);
                if (!org.springframework.util.StringUtils.isEmpty(dialysisDetectionDO1.getDialyzeState()) && dialysisDetectionDO1.getDialyzeState() == 1 && !StringUtils.isEmpty(dialysisDetectionDO1.getUltrafiltrationRate())) {
                    dialysisDetectionRespVO.setBloodFlow(null);
                    dialysisDetectionRespVO.setUltrafiltrationCapacity(null);
                    dialysisDetectionRespVO.setDisplacementQuantity(null);
                    dialysisDetectionRespVO.setReplacementRate(null);
                    dialysisDetectionRespVO.setUltrafiltrationRate(null);
                    dialysisDetectionRespVO.setSodiumConcentration(null);
                    dialysisDetectionRespVO.setConductance(null);
                    dialysisDetectionRespVO.setDialysateTemperature(null);
                }
                if (detectionDOSLast.size() > 1 && !org.springframework.util.StringUtils.isEmpty(dialysisDetectionDO1.getDialyzeState()) && dialysisDetectionDO1.getDialyzeState() == 1 && (StringUtils.isEmpty(dialysisDetectionDO1.getUltrafiltrationRate()) || "/".equals(dialysisDetectionDO1.getUltrafiltrationRate()) || "0".equals(dialysisDetectionDO1.getUltrafiltrationRate()))) {
//                    dialysisDetectionRespVO.setBloodFlow("100");
                    if (hemodialysisManagerOne.getBeforeUltrafiltrationtotal() != null) {
                        dialysisDetectionRespVO.setUltrafiltrationCapacity(String.valueOf((Convert.toBigDecimal(hemodialysisManagerOne.getBeforeUltrafiltrationtotal()).multiply(Convert.toBigDecimal("1000"))).intValue()));
                    }
                    if (!StringUtils.isEmpty(hemodialysisManagerOne.getSubstituteTodal()) && !"/".equals(hemodialysisManagerOne.getSubstituteTodal()) && !"0".equals(hemodialysisManagerOne.getSubstituteTodal())) {
                        dialysisDetectionRespVO.setDisplacementQuantity(hemodialysisManagerOne.getSubstituteTodal());
                    }

                }
            } else {
                dialysisDetectionRespVO.setUltrafiltrationCapacity("0");
                if (!StringUtils.isEmpty(dialysisDetectionRespVO.getReplacementRate())) {
                    dialysisDetectionRespVO.setDisplacementQuantity("0");
                }
            }


            /*for (DialysisDetectionDO detectionDO : detectionDOSLast) {
                if (StrUtil.isNotEmpty(detectionDO.getUltrafiltrationCapacity())) {
                    if (!"/".equals(detectionDO.getUltrafiltrationCapacity()) && !"0".equals(detectionDO.getUltrafiltrationCapacity())) {
                        BigDecimal add = Convert.toBigDecimal(detectionDO.getUltrafiltrationCapacity()).add(Convert.toBigDecimal(dialysisDetectionRespVO.getUltrafiltrationCapacity()));
                        dialysisDetectionRespVO.setUltrafiltrationCapacity(add.toString());
                        break;
                    }
                }
            }*/
        } else {
            dialysisDetectionRespVO.setBloodFlow(null);
            dialysisDetectionRespVO.setUltrafiltrationCapacity(null);
            dialysisDetectionRespVO.setDisplacementQuantity(null);
            dialysisDetectionRespVO.setReplacementRate(null);
            dialysisDetectionRespVO.setUltrafiltrationRate(null);
            dialysisDetectionRespVO.setSodiumConcentration(null);
            dialysisDetectionRespVO.setConductance(null);
            dialysisDetectionRespVO.setDialysateTemperature(null);
        }

        return dialysisDetectionRespVO;
    }

    private DialysisDetectionDO getLastTime(HemodialysisManagerCreateReqVO createReqVO) {
        Date parse = getThisTime(createReqVO);
        DialysisDetectionDO dialysisDetectionDO = dialysisDetectionMapper.selectOne(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eq(DialysisDetectionDO::getPatientId, createReqVO.getPatientId())
                .eq(DialysisDetectionDO::getDateWeek, createReqVO.getDateWeek())
                //.le(DialysisDetectionDO::getDetectionMin, parse)
                .select(DialysisDetectionDO::getDetectionMin, DialysisDetectionDO::getUltrafiltrationCapacity)
                .orderByDesc(DialysisDetectionDO::getDetectionMin)
                .last("limit 1"));
        if (dialysisDetectionDO != null && 0 != createReqVO.getCommand()) {
            DateTime dateTime = DateUtil.offsetMinute(dialysisDetectionDO.getDetectionMin(), createReqVO.getCommand());
            dialysisDetectionDO.setDetectionMin(dateTime);
            return dialysisDetectionDO;
        }
        return dialysisDetectionDO;
    }

    private HemodialysisManagerDO getHemodialysisManagerOne(HemodialysisManagerCreateReqVO createReqVO) {
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                .eq(HemodialysisManagerDO::getHemodialysisTime, createReqVO.getHemodialysisTime())
                .select(HemodialysisManagerDO::getBeforeUltrafiltrationtotal, HemodialysisManagerDO::getDuration, HemodialysisManagerDO::getDurationMin, HemodialysisManagerDO::getSubstituteTodal, HemodialysisManagerDO::getBloodFlow));
        return hemodialysisManagerDO;
    }

    @Override
    public DialysisTimeVo getDialysisTime(DialysisTimeVo vo) {

        List<DialysisDetectionDO> dialysisDetectionDOS =
                dialysisDetectionMapper.selectList(new MPJLambdaWrapper<>(DialysisDetectionDO.class).eq(DialysisDetectionDO::getPatientId,
                        vo.getPatientId()).like(DialysisDetectionDO::getDateWeek, vo.getBeginTime()).in(DialysisDetectionDO::getDialyzeState, 0,
                        1));
        // 查询设备编码
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                .eq(HemodialysisManagerDO::getHemodialysisTime, vo.getBeginTime())
                .eq(HemodialysisManagerDO::getPatientId, vo.getPatientId())
                .last("limit 1"));
        if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO)) {
            Long facilityId = hemodialysisManagerDO.getFacilityId();
            FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(new LambdaQueryWrapperX<FacilityManagerDO>().eq(FacilityManagerDO::getFacilityId, facilityId)
                    .eq(FacilityManagerDO::getDataFlag,1)
                    .last("limit 1"));
            if (!org.springframework.util.StringUtils.isEmpty(facilityManagerDO)) {
                vo.setDeviceCode(facilityManagerDO.getCode());
            }
        }else {
            // 查询排班
            ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>().eq(ArrangeClassesDO::getClassesTime, vo.getBeginTime())
                    .eq(ArrangeClassesDO::getPatientId, vo.getPatientId())
                    .eq(ArrangeClassesDO::getTempType, 0)
                    .last("limit 1"));
            if (!org.springframework.util.StringUtils.isEmpty(arrangeClassesDO)) {
                Long facilityId = arrangeClassesDO.getFacilityId();
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(new LambdaQueryWrapperX<FacilityManagerDO>().eq(FacilityManagerDO::getFacilityId, facilityId)
                        .eq(FacilityManagerDO::getDataFlag,1)
                        .last("limit 1"));
                if (!org.springframework.util.StringUtils.isEmpty(facilityManagerDO)) {
                    vo.setDeviceCode(facilityManagerDO.getCode());
                }
            }
        }

        if (StringUtils.isNotNull(dialysisDetectionDOS)) {
            dialysisDetectionDOS.forEach(dialysisDetectionDO -> {
                SimpleDateFormat sf = new SimpleDateFormat("HH:mm");

                switch (dialysisDetectionDO.getDialyzeState()) {
                    case 0:
                        vo.setStartTime(sf.format(dialysisDetectionDO.getDetectionMin()));
                        break;
                    case 1:
                        vo.setEndTime(sf.format(dialysisDetectionDO.getDetectionMin()));
                        break;
                }
            });
            return vo;
        }
        return vo;
    }

    @Override
    public PageResult<BloodPressureVO> getBloodPressure(BloodPressureVO bloodPressureVO, HttpServletRequest request) {
        IPage<BloodPressureVO> page = new Page<>(bloodPressureVO.getPageNo(), bloodPressureVO.getPageSize());
        MPJLambdaWrapper<PatientDO> wrapper = new MPJLambdaWrapper<>(PatientDO.class);
        wrapper.leftJoin(ArrangeClassesDO.class, ArrangeClassesDO::getPatientId, PatientDO::getId)
                .leftJoin(HemodialysisManagerDO.class, HemodialysisManagerDO::getPatientId, PatientDO::getId)
                .selectAs(PatientDO::getId, "patientId")
                .selectAs(PatientDO::getName, "patientName")
                .select(PatientDO::getDialyzeNo)
                .selectAs(ArrangeClassesDO::getId, "arrangeClassesId")
                .select(ArrangeClassesDO::getFacilityId, ArrangeClassesDO::getFacilityName, ArrangeClassesDO::getFacilitySubareaId, ArrangeClassesDO::getFaciitySubareaName
                        , ArrangeClassesDO::getDialysisName, ArrangeClassesDO::getDialysisValue, ArrangeClassesDO::getDayState)
                .select(HemodialysisManagerDO::getDryWeight, HemodialysisManagerDO::getDehydration, HemodialysisManagerDO::getBeforeUltrafiltrationtotal
                        , HemodialysisManagerDO::getActualUltrafiltrationCapacity, HemodialysisManagerDO::getDialyzeAfterWeight, HemodialysisManagerDO::getPno
                        , HemodialysisManagerDO::getBpNoOne, HemodialysisManagerDO::getBpNoTwo, HemodialysisManagerDO::getAfterPNo, HemodialysisManagerDO::getAfterBpOne
                        , HemodialysisManagerDO::getAfterBpTwo)
                .eq(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()))
                .eq(ArrangeClassesDO::getTempType, 0)
                .eq(HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(new Date()))
                .eq(StrUtil.isNotEmpty(bloodPressureVO.getDayState()), ArrangeClassesDO::getDayState, bloodPressureVO.getDayState())
                .and(StrUtil.isNotEmpty(bloodPressureVO.getKeyword()), i -> i
                        .like("name", bloodPressureVO.getKeyword())
                        .or()
                        .like("spell_name", bloodPressureVO.getKeyword())
                        .or()
                        .like("dialyze_no", bloodPressureVO.getKeyword()))
                .in(CollectionUtil.isNotEmpty(bloodPressureVO.getFacilitySubareaIds()), ArrangeClassesDO::getFacilitySubareaId, bloodPressureVO.getFacilitySubareaIds());
        IPage<BloodPressureVO> bloodPressureVOIPage = patientMapper.selectJoinPage(page, BloodPressureVO.class, wrapper);
        PageResult<BloodPressureVO> pageResult = new PageResult<>();
        pageResult.setList(bloodPressureVOIPage.getRecords());
        pageResult.setTotal(bloodPressureVOIPage.getTotal());
        if (CollectionUtil.isNotEmpty(pageResult.getList())) {
            String systemDeptId = request.getHeader("SystemDeptId");
            //获取全局预警值
            DetectionWarnDO detectionWarnDO = getWarn(systemDeptId, bloodPressureVO.getType(), bloodPressureVO.getDialyzeNo());
            List<BloodPressureVO> collect = pageResult.getList().stream().peek(bloodPressureVO1 -> {
                if (1 == bloodPressureVO.getType()) {
                    //获取局部预警值
                    DetectionWarnDO localDetectionWarnDO = getWarn(systemDeptId, bloodPressureVO.getType(), bloodPressureVO1.getDialyzeNo());
                    //透前血压脉搏
                    String blood = getBlood(bloodPressureVO1.getBpNoOne(), bloodPressureVO1.getBpNoTwo(), bloodPressureVO1.getPno());
                    bloodPressureVO1.setBeforeBlood(blood);
                    //全局预警
                    Boolean flag2 = getwarnstatus2(detectionWarnDO, bloodPressureVO1);
                    if (flag2 != null) {
                        if (flag2) {
                            bloodPressureVO1.setBgColorBefore(BgColorEnum.BLUE.toString());
                        } else {
                            bloodPressureVO1.setBgColorBefore(null);
                        }

                    }

                    //局部预警
                    Boolean localFlag2 = getwarnstatus2(localDetectionWarnDO, bloodPressureVO1);
                    if (localFlag2 != null) {

                        if (localFlag2) {
                            bloodPressureVO1.setBgColorBefore(BgColorEnum.BLUE.toString());
                        } else {
                            bloodPressureVO1.setBgColorBefore(null);
                        }
                    }

                    List<DialysisDetectionDO> dialysisDetectionDOList = getDialysisDetectionList2(bloodPressureVO1);
                    if (CollectionUtil.isNotEmpty(dialysisDetectionDOList)) {
                        List<DialysisDetectionDO> detectionDOList = dialysisDetectionDOList.stream().skip(0).limit(5).collect(Collectors.toList());
                        for (int i = 0; i < detectionDOList.size(); i++) {
                            if (0 == i) {
                                //第一次
                                String one = getBlood(dialysisDetectionDOList.get(0).getBloodOne(), dialysisDetectionDOList.get(0).getBloodTwo(), dialysisDetectionDOList.get(0).getPulse());
                                bloodPressureVO1.setOne(one);
                                //全局预警
                                Boolean flag = getWarnStatus(detectionWarnDO, i, dialysisDetectionDOList);
                                if (flag != null) {

                                    if (flag) {
                                        bloodPressureVO1.setBgColorOne(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorOne(null);
                                    }
                                }

                                //局部预警
                                Boolean localFlag = getWarnStatus(localDetectionWarnDO, i, dialysisDetectionDOList);
                                if (localFlag != null) {

                                    if (localFlag) {
                                        bloodPressureVO1.setBgColorOne(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorOne(null);
                                    }
                                }

                                continue;
                            } else if (1 == i) {
                                //第二次
                                String two = getBlood(dialysisDetectionDOList.get(1).getBloodOne(), dialysisDetectionDOList.get(1).getBloodTwo(), dialysisDetectionDOList.get(1).getPulse());
                                bloodPressureVO1.setTwo(two);
                                Boolean flag = getWarnStatus(detectionWarnDO, i, dialysisDetectionDOList);
                                if (flag != null) {

                                    if (flag) {
                                        bloodPressureVO1.setBgColorTwo(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorTwo(null);
                                    }
                                }

                                //局部预警
                                Boolean localFlag = getWarnStatus(localDetectionWarnDO, i, dialysisDetectionDOList);
                                if (localFlag != null) {

                                    if (localFlag) {
                                        bloodPressureVO1.setBgColorTwo(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorTwo(null);
                                    }
                                }
                                continue;
                            } else if (2 == i) {
                                //第三次
                                String three = getBlood(dialysisDetectionDOList.get(2).getBloodOne(), dialysisDetectionDOList.get(2).getBloodTwo(), dialysisDetectionDOList.get(2).getPulse());
                                bloodPressureVO1.setThree(three);
                                Boolean flag = getWarnStatus(detectionWarnDO, i, dialysisDetectionDOList);
                                if (flag != null) {

                                    if (flag) {
                                        bloodPressureVO1.setBgColorThree(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorThree(null);
                                    }
                                }

                                //局部预警
                                Boolean localFlag = getWarnStatus(localDetectionWarnDO, i, dialysisDetectionDOList);
                                if (localFlag != null) {

                                    if (localFlag) {
                                        bloodPressureVO1.setBgColorThree(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorThree(null);
                                    }
                                }

                                continue;
                            } else if (3 == i) {
                                //第四次
                                String four = getBlood(dialysisDetectionDOList.get(3).getBloodOne(), dialysisDetectionDOList.get(3).getBloodTwo(), dialysisDetectionDOList.get(3).getPulse());
                                bloodPressureVO1.setFour(four);
                                Boolean flag = getWarnStatus(detectionWarnDO, i, dialysisDetectionDOList);
                                if (flag != null) {

                                    if (flag) {
                                        bloodPressureVO1.setBgColorFour(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorFour(null);
                                    }
                                }

                                //局部预警
                                Boolean localFlag = getWarnStatus(localDetectionWarnDO, i, dialysisDetectionDOList);
                                if (localFlag != null) {

                                    if (localFlag) {
                                        bloodPressureVO1.setBgColorFour(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorFour(null);
                                    }
                                }

                                continue;
                            } else if (4 == i) {
                                //第五次
                                String five = getBlood(dialysisDetectionDOList.get(4).getBloodOne(), dialysisDetectionDOList.get(4).getBloodTwo(), dialysisDetectionDOList.get(4).getPulse());
                                bloodPressureVO1.setFive(five);
                                Boolean flag = getWarnStatus(detectionWarnDO, i, dialysisDetectionDOList);
                                if (flag != null) {

                                    if (flag) {
                                        bloodPressureVO1.setBgColorFive(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorFive(null);
                                    }
                                }

                                //局部预警
                                Boolean localFlag = getWarnStatus(localDetectionWarnDO, i, dialysisDetectionDOList);
                                if (localFlag != null) {

                                    if (localFlag) {
                                        bloodPressureVO1.setBgColorFive(BgColorEnum.BLUE.toString());
                                    } else {
                                        bloodPressureVO1.setBgColorFive(null);
                                    }
                                }
                                continue;
                            }
                        }
                        if (dialysisDetectionDOList.size() > 5) {
                            List<DialysisDetectionDO> detectionDOS = dialysisDetectionDOList.subList(5, dialysisDetectionDOList.size());
                            if (CollectionUtil.isNotEmpty(detectionDOS)) {
                                //String collect1 = detectionDOS.stream().map(dialysisDetectionDO -> {
                                //    return getBlood(dialysisDetectionDO.getBloodOne(), dialysisDetectionDO.getBloodTwo(), dialysisDetectionDO.getPulse());
                                //}).collect(Collectors.joining(","));
                                //bloodPressureVO1.setMore(collect1.replaceAll(",+$", ""));


                                List<BloodPressureVO> collect1 = detectionDOS.stream().map(dialysisDetectionDO -> {
                                    BloodPressureVO bloodPressureVO2 = new BloodPressureVO();
                                    String blood1 = getBlood(dialysisDetectionDO.getBloodOne(), dialysisDetectionDO.getBloodTwo(), dialysisDetectionDO.getPulse());
                                    bloodPressureVO2.setMore(blood1);

                                    Boolean flag = getWarnStatusMore(detectionWarnDO, dialysisDetectionDO);
                                    if (flag != null) {

                                        if (flag) {
                                            bloodPressureVO2.setBgColorMore(BgColorEnum.BLUE.toString());
                                        } else {
                                            bloodPressureVO2.setBgColorMore(null);
                                        }
                                    }


                                    //局部预警
                                    Boolean localFlag = getWarnStatusMore(localDetectionWarnDO, dialysisDetectionDO);
                                    if (localFlag != null) {

                                        if (localFlag) {
                                            bloodPressureVO2.setBgColorMore(BgColorEnum.BLUE.toString());
                                        } else {
                                            bloodPressureVO2.setBgColorMore(null);
                                        }
                                    }
                                    return bloodPressureVO2;
                                }).collect(Collectors.toList());
                                bloodPressureVO1.setMoreList(collect1);


                            }
                        }
                    }
                    //透后血压脉搏
                    String afterBlood = getBlood(bloodPressureVO1.getAfterBpOne(), bloodPressureVO1.getAfterBpTwo(), bloodPressureVO1.getAfterPNo());
                    bloodPressureVO1.setAfterBlood(afterBlood);
                    Boolean flag3 = getwarnstatus3(detectionWarnDO, bloodPressureVO1);
                    if (flag3 != null) {

                        if (flag3) {
                            bloodPressureVO1.setBgColorAfter(BgColorEnum.BLUE.toString());
                        } else {
                            bloodPressureVO1.setBgColorAfter(null);
                        }
                    }

                    //局部预警
                    Boolean localFlag3 = getwarnstatus3(localDetectionWarnDO, bloodPressureVO1);
                    if (localFlag3 != null) {

                        if (localFlag3) {
                            bloodPressureVO1.setBgColorAfter(BgColorEnum.BLUE.toString());
                        } else {
                            bloodPressureVO1.setBgColorAfter(null);
                        }
                    }

                } else {//检测记录
                    //获取局部预警值
                    DetectionWarnDO localDetectionWarnDO = getWarn(systemDeptId, bloodPressureVO.getType(), bloodPressureVO1.getDialyzeNo());
                    List<DialysisDetectionDO> dialysisDetectionList2 = getDialysisDetectionList2(bloodPressureVO1);
                    if (CollectionUtil.isNotEmpty(dialysisDetectionList2)) {
                        //脉搏
                        List<String> pulse = dialysisDetectionList2.stream().map(DialysisDetectionDO::getPulse).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(pulse)) {
                            String pluses = pulse.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setPulse(pluses);
                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 1);
                            if (detectionWarnStatus != null) {
                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorPulse((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorPulse(null);
                                }

                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 1);
                            if (localDetectionWarnStatus != null) {
                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorPulse((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorPulse(null);
                                }
                            }

                        }

                        //呼吸
                        List<String> breathe = dialysisDetectionList2.stream().map(DialysisDetectionDO::getBreathe).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(breathe)) {
                            String pluses = breathe.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setBreathe(pluses);

                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 2);
                            if (detectionWarnStatus != null) {
                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorBreathe((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorBreathe(null);
                                }

                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 2);
                            if (localDetectionWarnStatus != null) {
                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorBreathe((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorBreathe(null);
                                }
                            }
                        }


                        //ktv
                        List<String> ktv = dialysisDetectionList2.stream().map(DialysisDetectionDO::getKtv).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ktv)) {
                            String pluses = ktv.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setKtv(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 3);
                            if (detectionWarnStatus != null) {
                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorKtv((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorKtv(null);
                                }

                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 3);
                            if (localDetectionWarnStatus != null) {
                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorKtv((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorKtv(null);
                                }

                            }


                        }

                        //血流量
                        List<String> bloodFlow = dialysisDetectionList2.stream().map(DialysisDetectionDO::getBloodFlow).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(bloodFlow)) {
                            String pluses = bloodFlow.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setBloodFlow(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 4);
                            if (detectionWarnStatus != null) {
                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorBloodFlow((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorBloodFlow(null);
                                }

                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 4);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorBloodFlow((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorBloodFlow(null);
                                }
                            }

                        }


                        //静脉压
                        List<String> venousPressure = dialysisDetectionList2.stream().map(DialysisDetectionDO::getVenousPressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(venousPressure)) {
                            String pluses = venousPressure.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setVenousPressure(pluses);

                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 5);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorVenousPressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorVenousPressure(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 5);
                            if (localDetectionWarnStatus != null) {
                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorVenousPressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorVenousPressure(null);
                                }
                            }

                        }


                        //动脉压
                        List<String> arterialPressure = dialysisDetectionList2.stream().map(DialysisDetectionDO::getArterialPressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(arterialPressure)) {
                            String pluses = arterialPressure.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setArterialPressure(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 6);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorArterialPressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorArterialPressure(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 6);
                            if (localDetectionWarnStatus != null) {
                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorArterialPressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorArterialPressure(null);
                                }
                            }


                        }

                        //跨膜压
                        List<String> transmembranePressure = dialysisDetectionList2.stream().map(DialysisDetectionDO::getTransmembranePressure).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(transmembranePressure)) {
                            String pluses = transmembranePressure.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setTransmembranePressure(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 7);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorTransmembranePressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorTransmembranePressure(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 7);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorTransmembranePressure((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorTransmembranePressure(null);
                                }
                            }

                        }

                        //超滤率
                        List<String> ultrafiltrationRate = dialysisDetectionList2.stream().map(DialysisDetectionDO::getUltrafiltrationRate).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ultrafiltrationRate)) {
                            String pluses = ultrafiltrationRate.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setUltrafiltrationRate(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 8);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorUltrafiltrationRate((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorUltrafiltrationRate(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 8);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorUltrafiltrationRate((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorUltrafiltrationRate(null);
                                }
                            }


                        }

                        //超滤量
                        List<String> ultrafiltrationCapacity = dialysisDetectionList2.stream().map(DialysisDetectionDO::getUltrafiltrationCapacity).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(ultrafiltrationCapacity)) {
                            String pluses = ultrafiltrationCapacity.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setUltrafiltrationCapacity(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 9);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorUltrafiltrationCapacity((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorUltrafiltrationCapacity(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 9);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorUltrafiltrationCapacity((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorUltrafiltrationCapacity(null);
                                }
                            }


                        }

                        //钠浓度
                        List<String> sodiumConcentration = dialysisDetectionList2.stream().map(DialysisDetectionDO::getSodiumConcentration).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(sodiumConcentration)) {
                            String pluses = sodiumConcentration.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setSodiumConcentration(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 10);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorSodiumConcentration((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorSodiumConcentration(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 10);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorSodiumConcentration((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorSodiumConcentration(null);
                                }
                            }

                        }

                        //电导度
                        List<String> conductance = dialysisDetectionList2.stream().map(DialysisDetectionDO::getConductance).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(conductance)) {
                            String pluses = conductance.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setConductance(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 11);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorConductance((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorConductance(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 11);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorConductance((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorConductance(null);
                                }
                            }


                        }


                        //透析液温度
                        List<String> dialysateTemperature = dialysisDetectionList2.stream().map(DialysisDetectionDO::getDialysateTemperature).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(dialysateTemperature)) {
                            String pluses = dialysateTemperature.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setDialysateTemperature(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 12);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorDialysateTemperature((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorDialysateTemperature(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 12);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorDialysateTemperature((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorDialysateTemperature(null);
                                }
                            }

                        }


                        //置换率
                        List<String> replacementRate = dialysisDetectionList2.stream().map(DialysisDetectionDO::getReplacementRate).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(replacementRate)) {
                            String pluses = replacementRate.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setReplacementRate(pluses);


                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 13);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorReplacementRate((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorReplacementRate(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 13);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorReplacementRate((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorReplacementRate(null);
                                }
                            }

                        }

                        //置换量
                        List<String> displacementQuantity = dialysisDetectionList2.stream().map(DialysisDetectionDO::getDisplacementQuantity).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(displacementQuantity)) {
                            String pluses = displacementQuantity.stream().filter(s -> StrUtil.isNotEmpty(s) && !"/".equals(s)).collect(Collectors.joining(","));
                            bloodPressureVO1.setDisplacementQuantity(pluses);

                            //全局配置
                            Boolean detectionWarnStatus = getDetectionWarnStatus(pluses, detectionWarnDO, 14);
                            if (detectionWarnStatus != null) {

                                if (detectionWarnStatus) {
                                    bloodPressureVO1.setBgColorDisplacementQuantity((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorDisplacementQuantity(null);
                                }
                            }

                            //局部配置
                            Boolean localDetectionWarnStatus = getDetectionWarnStatus(pluses, localDetectionWarnDO, 14);
                            if (localDetectionWarnStatus != null) {

                                if (localDetectionWarnStatus) {
                                    bloodPressureVO1.setBgColorDisplacementQuantity((BgColorEnum.BLUE.toString()));
                                } else {
                                    bloodPressureVO1.setBgColorDisplacementQuantity(null);
                                }
                            }

                        }


                    }
                }
            }).collect(Collectors.toList());
            pageResult.setList(collect);
        }
        return pageResult;
    }


    private DetectionWarnDO getWarn(String systemDeptId, Integer type, String dialyzeNo) {
        DetectionWarnDO detectionWarnDO = detectionWarnMapper.selectOne(new LambdaQueryWrapperX<DetectionWarnDO>()
                .eq(DetectionWarnDO::getType, type)
                .eq(DetectionWarnDO::getDeptId, systemDeptId)
                .eq(StrUtil.isNotEmpty(dialyzeNo), DetectionWarnDO::getDialyzeNo, dialyzeNo)
                .isNull(StrUtil.isEmpty(dialyzeNo), DetectionWarnDO::getDialyzeNo));
        return detectionWarnDO;
    }

    private Boolean getDetectionWarnStatus(String pluses, DetectionWarnDO detectionWarnDO, Integer type) {
        if (detectionWarnDO != null && StrUtil.isNotEmpty(pluses)) {
            List<String> collect1 = Arrays.stream(pluses.split(",")).collect(Collectors.toList());
            if (1 == type) {
                if (!"0".equals(detectionWarnDO.getDetectionPulseLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDetectionPulseLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getDetectionPulseHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDetectionPulseHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (2 == type) {
                if (!"0".equals(detectionWarnDO.getBreatheLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getBreatheLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getBreatheHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getBreatheHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (3 == type) {
                if (!"0".equals(detectionWarnDO.getKtvLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getKtvLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getKtvHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getKtvHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (4 == type) {
                if (!"0".equals(detectionWarnDO.getBloodFlowLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getBloodFlowLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getBloodFlowHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getBloodFlowHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (5 == type) {
                if (!"0".equals(detectionWarnDO.getArterialPressureLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getArterialPressureLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getArterialPressureHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getArterialPressureHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (6 == type) {
                if (!"0".equals(detectionWarnDO.getVenousPressureLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getVenousPressureLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getVenousPressureHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getVenousPressureHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (7 == type) {
                if (!"0".equals(detectionWarnDO.getTransmembranePressureLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getTransmembranePressureLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getTransmembranePressureHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getTransmembranePressureHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (8 == type) {
                if (!"0".equals(detectionWarnDO.getUltrafiltrationRateLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getUltrafiltrationRateLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getUltrafiltrationRateHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getUltrafiltrationRateHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (9 == type) {
                if (!"0".equals(detectionWarnDO.getUltrafiltrationCapacityLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getUltrafiltrationCapacityLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getUltrafiltrationCapacityHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getUltrafiltrationCapacityHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (10 == type) {
                if (!"0".equals(detectionWarnDO.getSodiumConcentrationLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getSodiumConcentrationLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getSodiumConcentrationHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getSodiumConcentrationHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (11 == type) {
                if (!"0".equals(detectionWarnDO.getConductanceLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getConductanceLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getConductanceHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getConductanceHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (12 == type) {
                if (!"0".equals(detectionWarnDO.getDialysateTemperatureLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDialysateTemperatureLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getDialysateTemperatureHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDialysateTemperatureHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (13 == type) {
                if (!"0".equals(detectionWarnDO.getReplacementRateLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getReplacementRateLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getReplacementRateHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getReplacementRateHigh())) == 1)) {
                    return true;
                }
                return false;
            } else if (14 == type) {
                if (!"0".equals(detectionWarnDO.getDisplacementQuantityLow()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDisplacementQuantityLow())) == -1)
                        ||
                        !"0".equals(detectionWarnDO.getDisplacementQuantityHigh()) && collect1.stream().anyMatch(s -> Convert.toBigDecimal(s).compareTo(Convert.toBigDecimal(detectionWarnDO.getDisplacementQuantityHigh())) == 1)) {
                    return true;
                }
                return false;
            }


        }
        return null;
    }

    private Boolean getWarnStatusMore(DetectionWarnDO detectionWarnDO, DialysisDetectionDO dialysisDetectionDO) {
        if (detectionWarnDO != null) {
            if (
                    StrUtil.isNotEmpty(dialysisDetectionDO.getBloodOne()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDO.getBloodOne())) && (
                            !"0".equals(detectionWarnDO.getShrinkLow()) && Convert.toBigDecimal(dialysisDetectionDO.getBloodOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkLow())) == -1
                                    || !"0".equals(detectionWarnDO.getShrinkHigh()) && Convert.toBigDecimal(dialysisDetectionDO.getBloodOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkHigh())) == 1)

            ) {
                return true;
            } else if (
                    StrUtil.isNotEmpty(dialysisDetectionDO.getBloodTwo()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDO.getBloodTwo())) && (
                            !"0".equals(detectionWarnDO.getDiastoleLow()) && Convert.toBigDecimal(dialysisDetectionDO.getBloodTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleLow())) == -1
                                    || !"0".equals(detectionWarnDO.getDiastoleHigh()) && Convert.toBigDecimal(dialysisDetectionDO.getBloodTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleHigh())) == 1)


            ) {
                return true;
            } else if (StrUtil.isNotEmpty(dialysisDetectionDO.getPulse()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDO.getPulse())) && (
                    !"0".equals(detectionWarnDO.getPulseLow()) && Convert.toBigDecimal(dialysisDetectionDO.getPulse()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseLow())) == -1
                            || !"0".equals(detectionWarnDO.getPulseHigh()) && Convert.toBigDecimal(dialysisDetectionDO.getPulse()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseHigh())) == 1)

            ) {
                return true;
            }

            return false;
        }
        return null;
    }

    private Boolean getwarnstatus2(DetectionWarnDO detectionWarnDO, BloodPressureVO bloodPressureVO1) {
        if (detectionWarnDO != null) {
            if (
                    !"0".equals(detectionWarnDO.getShrinkLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getBpNoOne()) && Convert.toBigDecimal(bloodPressureVO1.getBpNoOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkLow())) == -1
                            || !"0".equals(detectionWarnDO.getShrinkHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getBpNoOne()) && Convert.toBigDecimal(bloodPressureVO1.getBpNoOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkHigh())) == 1

            ) {
                return true;
            } else if (
                    !"0".equals(detectionWarnDO.getDiastoleLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getBpNoTwo()) && Convert.toBigDecimal(bloodPressureVO1.getBpNoTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleLow())) == -1
                            || !"0".equals(detectionWarnDO.getDiastoleHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getBpNoTwo()) && Convert.toBigDecimal(bloodPressureVO1.getBpNoTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleHigh())) == 1


            ) {
                return true;
            } else if (
                    !"0".equals(detectionWarnDO.getPulseLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getPno()) && Convert.toBigDecimal(bloodPressureVO1.getPno()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseLow())) == -1
                            || !"0".equals(detectionWarnDO.getPulseHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getPno()) && Convert.toBigDecimal(bloodPressureVO1.getPno()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseHigh())) == 1

            ) {
                return true;
            }
            return false;
        }
        return null;
    }

    private Boolean getwarnstatus3(DetectionWarnDO detectionWarnDO, BloodPressureVO bloodPressureVO1) {
        if (detectionWarnDO != null) {
            if (
                    !"0".equals(detectionWarnDO.getShrinkLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getAfterBpOne()) && Convert.toBigDecimal(bloodPressureVO1.getAfterBpOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkLow())) == -1
                            || !"0".equals(detectionWarnDO.getShrinkHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getAfterBpOne()) && Convert.toBigDecimal(bloodPressureVO1.getAfterBpOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkHigh())) == 1

            ) {
                return true;
            } else if (
                    !"0".equals(detectionWarnDO.getDiastoleLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getAfterBpTwo()) && Convert.toBigDecimal(bloodPressureVO1.getAfterBpTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleLow())) == -1
                            || !"0".equals(detectionWarnDO.getDiastoleHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getAfterBpTwo()) && Convert.toBigDecimal(bloodPressureVO1.getAfterBpTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleHigh())) == 1


            ) {
                return true;
            } else if (
                    !"0".equals(detectionWarnDO.getPulseLow()) && StrUtil.isNotEmpty(bloodPressureVO1.getPno()) && Convert.toBigDecimal(bloodPressureVO1.getPno()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseLow())) == -1
                            || !"0".equals(detectionWarnDO.getPulseHigh()) && StrUtil.isNotEmpty(bloodPressureVO1.getPno()) && Convert.toBigDecimal(bloodPressureVO1.getPno()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseHigh())) == 1

            ) {
                return true;
            }
            return false;
        }
        return null;
    }

    private Boolean getWarnStatus(DetectionWarnDO detectionWarnDO, int i, List<
            DialysisDetectionDO> dialysisDetectionDOList) {
        if (detectionWarnDO != null) {
            if (
                    ObjectUtil.isNotEmpty(dialysisDetectionDOList.get(i).getBloodOne()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodOne())) &&
                            (!"0".equals(detectionWarnDO.getShrinkLow()) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkLow())) == -1
                                    || !"0".equals(detectionWarnDO.getShrinkHigh()) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodOne()).compareTo(Convert.toBigDecimal(detectionWarnDO.getShrinkHigh())) == 1)

            ) {
                return true;
            } else if (
                    ObjectUtil.isNotEmpty(dialysisDetectionDOList.get(i).getBloodTwo()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodTwo())) &&
                            (!"0".equals(detectionWarnDO.getDiastoleLow()) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleLow())) == -1
                                    || !"0".equals(detectionWarnDO.getDiastoleHigh()) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getDiastoleHigh())) == 1)


            ) {
                return true;
            } else if (
                    ObjectUtil.isNotEmpty(dialysisDetectionDOList.get(i).getPulse()) &&
                            (!"0".equals(detectionWarnDO.getPulseLow()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDOList.get(i).getPulse())) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getPulse()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseLow())) == -1
                                    || !"0".equals(detectionWarnDO.getPulseHigh()) && ObjectUtil.isNotEmpty(Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodTwo())) && Convert.toBigDecimal(dialysisDetectionDOList.get(i).getBloodTwo()).compareTo(Convert.toBigDecimal(detectionWarnDO.getPulseHigh())) == 1)

            ) {
                return true;
            }
            return false;
        }
        return null;
    }


    private List<DialysisDetectionDO> getDialysisDetectionList2(BloodPressureVO bloodPressureVO1) {
        List<DialysisDetectionDO> dialysisDetectionDOList = dialysisDetectionMapper.selectList(new LambdaQueryWrapperX<DialysisDetectionDO>()
                .eq(DialysisDetectionDO::getPatientId, bloodPressureVO1.getPatientId())
                .eq(DialysisDetectionDO::getDateWeek, DateUtil.beginOfDay(new Date()))
                .orderByAsc(DialysisDetectionDO::getDetectionMin));
        return dialysisDetectionDOList;
    }

    private String getBlood(String bpNoOne, String bpNoTwo, String pno) {
        StringBuilder sb = new StringBuilder();
        sb.append(StrUtil.isNotEmpty(bpNoOne) && !"/".equals(bpNoOne) ? bpNoOne + "/" : "")
                .append(StrUtil.isNotEmpty(bpNoTwo) && !"/".equals(bpNoTwo) ? bpNoTwo + ";" : "")
                .append(StrUtil.isNotEmpty(pno) && !"/".equals(pno) ? pno : "");
        return sb.toString();
    }


    @Override
    public void globalWarn(DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        DetectionWarnDO detectionWarnDO = DetectionWarnConvert.INSTANCE.convert(warnVO);
        detectionWarnMapper.insert(detectionWarnDO);
    }

    @Override
    public DetectionWarnDO globalWarnInfo(DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        String systemDeptId = request.getHeader("SystemDeptId");
        return detectionWarnMapper.selectOne(new LambdaQueryWrapperX<DetectionWarnDO>()
                .eq(DetectionWarnDO::getDeptId, systemDeptId)
                .eq(DetectionWarnDO::getType, warnVO.getType())
                .eq(StrUtil.isNotEmpty(warnVO.getDialyzeNo()), DetectionWarnDO::getDialyzeNo, warnVO.getDialyzeNo())
                .isNull(StrUtil.isEmpty(warnVO.getDialyzeNo()), DetectionWarnDO::getDialyzeNo));

    }

    @Override
    public void updateGlobalWarn(DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        DetectionWarnDO detectionWarnDO = DetectionWarnConvert.INSTANCE.convert(warnVO);
        detectionWarnMapper.updateById(detectionWarnDO);
    }

    @Override
    public DialysisDetectionRespVO getRecentRecord(String deviceCode, String orgCode, Date searchDate) {
        AnalysisData analysisData = postUrl(deviceUrl, deviceCode, orgCode,searchDate);

        if (org.springframework.util.StringUtils.isEmpty(analysisData)) {
            return null;
        }
        DialysisDetectionRespVO dialysisDetectionRespVO = new DialysisDetectionRespVO();

        dialysisDetectionRespVO.setDetectionMin(analysisData.getCreateDate());
        // 动脉压
        dialysisDetectionRespVO.setArterialPressure(String.valueOf(analysisData.getArterialPressure()));
        // 静脉压
        dialysisDetectionRespVO.setVenousPressure(String.valueOf(analysisData.getVenousPressure()));
        // 跨膜压
        dialysisDetectionRespVO.setTransmembranePressure(String.valueOf(analysisData.getTmp()));
        // 超滤率
        dialysisDetectionRespVO.setUltrafiltrationRate(String.valueOf(analysisData.getUfr()));
        // 超滤量
        dialysisDetectionRespVO.setUltrafiltrationCapacity(String.valueOf(analysisData.getUfv()));
        // 电导度
        dialysisDetectionRespVO.setConductance(String.valueOf(analysisData.getDialysateCond()));
        // 透析液温度
        dialysisDetectionRespVO.setDialysateTemperature(String.valueOf(analysisData.getDialysateTemp()));
        // 血流量
        dialysisDetectionRespVO.setBloodFlow(String.valueOf(analysisData.getBloodFlowRateSet()));
        // 钠浓度
        dialysisDetectionRespVO.setSodiumConcentration(String.valueOf(analysisData.getSodium()));
        // 脉搏
        //dialysisDetectionRespVO.setPulse(String.valueOf(analysisData.getPulse()));
        // 收缩压
        //dialysisDetectionRespVO.setBloodOne(String.valueOf(analysisData.getSbp()));
        // 舒张压
        //dialysisDetectionRespVO.setBloodTwo(String.valueOf(analysisData.getDbp()));
        //置换率
        dialysisDetectionRespVO.setReplacementRate(analysisData.getReplacementRate());
        // 置换量
        dialysisDetectionRespVO.setDisplacementQuantity(analysisData.getDisplacementQuantity());
        // ktv
        dialysisDetectionRespVO.setKtv(analysisData.getKtv());
        // 血容量
        dialysisDetectionRespVO.setBloodVolume(analysisData.getBloodVolume());
        // 在线尿素
        dialysisDetectionRespVO.setLineUrea(analysisData.getLineUrea());
        // 呼吸
        dialysisDetectionRespVO.setBreathe(analysisData.getBreath());

        return dialysisDetectionRespVO;
    }

    private AnalysisData postUrl(String url, String deviceCode, String orgCode, Date searchDate) {

        CloseableHttpClient httpClient = HttpClients.createDefault();
        AnalysisDataQueryForm analysisDataQueryForm = new AnalysisDataQueryForm();
        analysisDataQueryForm.setDeviceCode(deviceCode);
        analysisDataQueryForm.setOrgCode(orgCode);
        analysisDataQueryForm.setSearchDate(searchDate);

        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();
        String req = gson.toJson(analysisDataQueryForm);

        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(10000).setSocketTimeout(10000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.addHeader("app-key","HMS-APP");
        httpPost.addHeader("app-secret","XTFCR3JX26H5TZ7VSR3QIEWJVNSKQJN9");
        StringEntity stringEntity = new StringEntity(req, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            HttpResponse response = httpClient.execute(httpPost);
            // 读取响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    result.append(line.trim());
                }
                if (!org.springframework.util.StringUtils.isEmpty(result)) {
                    String s = result.toString();
                    AnalysisData adviceResult = gson.fromJson(s, AnalysisData.class);
                    if (!org.springframework.util.StringUtils.isEmpty(adviceResult)) {
                        return adviceResult;
                    }
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try{
                assert httpClient != null;
                httpClient.close();
            } catch (Exception ee){
                ee.printStackTrace();
            }

        }
        return null;
    }

}
