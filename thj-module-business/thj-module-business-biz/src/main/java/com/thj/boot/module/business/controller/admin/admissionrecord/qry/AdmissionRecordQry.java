package com.thj.boot.module.business.controller.admin.admissionrecord.qry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AdmissionRecordQry extends AdmissionRecordVo  {
    private String gender;
    private String age;
    private String address;
    private String phone;
    private String idCard;
    private String occupation;
    private String marriage;
    private String nickName;
    private String ethnicityName;
    private String attendingPhysicianName;

    private String diagnosisDateName;

    private String admissionDateName;
    private String fileName;
    private String recordTimeName;
    private String bedNumber;
    private String hospitalNumber;
    private String leaveDateName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveDate;

    /**
     * 入院诊断
     */
    private String admissionDiagnosis;

    /**
     * 入院情况及诊疗经过
     */
    private String admissionStatus;

    /**
     * 出院诊断
     */
    private String dischargeDiagnosis;

    /**
     * 出院情况
     */
    private String dischargeStatus;

    /**
     * 出院医嘱
     */
    private String dischargeOrder;

    /**
     * 出院记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leaveRecordDate;


    private String hospitalDay;

    private String leaveRecordDateName;
//    private
}
