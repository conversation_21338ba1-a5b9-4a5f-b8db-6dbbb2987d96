package com.thj.boot.module.business.controller.admin.dialysisadvice;

import cn.hutool.core.bean.BeanUtil;
import com.thj.boot.common.exception.ErrorCode;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.dialysisadvice.vo.AdviceTempsCreateVO;
import com.thj.boot.module.business.controller.admin.dialysisadvice.vo.DialysisAdviceExcelVO;
import com.thj.boot.module.business.controller.admin.dialysisadvice.vo.DialysisAdviceForDrugExcelVO;
import com.thj.boot.module.business.convert.dialysisadvice.DialysisAdviceConvert;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.*;
import com.thj.boot.module.business.service.dialysisadvice.DialysisAdviceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

import static com.thj.boot.common.pojo.CommonResult.error;
import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/dialysis-advice")
@Validated
public class DialysisAdviceController {

    @Resource
    private DialysisAdviceService dialysisAdviceService;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialysisAdvice(@RequestBody DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        return success(dialysisAdviceService.createDialysisAdvice(createReqVO, request));
    }

    /**
     * 将长期医嘱推送到临时医嘱
     */
    @PostMapping("/saveTempAdvice")
    public CommonResult<Boolean> saveTempAdvice(@RequestBody DialysisAdviceTempSaveReqVO tempSaveReqVO, HttpServletRequest request) {
        dialysisAdviceService.saveTempAdvice(tempSaveReqVO, request);
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialysisAdvice(@RequestBody DialysisAdviceUpdateReqVO updateReqVO, HttpServletRequest request) {
        dialysisAdviceService.updateDialysisAdvice(updateReqVO, request);
        return success(true);
    }

    /**
     * 删除医嘱
     */
    @PostMapping("/deleteAdvice")
    public CommonResult<Boolean> updateDialysisAdviceRequst(@RequestBody DialysisAdviceUpdateReqVO updateReqVO, HttpServletRequest request) {
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(updateReqVO.getId());
        if(dialysisAdviceDO != null && "0".equals(dialysisAdviceDO.getState())){
            dialysisAdviceService.deleteDialysisAdvice(updateReqVO.getId(),request);
            return success(true);
        }else{
            return error(10001, "删除失败");
        }
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialysisAdvice(@RequestParam("id") Long id,HttpServletRequest request) {
        dialysisAdviceService.deleteDialysisAdvice(id,request);
        return success(true);
    }

    /**
     * 删除
     */
    @PostMapping("/deleteAll")
    public CommonResult<Boolean> deleteAll(@RequestBody DialysisAdviceCreateReqVO createReqVO,HttpServletRequest request) {
        dialysisAdviceService.deleteAllDialysisAdvice(createReqVO,request);
        return success(true);
    }

    /**
     * 重新同步
     */
    @PostMapping("/reSyncAdvice")
    public CommonResult<Boolean> reSyncAdvice(@RequestBody DialysisAdviceCreateReqVO createReqVO,HttpServletRequest request) {
        dialysisAdviceService.reSyncAdvice(createReqVO,request);
        return success(true);
    }

    /**
     * 批量删除
     */
    @GetMapping("/batchDelete")
    public CommonResult<Boolean> batchDeleteDialysisAdvice(String ids,HttpServletRequest request) {
        dialysisAdviceService.batchDeleteDialysisAdvice(ids, request);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DialysisAdviceRespVO> getDialysisAdvice(@RequestParam("id") Long id) {
        return success(dialysisAdviceService.getDialysisAdvice(id));
    }

    /**
     * 获取执行医嘱详情
     */
    @GetMapping("/getCarry")
    public CommonResult<DialysisAdviceRespVO> getDialysisAdviceCarry(@RequestParam("id") Long id) {
        return success(dialysisAdviceService.getDialysisAdviceCarry(id));
    }

    /**
     * 获取执行医嘱详情
     */
    @GetMapping("/getCountById/{id}")
    public CommonResult<Integer> getCountById(@PathVariable("id") String id) {
        return success(dialysisAdviceService.getCountById(id));
    }

    /**
     * 医嘱不分页
     */
    @PostMapping("/list")
    public CommonResult<List<DialysisAdviceRespVO>> getDialysisAdviceList(@RequestBody DialysisAdviceCreateReqVO createReqVO) {
        return success(dialysisAdviceService.getDialysisAdviceList(createReqVO));
    }


    /**
     * 医嘱分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialysisAdviceRespVO>> getDialysisAdvicePage(@RequestBody DialysisAdvicePageReqVO pageVO) {
        return success(dialysisAdviceService.getDialysisAdvicePage(pageVO));
    }

    /**
     * 获取当前时间信息
     */
    @GetMapping("/getCurrentInfo")
    public CommonResult<DialysisAdviceRespVO> getCurrentInfo() {
        return success(dialysisAdviceService.getCurrentInfo());
    }

    /**
     * 获取执行医嘱角色对应的账号信息
     */
    @GetMapping("/getRoleAdvice")
    public CommonResult<Map<String, Object>> getRoleAdvice() {
        return success(dialysisAdviceService.getRoleAdvice());
    }

    /**
     * 新增医嘱模版
     */
    @PostMapping("/batchSaveAdviceTemp")
    public CommonResult<Boolean> batchSaveAdviceTemp(@RequestBody AdviceTempsCreateVO adviceTempsVO, HttpServletRequest request) {
        dialysisAdviceService.batchSaveAdviceTemp(adviceTempsVO, request);
        return success(true);
    }

    /**
     * 执行医嘱
     */
    @PostMapping("/activeAdvice")
    public CommonResult<Boolean> activeAdvice(@RequestBody DialysisAdviceUpdateReqVO updateReqVO) {
        dialysisAdviceService.activeAdvice(updateReqVO);
        return success(true);
    }
    /**
     * 批量执行医嘱
     */
    @PostMapping("/activeAdviceBatch")
    public CommonResult<Boolean> activeAdviceBatch(@RequestBody DialysisAdviceUpdateReqVO updateReqVO) {
        dialysisAdviceService.activeAdviceBatch(updateReqVO);
        return success(true);
    }

    /**
     * 停止医嘱
     */
    @PostMapping("/stopAdvice")
    public CommonResult<Boolean> stopAdvice(@RequestBody DialysisAdviceUpdateReqVO updateReqVO) {
        dialysisAdviceService.stopAdvice(updateReqVO);
        return success(true);
    }

    /**
     * 微信推送
     */
    @PostMapping("/wxSend")
    public CommonResult<Boolean> wxSend(@RequestBody AdviceTempsCreateVO createVO) {
        dialysisAdviceService.wxSend(createVO);
        return success(true);
    }


    /**
     * 批量新增医嘱
     */
    @PostMapping("/batchSaveDialysisAdvice")
    public CommonResult<Boolean> batchSaveDialysisAdvice(@RequestBody DialysisAdviceCreateReqVO createReqVO) {
        dialysisAdviceService.batchSaveDialysisAdvice(createReqVO);
        return success(true);
    }

    /**
     * 历史医嘱
     */
    @PostMapping("/getHistoryAdvice")
    public CommonResult<List<DialysisAdviceRespVO>> getHistoryAdvice(@RequestBody DialysisAdviceCreateReqVO createReqVO) {
        return success(dialysisAdviceService.getHistoryAdvice(createReqVO));
    }

    /**
     * 用药推送
     */
    @PostMapping("/medicatePush")
    public CommonResult<Boolean> medicatePush(@RequestBody DialysisAdviceCreateReqVO createReqVO) {
        dialysisAdviceService.medicatePush(createReqVO);
        return success(true);
    }

    /**
     * 用药推送批量新增临时医嘱
     */
    @PostMapping("/medicatePushBatch")
    public CommonResult<Boolean> medicatePushBatch(@RequestBody DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        dialysisAdviceService.medicatePushBatch(createReqVO,request);
        return success(true);
    }

    /**
     * 今日医嘱统计
     */
    @PostMapping("/adviceStatistics")
    public CommonResult<List<DialysisAdviceRespVO>> adviceStatistics(@RequestBody DialysisAdvicePageReqVO pageReqVO) {
        return success(dialysisAdviceService.adviceStatistics(pageReqVO));
    }

    /**
     * 导出
     */
    @PostMapping("/export-excel")
    public void exportDevManExcel(@RequestBody DialysisAdvicePageReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        List<DialysisAdviceRespVO> adviceStatisticsList = dialysisAdviceService.adviceStatistics(exportReqVO);
        List<DialysisAdviceExcelVO> dialysisAdviceExcelVOS = DialysisAdviceConvert.INSTANCE.convertList3(adviceStatisticsList);
        if (exportReqVO.getStatisticsType() == 1) {
            EasyExcelUtils.write(response, "今日医嘱统计信息.xls", "今日医嘱统计信息", DialysisAdviceExcelVO.class, dialysisAdviceExcelVOS);
        }
        if (exportReqVO.getStatisticsType() == 2) {
            List<DialysisAdviceForDrugExcelVO> drugExcelVOS = BeanUtil.copyToList(dialysisAdviceExcelVOS, DialysisAdviceForDrugExcelVO.class);
            EasyExcelUtils.write(response, "今日医嘱统计信息.xls", "今日医嘱统计信息", DialysisAdviceForDrugExcelVO.class, drugExcelVOS);
        }
    }

}
