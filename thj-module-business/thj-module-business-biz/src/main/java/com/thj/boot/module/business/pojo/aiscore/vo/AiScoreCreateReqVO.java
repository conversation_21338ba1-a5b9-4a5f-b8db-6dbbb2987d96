package com.thj.boot.module.business.pojo.aiscore.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;

/**
 * AI评分创建请求 VO
 *
 * <AUTHOR>
 */
@Data
public class AiScoreCreateReqVO {

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者信息
     */
    @Valid
    private PatientInfoVO patientInfo;

    /**
     * 病历内容（可选，如果不传则使用默认模拟数据）
     */
    private String medicalRecord;

    /**
     * 诊疗信息（可选，如果不传则使用默认模拟数据）
     */
    private String treatmentInfo;

    private Long deptId;

} 