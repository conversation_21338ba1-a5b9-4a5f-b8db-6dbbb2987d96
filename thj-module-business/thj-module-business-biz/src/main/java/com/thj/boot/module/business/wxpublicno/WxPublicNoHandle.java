package com.thj.boot.module.business.wxpublicno;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.thj.boot.common.pojo.wxpublicno.WxPublicNoCreateReqVO;
import com.thj.boot.module.business.controller.admin.wxpublicno.vo.SendMessageCreateReqVO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.tempcontent.TempContentDO;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.tempcontent.TempContentMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/16 15:54
 * @description
 */
@Component
@Slf4j
public class WxPublicNoHandle {

    @Resource
    private WxPublicNoCreateReqVO wxPublicNoCreateReqVO;

    @Resource
    private WxMpService wxMpService;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private TempContentMapper tempContentMapper;

    /***
     * <AUTHOR>
     * @date 2023/12/16 15:57
     * @Description pc点击登录按钮，生成临时二维码
     **/
    public String wxLoginPage(HttpServletResponse response, Long patientId) {
        String qrCodeUrl = null;
        try {
            //获取token
            String accessToken = getAccessToken();
            //获取 ticket
            String ticket = getTicket(accessToken, patientId);
            //获取二维码
            qrCodeUrl = wxPublicNoCreateReqVO.getShowQrCode().replace("TICKET", ticket);
            //ResponseEntity<byte[]> forEntity = getInstance("utf-8").getForEntity(qrCodeUrl, byte.class);
            //byte[] body1 = forEntity.getBody();
            //response.getOutputStream().write(body1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return qrCodeUrl;
    }

    /**
     * 获取accessToken
     *
     * @return
     */
    public String getAccessToken() {
        //根据appid和appsecret获取access_token
        String url = wxPublicNoCreateReqVO.getTokenUrl().replace("APPID", wxPublicNoCreateReqVO.getAppId()).replace("APPSECRET", wxPublicNoCreateReqVO.getAppSecret());
        String token = HttpUtil.get(url);
        JSONObject object = JSONObject.parseObject(token);
        String accessToken = object.getString("access_token");
        return accessToken;
    }


    /**
     * 获取ticket
     *
     * @param accessToken
     * @return
     */
    public String getTicket(String accessToken, Long patientId) {

        //请求地址
        String getQrCodeUrl = wxPublicNoCreateReqVO.getQrCodeUrl().replace("TOKEN", accessToken);
        HttpHeaders headers = new HttpHeaders();
        //参数设置
        Map<String, Object> map = new HashMap<>();
        //二维码的过期时间，单位为秒，最大2592000（即30天）不填，则默认有效期为60秒。
        map.put("expire_seconds", "604800");
        //二维码类型，QR_SCENE为临时的整型参数值，QR_STR_SCENE为临时的字符串参数值，QR_LIMIT_SCENE为永久的整型参数值，QR_LIMIT_STR_SCENE为永久的字符串参数值
        map.put("action_name", "QR_LIMIT_STR_SCENE");
        Map<String, Object> innerThenMap = new HashMap<>();
        //扫码回调时自定义要传输的数据
        innerThenMap.put("scene_str", patientId);
        Map<String, Object> innerMap = new HashMap<>();
        innerMap.put("scene", innerThenMap);
        //二维码详细信息
        map.put("action_info", innerMap);
        // 组装请求体
        HttpEntity<Map<String, Object>> sendMap =
                new HttpEntity<Map<String, Object>>(map, headers);
        ResponseEntity<String> responseEntity = getInstance("utf-8").postForEntity(getQrCodeUrl, sendMap, String.class);
        String body = responseEntity.getBody();
        JSONObject jsonObject = JSONObject.parseObject(body);
        String ticket = jsonObject.getString("ticket");
        return ticket;
    }

    /***
     * <AUTHOR>
     * @date 2023/12/16 16:07
     * @Description 校验服务器配置是否生效
     **/
    public String checkSign(HttpServletRequest request) throws Exception {
        //获取微信请求参数
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String echostr = request.getParameter("echostr");
        log.info("微信出参:{},{},{},{}", signature, timestamp, nonce, echostr);
        //参数排序。 token 就要换成自己实际写的 token
        String[] params = new String[]{timestamp, nonce, wxPublicNoCreateReqVO.getToken()};
        Arrays.sort(params);
        //拼接
        String paramstr = params[0] + params[1] + params[2];
        //加密
        //获取 shal 算法封装类
        MessageDigest Sha1Dtgest = MessageDigest.getInstance("SHA-1");
        //进行加密
        byte[] digestResult = Sha1Dtgest.digest(paramstr.getBytes("UTF-8"));
        //拿到加密结果
        String mysignature = bytes2HexString(digestResult);
        mysignature = mysignature.toLowerCase(Locale.ROOT);
        //是否正确
        boolean signsuccess = mysignature.equals(signature);
        //逻辑处理
        if (signsuccess && echostr != null) {
            //验证签名，接入服务器
            return echostr;
        } else {
            //接入成功后,下次回调过来就可以进行正常业务处理
            return callback(request);
        }
    }

    private String bytes2HexString(byte... bytes) {
        char[] hexChars = new char[bytes.length * 2];
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 回调业务处理
     *
     * @param request
     * @return
     * @throws Exception
     */
    private String callback(HttpServletRequest request) throws Exception {
        //解析
        //获取消息流,并解析xml
        WxMpXmlMessage message = WxMpXmlMessage.fromXml(request.getInputStream());
        //消息类型
        String messageType = message.getMsgType();
        //消息事件
        String messageEvent = message.getEvent();
        //发送者帐号
        String openId = message.getFromUser();
        //开发者微信号
        String touser = message.getToUser();
        //文本消息  文本内容
        String text = message.getContent();
        //二维码参数
        String eventKey = message.getEventKey();
        String unionId = message.getUnionId();
        //通过请求头获取值
        PatientDO patientDO = patientMapper.selectById(eventKey);
        if (messageType.equals("event")) {
            //获取微信用户信息
            //JSONObject userInfo = this.getUserInfo(openId);
            //根据不同的回调事件处理各自的业务
            switch (messageEvent) {
                //扫码
                case "SCAN":
                    log.info("扫码...");
                    //业务处理...
                    if (patientDO != null) {
                        SendMessageCreateReqVO createReqVO = new SendMessageCreateReqVO();
                        createReqVO.setOpenId(openId);
                        createReqVO.setName(patientDO.getName());
                        createReqVO.setTime(DateUtil.now());
                        createReqVO.setState(1);
                        String msg = sendMessage(createReqVO);
                        if (StrUtil.isNotEmpty(msg)) {
                            PatientDO patientDO3 = patientMapper.selectOne(PatientDO::getOpenId, openId);
                            if (patientDO3 != null && patientDO3.getId() != Long.valueOf(eventKey)) {
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, "")
                                        .set(PatientDO::getState, "0")
                                        .eq(PatientDO::getId, patientDO3.getId()));
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, openId)
                                        .set(PatientDO::getState, "1")
                                        .eq(PatientDO::getId, Long.valueOf(eventKey)));
                            } else {
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, openId)
                                        .set(PatientDO::getState, "1")
                                        .eq(PatientDO::getId, Long.valueOf(eventKey)));
                            }

                        }
                    }
                    break;
                //关注公众号
                case "subscribe":
                    log.info("关注公众号...");
                    //业务处理...
                    String[] s = eventKey.split("_");
                    PatientDO patientDO1 = patientMapper.selectById(s[1]);
                    if (patientDO1 != null) {
                        SendMessageCreateReqVO createReqVO = new SendMessageCreateReqVO();
                        createReqVO.setOpenId(openId);
                        createReqVO.setName(patientDO1.getName());
                        createReqVO.setTime(DateUtil.now());
                        createReqVO.setState(1);
                        String msg = sendMessage(createReqVO);
                        if (StrUtil.isNotEmpty(msg)) {
                            PatientDO patientDO3 = patientMapper.selectOne(PatientDO::getOpenId, openId);
                            if (patientDO3 != null && patientDO1.getId() != Long.valueOf(s[1])) {
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, "")
                                        .set(PatientDO::getState, "0")
                                        .eq(PatientDO::getId, patientDO3.getId()));
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, openId)
                                        .set(PatientDO::getState, "1")
                                        .eq(PatientDO::getId, Long.valueOf(s[1])));
                            } else {
                                patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                                        .set(PatientDO::getOpenId, openId)
                                        .set(PatientDO::getState, "1")
                                        .eq(PatientDO::getId, Long.valueOf(s[1])));
                            }
                        }
                    }

                    break;
                //取消关注公众号
                case "unsubscribe":
                    log.info("取消关注公众号...");
                    //业务处理...
                    patientMapper.update(null, new LambdaUpdateWrapper<PatientDO>()
                            .set(PatientDO::getOpenId, "")
                            .set(PatientDO::getState, "0")
                            .eq(PatientDO::getOpenId, openId));
                    break;
                default:
                    break;
            }
        }
        return request.getParameter("echostr");
    }

    public String sendMessage(SendMessageCreateReqVO createReqVO) {
        WxMpDefaultConfigImpl wxStorage = new WxMpDefaultConfigImpl();
        //appid
        wxStorage.setAppId(wxPublicNoCreateReqVO.getAppId());
        //appsecret
        wxStorage.setSecret(wxPublicNoCreateReqVO.getAppSecret());
        wxMpService.setWxMpConfigStorage(wxStorage);
        String msg = null;
        //患者绑定推送
        if (1 == createReqVO.getState()) {
            msg = patientBindSend(createReqVO);
        }
        //医嘱推送
        if (2 == createReqVO.getState()) {
            msg = adviceSend(createReqVO);
        }
        //排班推送
        if (3 == createReqVO.getState()) {
            msg = classesSend(createReqVO);
        }
        return msg;
    }

    private String classesSend(SendMessageCreateReqVO createReqVO) {
        TempContentDO tempContentDO = tempContentMapper.selectOne(TempContentDO::getType, "3");
        String msg = null;
        if (tempContentDO != null) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(tempContentDO.getContent());
            //推送消息
            WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                    .toUser(createReqVO.getOpenId())//要推送的用户openid
                    .templateId(tempContentDO.getTempId())//模板id
                    //.url("http://www.baidu.com")//点击模板消息要访问的网址
                    .build();
            //3,如果是正式版发送消息，，这里需要配置你的信息
            Set<String> strings = jsonObject.keySet();
            for (String string : strings) {
                if ("name1".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getName(), "#FF00FF"));
                } else if ("name2".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getTime(), "#FF00FF"));
                } else {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getEventName(), "#FF00FF"));
                }
            }
            try {
                msg = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                log.info("排班推送成功:{}", msg);
            } catch (Exception e) {
                log.info("排班推送失败:{}", e.getMessage());
                e.printStackTrace();
            }
        }
        return msg;
    }

    private String adviceSend(SendMessageCreateReqVO createReqVO) {
        TempContentDO tempContentDO = tempContentMapper.selectOne(TempContentDO::getType, "2");
        String msg = null;
        if (tempContentDO != null) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(tempContentDO.getContent());
            //推送消息
            WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                    .toUser(createReqVO.getOpenId())//要推送的用户openid
                    .templateId(tempContentDO.getTempId())//模板id
                    //.url("http://www.baidu.com")//点击模板消息要访问的网址
                    .build();
            //3,如果是正式版发送消息，，这里需要配置你的信息
            Set<String> strings = jsonObject.keySet();
            for (String string : strings) {
                if ("name1".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getName(), "#FF00FF"));
                } else if ("name2".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getAdviceName(), "#FF00FF"));
                }
            }
            try {
                msg = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                log.info("医嘱推送成功:{}", msg);
            } catch (Exception e) {
                log.info("医嘱推送失败:{}", e.getMessage());
                e.printStackTrace();
            }
        }
        return msg;
    }

    private String patientBindSend(SendMessageCreateReqVO createReqVO) {
        log.info("微信推送绑定患者入参：{}", JSONUtil.toJsonStr(createReqVO));
        TempContentDO tempContentDO = tempContentMapper.selectOne(TempContentDO::getType, "1");
        String msg = null;
        if (tempContentDO != null) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(tempContentDO.getContent());
            //推送消息
            WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                    .toUser(createReqVO.getOpenId())//要推送的用户openid
                    .templateId(tempContentDO.getTempId())//模板id
                    //.url("http://www.baidu.com")//点击模板消息要访问的网址
                    .build();
            //3,如果是正式版发送消息，，这里需要配置你的信息
            Set<String> strings = jsonObject.keySet();
            for (String string : strings) {
                if ("name1".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getName(), "#FF00FF"));
                } else if ("name2".equals(string)) {
                    templateMessage.addData(new WxMpTemplateData(jsonObject.getStr(string), createReqVO.getTime(), "#FF00FF"));
                }

            }
            try {
                msg = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                log.info("患者绑定推送成功:{}", msg);
            } catch (Exception e) {
                log.info("患者绑定推送失败:{}", e.getMessage());
                e.printStackTrace();
            }
        }
        return msg;
    }

    /**
     * 获取用户信息
     *
     * @param openId
     * @return
     */
    private JSONObject getUserInfo(String openId) {
        //从微信上中拉取用户信息
        String accessToken = getAccessToken();
        System.out.println(accessToken);
        String url = wxPublicNoCreateReqVO.getUserInfoUrl().replace("ACCESS_TOKEN", accessToken).replace("OPENID", openId);
        ResponseEntity<String> forEntity = getInstance("utf-8").getForEntity(url, String.class);
        String result = forEntity.getBody();
        JSONObject jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }

    /**
     * 创建指定字符集的RestTemplate
     *
     * @param charset 编码
     * @return 返回结果
     */
    public RestTemplate getInstance(String charset) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName(charset)));
        return restTemplate;
    }

}
