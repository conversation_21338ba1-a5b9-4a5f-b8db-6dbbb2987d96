package com.thj.boot.module.business.pojo.dialysisadvice.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisAdvicePageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 医嘱类型
     */
    private String type;
    /**
     * 医嘱时间
     */
    private Date adviceTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 提醒日期
     */
    private Date warnTime;
    /**
     * 开嘱医生
     */
    private String adviceUser;
    /**
     * 医嘱名称
     */
    private String adviceName;
    /**
     * 医嘱描述
     */
    private String adviceDesprition;
    /**
     * 单次用量
     */
    private String oneNo;
    /**
     * 开药数量
     */
    private String prescribeNo;
    /**
     * 给药途径
     */
    private List<String> drugWay;
    /**
     * 执行频率
     */
    private String frequency;
    /**
     * 子药
     */
    private String childName;
    /**
     * 执行时间
     */
    private Date activateTime;
    /**
     * 执行人员
     */
    private Long activateUser;
    /**
     * 核对人员
     */
    private Long checkUser;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 停止状态 0-未停止，1-停止
     */
    private String stopStatus;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 分区id
     */
    private List<Long> facilitySubareaId;
    /**
     * 医嘱核对0-未核对，1-已核对
     */
    private String check;
    /**
     * 医嘱状态 医嘱状态，0-未确认，1-已确认
     */
    private String adviceState;
    /**
     * 班次
     */
    private String weekDay;

    /**
     * 患者名称
     */
    private String patientName;

    private String more;

    private String keyword;

    /**
     * type=1按患者，2按药品
     */
    private Integer statisticsType;

    private List<Long> patientIdList;
    private String sort;
    private String frequencyLabel;
}
