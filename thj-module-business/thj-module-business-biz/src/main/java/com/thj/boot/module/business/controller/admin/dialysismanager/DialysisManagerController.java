package com.thj.boot.module.business.controller.admin.dialysismanager;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysismanager.vo.*;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerRespVO;
import com.thj.boot.module.business.pojo.dialysismanager.vo.DialysisManagerUpdateReqVO;
import com.thj.boot.module.business.service.dialysismanager.DialysisManagerService;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2023/12/23 14:15
 * @description
 */

/**
 * 透析管理
 */
@RestController
@RequestMapping("/business/dialysis-manager")
public class DialysisManagerController {

    @Resource
    private DialysisManagerService dialysisManagerService;

    /**
     * 血液透析左侧患者列表
     */
    @PostMapping("/patientList")
    public CommonResult<List<TeamPatientRespVO>> patientList(@RequestBody TeamPatientCreateVO createVO) {
        return success(dialysisManagerService.patientList(createVO));
    }

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialysisManager(@RequestBody DialysisManagerCreateReqVO createReqVO) {
        return success(dialysisManagerService.createDialysisManager(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialysisManager(@RequestBody DialysisManagerUpdateReqVO updateReqVO) {
        dialysisManagerService.updateDialysisManager(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialysisManager(@RequestParam("id") Long id) {
        dialysisManagerService.deleteDialysisManager(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DialysisManagerRespVO> getDialysisManager(@RequestParam("id") Long id) {
        return success(dialysisManagerService.getDialysisManager(id));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DialysisManagerRespVO>> getDialysisManagerList(DialysisManagerCreateReqVO createReqVO) {
        return success(dialysisManagerService.getDialysisManagerList(createReqVO));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialysisManagerRespVO>> getDialysisManagerPage(@RequestBody DialysisManagerPageReqVO pageVO) {
        return success(dialysisManagerService.getDialysisManagerPage(pageVO));
    }

    /**
     * 治疗小结校验用户密码
     */
    @PostMapping("/checkUserName")
    public CommonResult<Boolean> checkUserName(@RequestBody UserCreateReqDTO createReqDTO) {
        dialysisManagerService.checkUserName(createReqDTO);
        return success(true);
    }

    /**
     * 透析准备
     */
    @PostMapping("/consumablePage")
    public CommonResult<PageResult<? extends DialyzeArrangeBaseVO>> consumablePage(@RequestBody DialysisManagerPageReqVO pageVO) {
        return success(dialysisManagerService.consumablePage(pageVO));
    }

    /**
     * 根据透析日期和患者id晒选
     */
    @PostMapping("/getDialysisManagerInfo")
    public CommonResult<DialysisManagerRespVO> getDialysisManagerInfo(@RequestBody DialysisManagerCreateReqVO createReqVO) {
        return success(dialysisManagerService.getDialysisManagerInfo(createReqVO));
    }

    /**
     * 透析检测
     */
    @PostMapping("/dialysisTestPage")
    public CommonResult<PageResult<? extends DialyzeArrangeBaseVO>> dialysisTestPage(@RequestBody DialysisManagerPageReqVO pageVO) {
        return success(dialysisManagerService.dialysisTestPage(pageVO));
    }

    /**
     * 开始透析
     */
    @PostMapping("/startDialyze")
    public CommonResult<Boolean> startDialyze(@RequestBody DialysisManagerCreateReqVO createReqVO) {
        dialysisManagerService.startDialyze(createReqVO);
        return success(true);
    }

    /**
     * 结束透析
     */
    @PostMapping("/endDialyze")
    public CommonResult<Boolean> endDialyze(@RequestBody DialysisManagerCreateReqVO createReqVO) {
        dialysisManagerService.endDialyze(createReqVO);
        return success(true);
    }

    /**
     * 已自查-已归档
     */
    @PostMapping("/updateState")
    public CommonResult<Boolean> updateState(@RequestBody DialysisManagerCreateReqVO createReqVO) {
        dialysisManagerService.updateState(createReqVO);
        return success(true);
    }

    /**
     * 透析器使用列表
     */
    @GetMapping("/userList")
    public CommonResult<List<DialysisUseRespVO>> userList(Long patientId) {
        return success(dialysisManagerService.userList(patientId));
    }

    /**
     * 交班日志
     */
    @PostMapping("/classesRecords")
    public CommonResult<TransferRecordsRespVO> classesRecords(@RequestBody TransferRecordsReqVO recordsRespVO,@RequestHeader(required = false) Long SystemDeptId) {
        return success(dialysisManagerService.newSlassesRecords(recordsRespVO,SystemDeptId));
    }

        /**
         * 新增备注信息
         * @param
         * @return
         */
        @PostMapping("/save")
        public CommonResult<Boolean> save(@RequestHeader(required = false) Long SystemDeptId,@RequestBody HandoverLog log) {
            log.setDeptId(SystemDeptId);
            log.setCreateTime(new Date());
            Integer type = log.getType();
            if(type == 1){
                log.setRemarks(log.getRemarksDoctor());
            }
            dialysisManagerService.save(log);
            return success(true);
        }

        /**
         * 获取当前最新一条记录信息
         * @param SystemDeptId
         * @return
         */
        @GetMapping("/getNewRemarks")
        public  CommonResult<HandoverLog> getNewRemarks(@RequestHeader(required = false) Long SystemDeptId,Integer type){
            return success( dialysisManagerService.getNewRemarks(SystemDeptId,type,null));
        }
}
