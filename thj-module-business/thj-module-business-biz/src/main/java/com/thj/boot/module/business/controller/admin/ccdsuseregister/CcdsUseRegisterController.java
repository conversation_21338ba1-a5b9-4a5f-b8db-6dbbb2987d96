package com.thj.boot.module.business.controller.admin.ccdsuseregister;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.ccdsuseregister.CcdsUseRegisterConvert;
import com.thj.boot.module.business.dal.datado.ccdsuseregister.CcdsUseRegisterDO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterPageReqVO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterRespVO;
import com.thj.boot.module.business.pojo.ccdsuseregister.vo.CcdsUseRegisterUpdateReqVO;
import com.thj.boot.module.business.service.ccdsuseregister.CcdsUseRegisterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * ccds使用登记
 */
@RestController
@RequestMapping("/business/ccds-use-register")
@Validated
public class CcdsUseRegisterController {

    @Resource
    private CcdsUseRegisterService ccdsUseRegisterService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createCcdsUseRegister(@RequestBody CcdsUseRegisterCreateReqVO createReqVO) {
        return success(ccdsUseRegisterService.createCcdsUseRegister(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateCcdsUseRegister(@RequestBody CcdsUseRegisterUpdateReqVO updateReqVO) {
        ccdsUseRegisterService.updateCcdsUseRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteCcdsUseRegister(@RequestParam("id") Long id) {
        ccdsUseRegisterService.deleteCcdsUseRegister(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<CcdsUseRegisterRespVO> getCcdsUseRegister(@RequestParam("id") Long id) {
        CcdsUseRegisterDO ccdsUseRegister = ccdsUseRegisterService.getCcdsUseRegister(id);
        return success(CcdsUseRegisterConvert.INSTANCE.convert(ccdsUseRegister));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<CcdsUseRegisterRespVO>> getCcdsUseRegisterList(CcdsUseRegisterCreateReqVO createReqVO) {
        List<CcdsUseRegisterDO> list = ccdsUseRegisterService.getCcdsUseRegisterList(createReqVO);
        return success(CcdsUseRegisterConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<CcdsUseRegisterRespVO>> getCcdsUseRegisterPage(@RequestBody CcdsUseRegisterPageReqVO pageVO) {
        PageResult<CcdsUseRegisterDO> pageResult = ccdsUseRegisterService.getCcdsUseRegisterPage(pageVO);
        return success(CcdsUseRegisterConvert.INSTANCE.convertPage(pageResult));
    }


}
