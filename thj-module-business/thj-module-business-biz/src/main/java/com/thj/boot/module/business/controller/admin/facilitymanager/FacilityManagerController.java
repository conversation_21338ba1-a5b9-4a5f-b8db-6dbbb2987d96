package com.thj.boot.module.business.controller.admin.facilitymanager;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.facilitymanager.FacilityManagerConvert;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.pojo.facility.vo.FacilityRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerCreateReqVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerPageReqVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerUpdateReqVO;
import com.thj.boot.module.business.service.facilitymanager.FacilityManagerService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 设备日常管理
 */
@RestController
@RequestMapping("/business/facility-manager")
@Validated
public class FacilityManagerController {

    @Resource
    private FacilityManagerService facilityManagerService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createFacilityManager(@RequestBody FacilityManagerCreateReqVO createReqVO) {
        return success(facilityManagerService.createFacilityManager(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateFacilityManager(@RequestBody FacilityManagerUpdateReqVO updateReqVO) {
        facilityManagerService.updateFacilityManager(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteFacilityManager(@RequestParam("id") Long id) {
        facilityManagerService.deleteFacilityManager(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<FacilityManagerRespVO> getFacilityManager(@RequestParam("id") Long id) {
        FacilityManagerDO facilityManager = facilityManagerService.getFacilityManager(id);
        return success(FacilityManagerConvert.INSTANCE.convert(facilityManager));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<FacilityManagerRespVO>> getFacilityManagerList(FacilityManagerCreateReqVO createReqVO) {
        return success(facilityManagerService.getFacilityManagerList(createReqVO));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<FacilityManagerRespVO>> getFacilityManagerPage(@RequestBody FacilityManagerPageReqVO pageVO) {
        return success(facilityManagerService.getFacilityManagerPage(pageVO));
    }

    /**
     * 综合查询
     */
    @PostMapping("/totalPage")
    public CommonResult<Map<String, Object>> getFacilityManagerTotalPage(@RequestBody FacilityManagerPageReqVO pageReqVO) {
        return success(facilityManagerService.getFacilityManagerTotalPage(pageReqVO));
    }

    /**
     * 查询未分配机号
     */
    @GetMapping("/noCallNumer")
    public CommonResult<List<FacilityRespVO>> getNoCallNumer() {
        return success(facilityManagerService.getNoCallNumer());
    }


}
