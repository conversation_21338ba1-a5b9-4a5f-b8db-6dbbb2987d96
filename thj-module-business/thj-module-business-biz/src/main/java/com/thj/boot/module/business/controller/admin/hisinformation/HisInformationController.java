package com.thj.boot.module.business.controller.admin.hisinformation;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.hisinformation.HisInformationConvert;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationCreateReqVO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationPageReqVO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationRespVO;
import com.thj.boot.module.business.pojo.hisinformation.vo.HisInformationUpdateReqVO;
import com.thj.boot.module.business.service.hisinformation.HisInformationService;
import com.thj.boot.module.system.api.dept.DeptApi;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * his 中心服务项目信息表
 */
@RestController
@RequestMapping("/business/his-information")
@Validated
public class HisInformationController {

    @Resource
    private HisInformationService hisInformationService;

    @Resource
    private DeptApi deptApi;

    /**
     * 新增
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    public CommonResult<Long> createHisInformation(@RequestBody HisInformationCreateReqVO createReqVO) {
        return success(hisInformationService.createHisInformation(createReqVO));
    }

    /**
     * 修改
     * @param updateReqVO
     * @return
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateHisInformation(@RequestBody HisInformationUpdateReqVO updateReqVO) {
        hisInformationService.updateHisInformation(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteHisInformation(@RequestParam("id") Long id) {
        hisInformationService.deleteHisInformation(id);
        return success(true);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @GetMapping("/get")
    public CommonResult<HisInformationRespVO> getHisInformation(@RequestParam("id") Long id) {
        HisInformationDO hisInformation = hisInformationService.getHisInformation(id);
        return success(HisInformationConvert.INSTANCE.convert(hisInformation));
    }

    @GetMapping("/list")
    public CommonResult<List<HisInformationRespVO>> getHisInformationList(@RequestParam("ids") Collection<Long> ids) {
        List<HisInformationDO> list = hisInformationService.getHisInformationList(ids);
        return success(HisInformationConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     * @param pageVO
     * @return
     */
    @PostMapping("/page")
    public CommonResult<PageResult<HisInformationRespVO>> getHisInformationPage(@RequestBody HisInformationPageReqVO pageVO) {
        PageResult<HisInformationDO> pageResult = hisInformationService.getHisInformationPage(pageVO);
        return success(HisInformationConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 同步his信息
     * @param request
     * @return
     */
    @GetMapping("/getHis")
    public  CommonResult<Object> getHis(HttpServletRequest request){
        return success(hisInformationService.asyncHisPrescriptionItem(request));
    }

}
