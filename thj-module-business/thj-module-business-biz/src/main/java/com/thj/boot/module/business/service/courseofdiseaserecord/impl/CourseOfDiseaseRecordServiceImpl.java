package com.thj.boot.module.business.service.courseofdiseaserecord.impl;

import com.thj.boot.module.business.dal.datado.CourseOfDiseaseRecord;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.thj.boot.module.business.dal.mapper.CourseOfDiseaseRecordMapper;
import com.thj.boot.module.business.service.courseofdiseaserecord.CourseOfDiseaseRecordService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【course_of_disease_record(病程记录表)】的数据库操作Service实现
* @createDate 2025-07-18 14:33:06
*/
@Service
public class CourseOfDiseaseRecordServiceImpl extends ServiceImpl<CourseOfDiseaseRecordMapper, CourseOfDiseaseRecord>
    implements CourseOfDiseaseRecordService {

}




