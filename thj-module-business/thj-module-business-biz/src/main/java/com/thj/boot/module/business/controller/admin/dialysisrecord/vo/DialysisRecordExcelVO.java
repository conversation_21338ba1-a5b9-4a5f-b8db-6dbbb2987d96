package com.thj.boot.module.business.controller.admin.dialysisrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.thj.boot.module.business.excel.convert.ClassesConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/20 16:55
 * @description
 */
@Data
public class DialysisRecordExcelVO implements Serializable {

    /**
     * 透析日期
     */
    @ExcelProperty(value = "透析日期")
    private Date dateWeek;
    /**
     * 班次
     */
    @ExcelProperty(value = "班次", converter = ClassesConvert.class)
    private String weekDay;
    /**
     * 分区+机号
     */
    @ExcelProperty("分区-机号")
    private String areaCode;
    /**
     * 透析模式名称
     */
    @ExcelProperty("透析模式")
    private String dialyzeWayValue;
    /**
     * 透析时长
     */
    @ExcelProperty("透析时长")
    private String duration;
    /**
     * 干体重
     */
    @ExcelProperty("干体重")
    private String dryWeight;
    /**
     * 透前体重
     */
    @ExcelProperty("透前体重")
    private String dialyzeBeforeWeight;
    /**
     * 透前血压
     */
    @ExcelProperty("透前血压")
    private String beforeBpNo;
    /**
     * 透后体重
     */
    @ExcelProperty("透后体重")
    private String dialyzeAfterWeigh;
    /**
     * 超滤总量
     */
    @ExcelProperty("超滤总量")
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量
     */
    @ExcelProperty("实际超滤量")
    private String actualUltrafiltrationCapacity;
    /**
     * 结束透析时间
     */
    @ExcelProperty("结束透析时间")
    private Date endDialyzeTime;

    /**
     * 透前血压
     */
    @ExcelIgnore
    private String beforeBpNoOne;
    /**
     * 透前血压
     */
    @ExcelIgnore
    private String beforeBpNoTwo;

}
