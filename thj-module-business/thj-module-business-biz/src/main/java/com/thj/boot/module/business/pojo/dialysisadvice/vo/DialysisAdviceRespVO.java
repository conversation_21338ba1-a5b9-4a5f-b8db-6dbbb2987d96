package com.thj.boot.module.business.pojo.dialysisadvice.vo;

import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialysisAdviceRespVO extends DialysisAdviceBaseVO {

    /**
     * 当前医嘱医生名称
     */
    private String adviceUserName;
    /**
     * 执行人员名称
     */
    private String activateUserName;
    /**
     * 核对人员名称
     */
    private String checkUserName;
    /**
     * 多个医嘱名称
     */
    private List<String> adviceNameList;
    /**
     * 多个子药
     */
    private List<DialysisAdviceDO> dialysisAdviceDOS;
    /**
     * 机号
     */
    private String facilityName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 停止医生姓名
     */
    private String stopUserName;
    /**
     * 拼接医嘱内容
     */
    private String adviceContent;
    /**
     * 透前上次透后体重
     */
    private String beforeDialyzeAfterWeigh;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight;
    /**
     * 透前体重增加
     */
    private String beforeGainWeight;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 目标脱水=较干体重增加量
     */
    private String dehydration;
    /**
     * 实际超滤量=净脱水量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeight;
    /**
     * 体重减少
     */
    private String weights;
    /**
     * 医嘱数量
     */
    private Integer adviceNameCount;
    /**
     * 住院号
     */
    private String hospitalNo;

    private String dpId;

    private String dpdId;

    private Integer showName;

    // 是否禁用
    private Integer enabled;
    private String code;
    private String label;
    private String frequencyLabel;
}
