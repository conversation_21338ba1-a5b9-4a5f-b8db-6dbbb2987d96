package com.thj.boot.module.business.controller.admin.admissionrecord.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
@Data
public class AdmissionRecordVo implements Serializable {
    private Long id;

    /**
     * 病人id
     */
    private Long patientId;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 出生地
     */
    private String birthPlace;

    /**
     * 病史陈述者
     */
    private String historyCollector;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 婚育史
     */
    private String marriageHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 血尿常规
     */
    private String bloodRoutine;

    /**
     * 血液生化
     */
    private String bloodBiochemical;

    /**
     * B超·X线及其他特殊检查
     */
    private String specialExam;

    /**
     * 入院时病例分型
     */
    private String admissionDiagnosis;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 一般情况
     */
    private String generalCondition;

    /**
     * 皮肤粘膜
     */
    private String skinMucosa;

    /**
     * 浅表淋巴结
     */
    private String lymphNodes;

    /**
     * 头部及其器官
     */
    private String headNeckExam;

    /**
     * 颈部
     */
    private String neck;

    /**
     * 胸部-胸腔
     */
    private String chest;

    /**
     * 胸部-肺脏
     */
    private String lungs;

    /**
     * 胸部-心脏
     */
    private String heart;

    /**
     * 周围血管
     */
    private String bloodVessels;

    /**
     * 腹部
     */
    private String abdomenExam;

    /**
     * 肛门、直肠、外生殖器
     */
    private String anus;

    /**
     * 脊柱·四肢
     */
    private String spineExtremities;

    /**
     * 神经系统
     */
    private String neurologicalMental;

    /**
     * 专科检查
     */
    private String specialistExamination;



    private static final long serialVersionUID = 1L;

    private Long deptId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime diagnosisDate;
    /**
     * 入院日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime admissionDate;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordTime;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 心电图检查结果
     */
    private String ecg;

    /**
     * 初步诊断
     */
    private String preliminaryDiagnosis;

    /**
     * 病例特点
     */
    private String caseFeatures;

    /**
     * 诊断依据
     */
    private String diagnosticBasis;

    /**
     * 鉴别诊断
     */
    private String differentialDiagnosis;

    /**
     * 诊疗计划
     */
    private String treatmentPlan;

    /**
     * 主治医师
     */
    private String attendingPhysician;

    private String generalProject;

    /**
     * 月经史
     */
    private String menstrualHistory;

    /**
     * 眼睛检查结果
     */
    private String eyes;

    /**
     * 耳部检查结果
     */
    private String ears;

    /**
     * 鼻部检查结果
     */
    private String nose;

    /**
     * 口腔检查结果
     */
    private String mouth;

    /**
     * 咽喉检查结果
     */
    private String throat;

    /**
     * 胸部视诊结果
     */
    private String chestVisual;

    /**
     * 胸部触诊结果
     */
    private String chestPalpation;

    /**
     * 胸部叩诊结果
     */
    private String chestPercussion;

    /**
     * 胸部听诊结果
     */
    private String chestAuscultation;

    /**
     * 腹部视诊结果
     */
    private String abdomenVisual;

    /**
     * 腹部触诊结果
     */
    private String abdomenPalpation;

    /**
     * 腹部叩诊结果
     */
    private String abdomenPercussion;

    /**
     * 腹部听诊结果
     */
    private String abdomenAuscultation;

    /**
     * 辅助检查结果
     */
    private String examinations;
    private String reliable;
    private String name;
    private String temperature;//体温（），
    private String pulse;//(脉搏),
    private String breathing;//（呼吸）,
    private String bloodPressure;//（血压）,
    private String height;//（身高）,
    private String weight;//（体重字段）
}
