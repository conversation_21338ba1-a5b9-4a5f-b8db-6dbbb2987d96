package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceRespVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/9 9:42
 * @description
 */
@Data
public class HemodialysisManagerPrintDialyzeRespVO implements Serializable {
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 透析方式
     */
    private String dialyzeWayValue;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 治疗日期
     */
    private Date hemodialysisTime;
    /**
     * 病区
     */
    private String faciitySubareaName;
    /**
     * 床号
     */
    private String bedNo;

    /**
     * 排班床号
     */
    private String arrayBed;
    /**
     * 治疗方式
     */
    private String dialysisName;
    /**
     * 置换方式
     */
    private String substituteModel;
    /**
     * 置换总量
     */
    private String substituteTodal;
    /**
     * 实际置换量
     */
    private String actualExchangeAmount;
    /**
     * 治疗时间小时
     */
    private String duration;
    /**
     * 治疗时间分钟
     */
    private String durationMin;
    /**
     * 抗凝剂名称
     */
    private List<ContradictRespVO> contradictRespVOS;
    /**
     * 处方脱水量
     */
    private String prescriptionEhydratedLevel;
    /**
     * 透析液流量
     */
    private String dialysateFlowrate;
    /**
     * 医师签名
     */
    private Long doctorId;
    /**
     * 医师签名
     */
    private String doctorName;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;
    /**
     * 血透器名称
     */
    private String hemodialysisDeviceName;
    /**
     * 血滤器名称
     */
    private String bloodFilterName;
    /**
     * 灌流器名称
     */
    private String perfumerName;
    /**
     * 血管通路1
     */
    private String vascularAccessOne;
    /**
     * 血管通路2
     */
    private String vascularAccessTwo;
    /**
     * 管路
     */
    private String pipeline;
    /**
     * 透析处方-确认人名称
     */
    private String prescriptionUseName;
    /**
     * 检测记录
     */
    private List<DialysisDetectionRespVO> dialysisDetectionRespVOS = Lists.newArrayList();
    /**
     * 临时医嘱
     */
    private List<DialysisAdviceRespVO> dialysisAdviceRespVOS = Lists.newArrayList();
    /**
     * 体重对象
     */
    private Map<String, Object> weightInfo = Maps.newHashMap();
    /**
     * 透前上次透后体重
     */
    private String beforeDialyzeAfterWeigh;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight;
    /**
     * 透前体重增加
     */
    private String beforeGainWeight;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 目标脱水
     */
    private String dehydration;
    /**
     * 实际超滤量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeight;
    /**
     * 体重减少
     */
    private String weights;
    /**
     * 透析小结
     */
    private String summaryContent;
    /**
     * 治疗医生
     */
    private Long treatmentDoctor;
    /**
     * 治疗医生名称
     */
    private String treatmentDoctorName;
    /**
     * 上机护士
     */
    private Long computerNurse;
    /**
     * 下机护士
     */
    private Long offComputerNurse;
    /**
     * 上机/下机护士名称
     */
    private String offComputerNurseName;
    /**
     * 上机前病情
     */
    private String beforeJson;
    /**
     * 治疗护士
     */
    private Long treatmentNurse;
    /**
     * 核对人员
     */
    private Long verificationPersonnel;

    private String machine;

    private String treatmentHour;

    private String treatmentMin;

    private Integer sort;

}
