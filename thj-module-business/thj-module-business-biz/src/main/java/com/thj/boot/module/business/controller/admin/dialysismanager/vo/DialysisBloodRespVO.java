package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4 9:34
 * @description
 */
@Data
public class DialysisBloodRespVO extends DialyzeArrangeBaseVO {
    /**
     * 透析处方
     */
    private String prescription;
    /**
     * 透前评估
     */
    private String beforeEvaluate;
    /**
     * 透后评估
     */
    private String afterEvaluate;
    /**
     * 透析模式
     */
    private String dialyzeDictValue;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 目标脱水量
     */
    private String targetDehydratedLevel;
    /**
     * 超滤总量
     */
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeigh;
    /**
     * 透前血压脉搏
     */
    private String beforeBlood;
    /**
     * 第1次(血压+脉搏)
     */
    private String one;
    /**
     * 第2次
     */
    private String two;
    /**
     * 第3次
     */
    private String three;
    /**
     * 第4次
     */
    private String four;
    /**
     * 第5次
     */
    private String five;
    /**
     * 第n次
     */
    private List<String> more;
    /**
     * 透后血压脉搏
     */
    private String afterBlood;
}
