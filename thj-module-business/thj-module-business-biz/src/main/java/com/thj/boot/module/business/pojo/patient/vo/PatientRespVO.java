package com.thj.boot.module.business.pojo.patient.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PatientRespVO extends PatientBaseVO {
    /**
     * 患者排班数量
     */
    private Long teamCount;

    /**
     * 预约排班
     */
    /**
     * 当前周总数量
     */
    private Long weekCount;
    /**
     * 当前月的总数量
     */
    private Long monthCount;
    /**
     * 排班机号
     */
    private String facilityName;
    /**
     * 调整机号
     */
    private String adjustFacilityName;
    /**
     * 状态 0-未签到，1-已签到，2-透析中，3-透析后,4归档，5未排班，6已排班
     */
    private Integer classesState;
    /**
     * 处方确认状态 0-未确认，1-已确认，3-已更新（透析处方）
     */
    private Integer prescriptionState;
    /**
     * 透析模式
     */
    private String dialyzeName;
    /**
     * 透析方式
     */
    private String dialyzeValue;
    /**
     * 0-未排班，1-已排班
     */
    private String scheduling;
    /**
     * 0-无方案，1-有方案
     */
    private String dialysisPlan;

    private Integer sort;

    /**
     * 排班时间
     */
    private List<String> dateList;
    /**
     * 疾病诊断
     */
    private String diseaseReasonNames;

    /**
     * 排班时间
     */
    private Date classesTime;

    /**
     * 文化程度
     */
    private String culture;
    /**
     * 职业
     */
    private String occupation;

    /**
     * 报销方式
     */
    private String applyWay;

    /**
     * 排班机号id
     */
    private Long facilityId;
    /**
     * a-上午，b-下午,c-晚上
     */
    private String dayState;
    /**
     * 1-周一，2-周二，3-周三，4-周四，5-周五，6-周六，7-周日
     */
    private Integer weekState;
    /**
     * 透析机使用登记id
     */
    private Long useRegisterId;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date startDialyzeTime;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date endDialyzeTime;

    /**
     * 消毒时间
     */
    private String disinfectTime;

    /**
     * 诊断
     */
    private String diagnosis;

    /**
     * 透前体重
     */
    private String weightPre;
    /**
     * 透前血压
     */
    private String pressurePre;

    /**
     * 在中心首次治疗时间
     */
    private Date firstCureTime;

    /**
     * 最后一次治疗时间
     */
    private Date lastCureTime;

    /**
     * 转出时间
     */
    private Date outTime;

    /**
     * 最近转入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date latestReceiveTime;

    /**
     * 透析机型号
     */
    private String modelConsumableSpec;

    /**
     * 透析结束时间
     */
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date finishDialyzeTime;

    private Integer enabledMark;

    private String nurseName;

    private PatientRespDiseaseVO disease;
//    /**
//     * 三级id
//     */
//    private Long parentThreeId;
private Integer tunnelCuffedFlag;
    private Integer noCuffFlag;
    private Integer diabetesFlag;
    private Integer noHeparinFlag;
}
