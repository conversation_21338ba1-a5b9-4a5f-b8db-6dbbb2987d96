package com.thj.boot.module.business.controller.admin.patient;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.patient.vo.DiseaseHistoryRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.FirstTreatmentExcelVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientInspectionRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientLabelReqVO;
import com.thj.boot.module.business.controller.admin.useregister.vo.UserRegisterExcelVO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyDO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyReq;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyVo;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientUpdateReqVO;
import com.thj.boot.module.business.service.dialysisadvice.DialysisAdviceService;
import com.thj.boot.module.business.service.patient.PatientService;
import com.thj.boot.module.system.pojo.dept.vo.DeptCreateReqVO;
import com.thj.boot.module.system.pojo.dept.vo.DeptRespVO;
import com.thj.boot.module.system.service.dept.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 患者管理
 */
@RestController
@RequestMapping("/business/patient")
@Validated
public class PatientController {

    @Resource
    private PatientService patientService;

    @Resource
    private DeptService deptService;

    /**
     * 新增
     */
    @RepeatSubmit
    @PostMapping("/create")
    public CommonResult<Long> createPatient(@RequestBody PatientCreateReqVO createReqVO) {
        return success(patientService.createPatient(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updatePatient(@RequestBody PatientUpdateReqVO updateReqVO) {
        patientService.updatePatient(updateReqVO);
        return success(true);
    }

    /**
     * 批量删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deletePatient(String ids) {
        patientService.deletePatient(ids);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<PatientRespVO> getPatient(@RequestParam("id") Long id) {
        return success(patientService.getPatient(id));
    }

    /**
     * 不分页
     */
    @PostMapping("/list")
    public CommonResult<List<PatientRespVO>> getPatientList(@RequestBody PatientCreateReqVO createReqVO,@RequestHeader(required = false) Long SystemDeptId) {
        return success(patientService.getPatientList(createReqVO,SystemDeptId));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<PatientRespVO>> getPatientPage(@RequestBody PatientPageReqVO pageVO) {
        return success(patientService.getPatientPage(pageVO));
    }

    /**
     * 患者首次治疗详情 的导出
     */
    @PostMapping("/export/firstTreatment")
    public void exportFirstTreatment(@RequestBody PatientPageReqVO pageVO, HttpServletResponse response) throws IOException {
        PageResult<PatientRespVO> patientPage = patientService.getPatientPage(pageVO);
        // 获取患者数据
        List<PatientRespVO> patientPageList = patientPage.getList();
        List<FirstTreatmentExcelVO> firstTreatmentExcelVOS = BeanUtil.copyToList(patientPageList, FirstTreatmentExcelVO.class);

        // 获取 中心数据字典
        List<DeptRespVO> list = deptService.getDeptList(new DeptCreateReqVO());
        Map<Long, String> deptMap = CollUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(DeptRespVO::getId, DeptRespVO::getName));

        // 数据序号
        AtomicInteger indexCounter = new AtomicInteger(1);
        // 处理数据
        firstTreatmentExcelVOS = firstTreatmentExcelVOS.stream().peek(item -> {
            // 设置序号
            item.setIndex(indexCounter.getAndIncrement());

            // 设置中心名称
            item.setDeptName(deptMap.get(item.getDeptId()));

            // 设置性别
            if ("1".equals(item.getSex())) {
                item.setSex("男");
            } else {
                item.setSex("女");
            }
        }).collect(Collectors.toList());
        EasyExcelUtils.write(response, "患者首次治疗详情.xls", "患者首次治疗详情", FirstTreatmentExcelVO.class, firstTreatmentExcelVOS);
    }

    /**
     * 导出pdf
     */
    @GetMapping("/export-pdf")
    //@("business:patient:export-pdf")
    public CommonResult<Map<String, String>> exportPatientPDF(PatientCreateReqVO createReqVO, HttpServletResponse response) throws Exception {
        String url = patientService.exportPatientPDF(createReqVO, response);
        Map<String, String> urlMap = Maps.newHashMap();
        urlMap.put("url", url);
        return success(urlMap);
    }

    /**
     * 生成二维码
     */
    @GetMapping("/getPcQrCode")
    public CommonResult<String> getPcQrCode(HttpServletResponse response, Long patientId) {
        return success(patientService.getPcQrCode(response, patientId));
    }

    /**
     * 患者管理-排班信息
     */
    @GetMapping("/getTeamPatientList")
    public CommonResult<List<ArrangeClassesRespVO>> getTeamPatientList(Long patientId) {
        return success(patientService.getTeamPatientList(patientId));
    }


    /**
     * app不分页
     */
    @PostMapping("/listApp")
    public CommonResult<List<PatientRespVO>> getPatientListApp(@RequestBody PatientCreateReqVO createReqVO) {
        return success(patientService.getPatientListApp(createReqVO));
    }

    /**
     * app分页
     */
    @PostMapping("/pageApp")
    public CommonResult<PageResult<PatientRespVO>> getPatientPageApp(@RequestBody PatientPageReqVO pageVO) {
        return success(patientService.getPatientPageApp(pageVO));
    }

    /**
     * app详情
     */
    @GetMapping("/getApp")
    public CommonResult<PatientRespVO> getPatientApp(@RequestParam("id") Long id) {
        return success(patientService.getPatientApp(id));
    }

    /**
     * 病历摘要
     */
    @GetMapping("/diseaseHistory")
    public CommonResult<DiseaseHistoryRespVO> diseaseHistory(Long patientId) {
        return success(patientService.diseaseHistory(patientId));
    }

    /**
     * 检验提醒
     */
    @PostMapping("/inspectionList")
    public CommonResult<List<PatientInspectionRespVO>> inspectionList(@RequestBody PatientCreateReqVO createReqVO) {
        return success(patientService.inspectionList(createReqVO));
    }

    /**
     * 批量修改标签
     */
    @PostMapping("/batchLabels")
    public CommonResult<Boolean> batchLabels(@RequestBody PatientLabelReqVO patientLabelReqVO) {
        patientService.batchLabels(patientLabelReqVO);
        return success(true);
    }

    /***
     * <AUTHOR>
     * @date 2024/4/1 20:25
     * @Description 排班后面显示数量
     **/
    @GetMapping("/getArrangeClassesNumber")
    public CommonResult<Map<String, Integer>> getArrangeClassesNumber() {
        return success(patientService.getArrangeClassesNumber());
    }

    /***
     * <AUTHOR>
     * @date 2024/4/1 20:25
     * @Description 排班后面显示数量
     **/
    @PostMapping("/getArrangeClassesNumbers")
    public CommonResult<Map<String, Integer>> getArrangeClassesNumber(@RequestBody PatientPageReqVO pageVO) {
        return success(patientService.getArrangeClassesNumber(pageVO));
    }

    /****
     * <AUTHOR>
     * @date 2024/5/7 16:46
     * @Description 患者导出
     **/
    @PostMapping("/exportPatientInfo")
    public void exportPatientInfo(@RequestBody PatientPageReqVO patientPageReqVO, HttpServletResponse response) throws IOException {
        patientService.exportPatientInfo(patientPageReqVO, response);
    }

    /**
     * 同步患者信息到his
     */
    @PostMapping("/synPatInfoToHis")
    public CommonResult<Boolean> synPatInfoToHis(@RequestBody PatientRespVO patientRespVO) {
        Boolean result = patientService.synPatInfoToHis(patientRespVO);
        return success(result);
    }

    /**
     * 根据检验医嘱日期查询患者
     */
    @PostMapping("/getPatientListByDate")
    public CommonResult<List<PatientRespVO>> getPatientListByDate(@RequestBody ExamApplyVo examApplyVo ) {
        List<PatientRespVO> result = patientService.getPatientListByDate(examApplyVo);
        return success(result);
    }

    /**
     * 生成检验条码
     */
    @PostMapping("/generateBarcodeByDate")
    public CommonResult<Boolean> generateBarcodeByDate(@RequestBody ExamApplyVo examApplyVo, HttpServletRequest request ) {
        Boolean result = patientService.generateBarcodeByDate(examApplyVo,request);
        return success(true);
    }

    /**
     * 根据医嘱日期及患者ID获取所有检验
     */
    @PostMapping("/getExamList")
    public CommonResult<List<ExamApplyDO>> getExamList(@RequestBody ExamApplyVo examApplyVo ) {
        List<ExamApplyDO> result = patientService.getExamList(examApplyVo);
        return success(result);
    }

    /**
     * 根据医嘱日期及患者ID获取所有检验
     */
    @PutMapping("/updateExamList")
    public CommonResult<Boolean> updateExamList(@RequestBody List<ExamApplyDO> examApplyDOList) {
        Boolean result = patientService.updateExamList(examApplyDOList);
        return success(result);
    }

}
