package com.thj.boot.module.business.service.jkimplementation;


import cn.hutool.core.util.StrUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo;
import com.thj.boot.module.business.controller.admin.gk.vo.EstimateVo;
import com.thj.boot.module.business.controller.admin.gk.vo.MaintainDialysisVo;
import com.thj.boot.module.business.convert.jkimplementation.JkImplementationConvert;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.jkdivision.JkDivisionDO;
import com.thj.boot.module.business.dal.datado.jkeducation.JkEducationDO;
import com.thj.boot.module.business.dal.datado.jkimplementation.JkImplementationDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.jkeducation.JkEducationMapper;
import com.thj.boot.module.business.dal.mapper.jkimplementation.JkImplementationMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationCreateReqVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationExportReqVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationPageReqVO;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationUpdateReqVO;
import com.thj.boot.module.business.service.jkdivision.JkDivisionService;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 健康教育-患教实施 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JkImplementationServiceImpl implements JkImplementationService {

    @Resource
    private JkImplementationMapper jkImplementationMapper;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;
    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private JkEducationMapper jkEducationMapper;

    @Resource
    private JkDivisionService jkDivisionService;

    @Override
    public Long createJkImplementation(JkImplementationCreateReqVO createReqVO) {
        // 插入
        JkImplementationDO jkImplementation = JkImplementationConvert.INSTANCE.convert(createReqVO);
        jkImplementationMapper.insert(jkImplementation);
        // 返回
        return jkImplementation.getId();
    }

    @Override

    public void updateJkImplementation(JkImplementationUpdateReqVO updateReqVO) {
        // 更新
        JkImplementationDO updateObj = JkImplementationConvert.INSTANCE.convert(updateReqVO);
        jkImplementationMapper.updateById(updateObj);
    }

    @Override
    public void deleteJkImplementation(Long id) {
        // 删除
        jkImplementationMapper.deleteById(id);
    }

    @Override
    public JkImplementationDO getJkImplementation(Long id) {
        return jkImplementationMapper.selectById(id);
    }

    @Override
    public List<JkImplementationDO> getJkImplementationList(Collection<Long> ids) {
        return jkImplementationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<JkImplementationDO> getJkImplementationPage(JkImplementationPageReqVO pageReqVO) {
        return jkImplementationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<JkImplementationDO> getJkImplementationList(JkImplementationExportReqVO exportReqVO) {
        return jkImplementationMapper.selectList(exportReqVO);
    }

    @Override
    public void save(ArrangeClassesCreateReqVO vo) {
        JkImplementationDO t = jkImplementationMapper.selectOne(JkImplementationDO::getPatientId, vo.getPatientId());
        //患者信息
        PatientDO p = patientMapper.selectOne(PatientDO::getId, vo.getPatientId());
        FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectOne(FacilitySubareaDO::getId, vo.getFacilitySubareaId());
        FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getId, vo.getFacilityId());

        if (StringUtils.isNull(t)) {
            t = new JkImplementationDO();
            t.setDialyzeTotal(0);
            t.setDialyzeNo(p.getDialyzeNo());
            t.setCunt(0);
            t.setIsGroup("0");
        }
        t.setState("0");
        t.setPatientName(p.getName());
        t.setPinyin(p.getSpellName());
        t.setBinding(p.getState());
        t.setPatientId(vo.getPatientId());
        t.setPatientType(p.getPatientType());
        switch (vo.getDayState()) {
            case "a":
                t.setDayScheduling("1");
                break;
            case "b":
                t.setDayScheduling("2");
                break;
            case "c":
                t.setDayScheduling("3");
                break;
            default:
                t.setDayScheduling("4");
        }
//        t.setDayScheduling(vo.getDayState());
        t.setWard(facilitySubareaDO.getName());
        t.setWardId(vo.getFacilitySubareaId());
        t.setMachineNum(facilityDO.getCode());
        t.setAvatar(p.getAvatar());
        t.setAge(p.getAge());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        t.setDialyzeTime(sdf.format(vo.getClassesTime()));
        jkImplementationMapper.saveOrUpdate(t);
    }


    @Override
    public List<JkImplementationDO> getList(JkImplementationDO vo) {
        if (Collections.emptyList().equals(vo.getWard())) {
            StringBuilder sb = new StringBuilder();
            vo.getWards().forEach(ward -> {
                sb.append("," + ward);
            });
            vo.setWard(sb.substring(1));
        }
        StringBuilder py = new StringBuilder();
        if(!StringUtils.isEmpty(vo.getKeyword())) {
            String more = vo.getKeyword();
            String[] split = more.split("");
            String join = String.join("%", split);
            py.append(join);

        }
        List<Long> collect = patientMapper.selectList(new LambdaQueryWrapperX<PatientDO>()
                .eq(PatientDO::getPatientTypeSource, "00")).stream().map(PatientDO::getId).collect(Collectors.toList());
        MPJLambdaWrapper<JkImplementationDO> wrapper = new MPJLambdaWrapper<JkImplementationDO>(JkImplementationDO.class);
        wrapper.eq(StringUtils.isNotNull(vo.getNurseId()), JkImplementationDO::getNurseId, vo.getNurseId())
                .eq(StrUtil.isNotEmpty(vo.getCreateMouth()), JkImplementationDO::getCreateMouth, vo.getCreateMouth())
                .eq(StrUtil.isNotEmpty(vo.getState()), JkImplementationDO::getState, vo.getState())
                .eq(StrUtil.isNotEmpty(vo.getBinding()), JkImplementationDO::getBinding, vo.getBinding())
                .eq(StrUtil.isNotEmpty(vo.getPatientName()), JkImplementationDO::getPatientName, vo.getPatientName())
                .eq(StrUtil.isNotEmpty(vo.getPatientType()), JkImplementationDO::getPatientType, vo.getPatientType())
                .eq(StrUtil.isNotEmpty(vo.getDialyzeTime()), JkImplementationDO::getDialyzeTime, vo.getDialyzeTime())
                .in(StrUtil.isNotEmpty(vo.getWard()), JkImplementationDO::getWardId, vo.getWard())
                .in(!CollectionUtils.isEmpty(collect),JkImplementationDO::getPatientId,collect)
                .and(
                        StrUtil.isNotEmpty(vo.getKeyword()), i -> i
                                .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getPatientName, vo.getKeyword())
                                .or()
                                .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getPinyin, vo.getKeyword())
                                .or()
                                .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getDialyzeNo, vo.getKeyword())
                                .or()
                                .like(StrUtil.isNotEmpty(py), JkImplementationDO::getPinyin, py.toString())
                )
                .orderByAsc(JkImplementationDO::getPatientId);
        List<JkImplementationDO> list = jkImplementationMapper.selectList(wrapper);
        list.forEach(jkImplementationDO -> {
            //统计教育次数
            Long l = jkEducationMapper.selectCount(JkEducationDO::getPatientId, jkImplementationDO.getPatientId());
            MPJLambdaWrapper<JkEducationDO> w = new MPJLambdaWrapper<JkEducationDO>(JkEducationDO.class).orderByDesc("id");
            w.last("limit 1");
            jkImplementationDO.setCunt(l.intValue());
            JkEducationDO jkImplementationDO1 = jkEducationMapper.selectOne(w);
            if (StringUtils.isNotNull(jkImplementationDO1)) {
                jkImplementationDO.setLastTime(jkImplementationDO1.getEducationTime());
            }
            JkDivisionDO jkDivisionDO = jkDivisionService.queryById(String.valueOf(jkImplementationDO.getPatientId()));
            if(!StrUtil.isEmptyIfStr(jkDivisionDO)){
                jkImplementationDO.setNurseId(jkDivisionDO.getNurseId());
            }
        });
        return list;
    }

    @Override
    public void update(JkImplementationDO v0) {
        jkImplementationMapper.saveOrUpdate(v0);
    }

    @Override
    public List<MaintainDialysisVo> getNewsEducationPlan(MaintainDialysisVo vo) {

        MPJLambdaWrapper<JkImplementationDO> wrapper = new MPJLambdaWrapper<>(JkImplementationDO.class)
                .leftJoin(JkDivisionDO.class, JkDivisionDO::getPatientId, PatientDO::getId)
                .eq(StrUtil.isNotEmpty(vo.getDateWeek()), JkImplementationDO::getDialyzeTime, vo.getDateWeek())
                .eq(StrUtil.isNotEmpty(vo.getWeekDay()), JkImplementationDO::getDayScheduling, vo.getWeekDay())
                .eq(StrUtil.isNotEmpty(vo.getPatientState()), JkImplementationDO::getPatientType, vo.getPatientState())//  新患者/维持性患者
                .eq(StrUtil.isNotEmpty(vo.getFacilitySubareaId()), JkImplementationDO::getWard, vo.getFacilitySubareaId())
                .eq(StrUtil.isNotEmpty(vo.getScheduleTime()), JkImplementationDO::getDialyzeTime, vo.getScheduleTime())
//                .eq(StrUtil.isNotEmpty(vo.getIsGroup()), JkImplementationDO::getIsGroup, vo.getIsGroup())
                .groupBy(true, PatientDO::getId)
                .selectAs(JkImplementationDO::getPatientId, "id")
                .selectAs(JkImplementationDO::getPatientName, "name")
                .selectAs(JkImplementationDO::getDialyzeNo, "dialyzeNo")
                .selectAs(JkImplementationDO::getDialyzeTotal, "dialyzeTotal")
                .selectAs(JkImplementationDO::getNurseId, "nurseId")
                .selectAs(JkImplementationDO::getNurseName, "nurse")
                .selectAs(JkImplementationDO::getWard, "facilityName")
                .selectAs(JkImplementationDO::getWardId, "facilitySubareaId")
                .and(StrUtil.isNotEmpty(vo.getKeyword()), i -> i
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getPatientName, vo.getKeyword())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getPinyin, vo.getKeyword())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), JkImplementationDO::getDialyzeNo, vo.getKeyword()));
        List<MaintainDialysisVo> patientDOS = jkImplementationMapper.selectJoinList(MaintainDialysisVo.class, wrapper);
        if(StrUtil.isNotEmpty(vo.getIsGroup())){
            if("0".equals(vo.getIsGroup())){
                patientDOS = patientDOS.stream().filter(item->"0".equals(item.getIsGroup())|| StrUtil.isEmpty(item.getNurseId())).collect(Collectors.toList());
            }else{
                patientDOS = patientDOS.stream().filter(item->"1".equals(item.getIsGroup()) || StrUtil.isNotEmpty(item.getNurseId())).collect(Collectors.toList());
            }
        }
        return patientDOS;

    }

    @Override
    public JkImplementationDO getById(Long patientId) {
        return jkImplementationMapper.selectOne(JkImplementationDO::getPatientId, patientId);
    }

    @Override
    public List<CollectiveVo> getcollectList(String keyword) {
        return jkImplementationMapper.getcollectList(keyword);
    }

    @Override
    public List<EstimateVo> queryEstimate(EstimateVo vo) {
        List<EstimateVo> estimateVos = jkImplementationMapper.queryEstimate(vo);
        List<Long> collect = patientMapper.selectList(new LambdaQueryWrapperX<PatientDO>()
                .eq(PatientDO::getPatientTypeSource, "00")).stream().map(PatientDO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(estimateVos) && !CollectionUtils.isEmpty(collect)) {
            List<EstimateVo> newEstimateList = new ArrayList<>();
            estimateVos.forEach(estimateVo -> {
                if (collect.contains(Long.valueOf(estimateVo.getPatientId()))) {
                    newEstimateList.add(estimateVo);
                }
            });
            return newEstimateList;
        }
        return estimateVos;
    }


}
