package com.thj.boot.module.business.service.admissionrecord.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.enums.CourseRecordEnum;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.dal.datado.CourseOfDiseaseRecord;
import com.thj.boot.module.business.dal.mapper.CourseOfDiseaseRecordMapper;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordFactory;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordTemple;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RongshuiCourseRecordImpl extends CourseRecordTemple {
    @Resource
    CourseOfDiseaseRecordMapper courseOfDiseaseRecordMapper;
    @Override
    public AdmissionRecordVo getList(Long patientId) {
        AdmissionRecordVo admissionRecordVo = new AdmissionRecordVo();
                CourseOfDiseaseRecord courseOfDiseaseRecord =
                courseOfDiseaseRecordMapper.selectOne(new LambdaQueryWrapper<CourseOfDiseaseRecord>()
                        .eq(CourseOfDiseaseRecord::getPatientId,patientId)
                        .last("limit 1"));
                if(courseOfDiseaseRecord != null){
            BeanUtils.copyProperties(courseOfDiseaseRecord,admissionRecordVo);
        }
        return admissionRecordVo;
    }

    @Override
    public boolean saveOrUpdate(AdmissionRecordVo admissionRecordVo) {
                CourseOfDiseaseRecord courseOfDiseaseRecord = new CourseOfDiseaseRecord();
                BeanUtils.copyProperties(admissionRecordVo,courseOfDiseaseRecord);
                int k = courseOfDiseaseRecordMapper.update(courseOfDiseaseRecord,new LambdaQueryWrapper<CourseOfDiseaseRecord>()
                .eq(CourseOfDiseaseRecord::getPatientId,courseOfDiseaseRecord.getPatientId()));

        if(k == 0){
            courseOfDiseaseRecordMapper.insert(courseOfDiseaseRecord);
        }
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CourseRecordFactory.map.put(CourseRecordEnum.RONGSHUICOURSERECORD.getName(), this);
    }
}
