package com.thj.boot.module.business.service.medicaldischargesummary;

import com.baomidou.mybatisplus.extension.service.IService;
import com.thj.boot.module.business.dal.datado.MedicalDischargeSummary;


/**
* <AUTHOR>
* @description 针对表【medical_discharge_summary(出院小结表)】的数据库操作Service
* @createDate 2025-07-29 11:19:59
*/
public interface MedicalDischargeSummaryService extends IService<MedicalDischargeSummary> {

    MedicalDischargeSummary medicalDischargeSummaryGetById(Long patientId);

    Boolean saveOrUpdateById(MedicalDischargeSummary medicalDischargeSummary);
}
