package com.thj.boot.module.business.controller.admin.dialysisprotocol;

import com.thj.boot.common.annotation.OperateLog;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.dialysisprotocol.DialysisProtocolConvert;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolPageReqVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolRespVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolUpdateReqVO;
import com.thj.boot.module.business.service.dialysisprotocol.DialysisProtocolService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 个性化-透析方案
 */
@RestController
@RequestMapping("/business/dialysis-protocol")
@Validated
public class DialysisProtocolController {

    @Resource
    private DialysisProtocolService dialysisProtocolService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialysisProtocol(@Valid @RequestBody DialysisProtocolCreateReqVO createReqVO) {
        return success(dialysisProtocolService.createDialysisProtocol(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialysisProtocol(@Valid @RequestBody DialysisProtocolUpdateReqVO updateReqVO) {
        dialysisProtocolService.updateDialysisProtocol(updateReqVO);
        return success(true);
    }

    /**
     * 新增或修改
     */
    @PostMapping("/saveOrUpdate")
    @OperateLog("新增或修改透析方案")
    @RepeatSubmit
    public CommonResult<Boolean> saveOrUpdateDialysisProtocol(@Valid @RequestBody DialysisProtocolUpdateReqVO updateReqVO, HttpServletRequest request) {
        dialysisProtocolService.saveOrUpdateDialysisProtocol(updateReqVO,request);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialysisProtocol(@RequestParam("id") Long id) {
        dialysisProtocolService.deleteDialysisProtocol(id);
        return success(true);
    }

    /**
     * 详情
     */
    @PostMapping("/get")
    public CommonResult<DialysisProtocolRespVO> getDialysisProtocol(@RequestBody DialysisProtocolCreateReqVO createReqVO) {
        return success(dialysisProtocolService.getDialysisProtocol(createReqVO));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DialysisProtocolRespVO>> getDialysisProtocolList(DialysisProtocolCreateReqVO createReqVO) {
        List<DialysisProtocolDO> list = dialysisProtocolService.getDialysisProtocolList(createReqVO);
        return success(DialysisProtocolConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialysisProtocolRespVO>> getDialysisProtocolPage(@RequestBody DialysisProtocolPageReqVO pageVO) {
        PageResult<DialysisProtocolDO> pageResult = dialysisProtocolService.getDialysisProtocolPage(pageVO);
        return success(DialysisProtocolConvert.INSTANCE.convertPage(pageResult));
    }


}
