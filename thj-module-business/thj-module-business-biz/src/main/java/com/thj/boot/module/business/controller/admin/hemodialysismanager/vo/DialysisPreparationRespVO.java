package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/3 11:31
 * @description
 */
@Data
public class DialysisPreparationRespVO implements Serializable {
    /**
     * 透析时间
     */
    private Date hemodialysisTime;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 姓名
     */
    private String name;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 住院号
     */
    private String hospitalNo;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 1-男，2-女
     */
    private String sex;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 机号
     */
    private String code;
    /**
     * 分区名称
     */
    private String subareaName;
    /**
     * 机型
     */
    private String facilityTypeId;
    /**
     * 机型名称
     */
    private String facilityTypeName;
    /**
     * 血液透析id
     */
    private Long id;
    /**
     * 机号
     */
    private Long facilityId;
    /**
     * 病区id
     */
    private Long facilitySubareaId;
    /**
     * 透析处方0-未确认，1-已确认
     */
    private Integer prescriptionState;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;

    /**
     * 灌流器2
     */
    private String perfume;
    /**
     * 钾(mmol/L)
     */
    private String potassium;
    /**
     * 钙(mmol/L)
     */
    private String calcium;
    /**
     * 葡萄糖(mmol/L)
     */
    private String glucose;
    /**
     * 血管通路1
     */
    private String vascularAccessOne;
    /**
     * 血管通路2
     */
    private String vascularAccessTwo;
    /**
     * 穿刺针
     */
    private String punctureNeedle;
    /**
     * 穿刺针型号
     */
    private String punctureNeedleModel;
    /**
     * 每天时间段
     */
    private String dayState;
    /**
     * 透析处方
     */
    private String prescriptionName;
    /**
     * 血透器名称
     */
    private String hemodialysisDeviceName;
    /**
     * 血滤器名称
     */
    private String bloodFilterName;
    /**
     * 灌流器名称
     */
    private String perfumerName;
    /**
     * 管理
     */
    private String pipeline;
    /**
     * 穿刺针名称
     */
    private String punctureNeedleName;
    /**
     * 穿刺针型号名称
     */
    private String punctureNeedleModelName;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * bp
     */
    private String bpNoOne;
    /**
     * bp
     */
    private String bpNoTwo;
    /**
     * 透前血压
     */
    private String bp;
    /**
     * 目标脱水
     */
    private String dehydration;
    /**
     * 处方脱水量
     */
    private String prescriptionEhydratedLevel;
    /**
     * 置换总量
     */
    private String substituteTodal;
    /**
     * 超滤总量
     */
    private String beforeUltrafiltrationtotal;
    /**
     * 抗凝剂类型名称
     */
    private String anticoagulant;
    /**
     * 抗凝剂类型名称
     */
    private String anticoagulantName;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 透析方式
     */
    private String dialyzeWayValue;
    /**
     * 透析方式名称
     */
    private String dialyzeWayValueName;
    /**
     * 透析液流量
     */
    private String dialysateFlowrate;
    /**
     * 透析时长小时
     */
    private String duration;
    /**
     * 透析时长分钟
     */
    private String durationMin;
    /**
     * 透析时长
     */
    private String dialysisDuration;
    /**
     * 配方钠(mmol/L)
     */
    private String formulaSodium;
    /**
     * 处方钠
     */
    private String prescriptionSodium;
    /**
     * 碳酸氢根(mmol/L)
     */
    private String bicarbonate;
    /**
     * 人工肾
     */
    private String artificialKidney;

    /**
     * 促红素(推送)
     */
    private String erythropoietinPush;
    /**
     * 促红素(医嘱)
     */
    private String erythropoietin;
    /**
     * 左卡尼汀(推送)
     */
    private String carnitinePush;
    /**
     * 左卡尼汀(医嘱)
     */
    private String carnitine;
    /**
     * 蔗糖铁 (推送)
     */
    private String ironSucrosePush;
    /**
     * 蔗糖铁 (医嘱)
     */
    private String ironSucrose;
    /**
     * 帕立骨化醇(推送)
     */
    private String paricalcitolPush;
    /**
     * 帕立骨化醇(医嘱)
     */
    private String paricalcitol;
    /**
     * 骨化三醇(推送)
     */
    private String calcitriolPush;
    /**
     * 骨化三醇(医嘱)
     */
    private String calcitriol;
    /**
     * 尿激酶 (推送)
     */
    private String urokinasePush;
    /**
     * 尿激酶 (医嘱)
     */
    private String urokinase;
    /**
     * 葡萄糖酸钙(推送)
     */
    private String calciumGluconatePush;
    /**
     * 葡萄糖酸钙(医嘱)
     */
    private String calciumGluconate;

    /**
     * 0-个性化透析处方，1-患者透析处方,2-血液透析
     */
    private Integer protocolType;

    /**
     * 上机换药包
     */
    private Integer upMidPackage;

    /**
     * 下机换药包
     */
    private Integer downMidPackage;

    /**
     * 内瘘穿刺包
     */
    private Integer internalPackage;

    private Long protocolId;
}
