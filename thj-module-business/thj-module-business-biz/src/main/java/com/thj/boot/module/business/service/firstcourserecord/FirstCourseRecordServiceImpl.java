package com.thj.boot.module.business.service.firstcourserecord;

import cn.hutool.core.bean.BeanUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.firstcourserecord.FirstCourseRecordConvert;
import com.thj.boot.module.business.dal.datado.firstcourserecord.FirstCourseRecordDO;
import com.thj.boot.module.business.dal.datado.outpatientblood.OutpatientBloodDO;
import com.thj.boot.module.business.dal.mapper.firstcourserecord.FirstCourseRecordMapper;
import com.thj.boot.module.business.dal.mapper.outpatientblood.OutpatientBloodMapper;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordRespVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordUpdateReqVO;
import com.thj.boot.module.system.dal.datado.user.AdminUserDO;
import com.thj.boot.module.system.dal.mapper.user.AdminUserMapper;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 首次病程记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FirstCourseRecordServiceImpl implements FirstCourseRecordService {

    @Resource
    private FirstCourseRecordMapper firstCourseRecordMapper;

    @Resource
    private OutpatientBloodMapper outpatientBloodMapper;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Override
    public Long createFirstCourseRecord(FirstCourseRecordCreateReqVO createReqVO) {
        // 插入
        FirstCourseRecordDO firstCourseRecord = FirstCourseRecordConvert.INSTANCE.convert(createReqVO);
        firstCourseRecordMapper.insert(firstCourseRecord);
        // 返回
        return firstCourseRecord.getId();
    }

    @Override
    public void updateFirstCourseRecord(FirstCourseRecordUpdateReqVO updateReqVO) {
        // 更新
        FirstCourseRecordDO updateObj = FirstCourseRecordConvert.INSTANCE.convert(updateReqVO);
        firstCourseRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteFirstCourseRecord(Long id) {
        // 删除
        firstCourseRecordMapper.deleteById(id);
    }


    @Override
    public FirstCourseRecordRespVO getFirstCourseRecord(FirstCourseRecordCreateReqVO createReqVO) {
        FirstCourseRecordRespVO firstCourseRecordRespVO = new FirstCourseRecordRespVO();
        FirstCourseRecordDO firstCourseRecordDO = firstCourseRecordMapper.selectOne(FirstCourseRecordDO::getPatientId, createReqVO.getPatientId());
        BeanUtil.copyProperties(firstCourseRecordDO, firstCourseRecordRespVO);
        OutpatientBloodDO outpatientBloodDO = outpatientBloodMapper.selectOne(new LambdaQueryWrapperX<OutpatientBloodDO>()
                .eqIfPresent(OutpatientBloodDO::getPatientId, createReqVO.getPatientId())
                .select(OutpatientBloodDO::getMainSuit, OutpatientBloodDO::getId, OutpatientBloodDO::getPreliminary, OutpatientBloodDO::getPresentDisease));
        if (outpatientBloodDO != null) {
            firstCourseRecordRespVO.setMainSuit(outpatientBloodDO.getMainSuit());
            firstCourseRecordRespVO.setNowDiseaseHistory(outpatientBloodDO.getPresentDisease());
            firstCourseRecordRespVO.setInitDiagnosis(outpatientBloodDO.getPreliminary());
        }
        if (firstCourseRecordDO == null) {
            firstCourseRecordRespVO.setPatientId(createReqVO.getPatientId());
        }
        if (!StringUtils.isEmpty(firstCourseRecordDO) && !StringUtils.isEmpty(firstCourseRecordDO.getSignature())) {
            AdminUserDO adminUserDO = adminUserMapper.selectOne(AdminUserDO::getId, firstCourseRecordDO.getSignature());
            if (!StringUtils.isEmpty(adminUserDO)) {
                firstCourseRecordRespVO.setDoctorName(adminUserDO.getNickname());
            }
        }
        return firstCourseRecordRespVO;
    }

    @Override
    public List<FirstCourseRecordDO> getFirstCourseRecordList(Collection<Long> ids) {
        return firstCourseRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<FirstCourseRecordDO> getFirstCourseRecordPage(FirstCourseRecordPageReqVO pageReqVO) {
        return firstCourseRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<FirstCourseRecordDO> getFirstCourseRecordList(FirstCourseRecordCreateReqVO createReqVO) {
        List<FirstCourseRecordDO> firstCourseRecordDOS = firstCourseRecordMapper.selectList(createReqVO);
        return firstCourseRecordDOS;
    }

}
