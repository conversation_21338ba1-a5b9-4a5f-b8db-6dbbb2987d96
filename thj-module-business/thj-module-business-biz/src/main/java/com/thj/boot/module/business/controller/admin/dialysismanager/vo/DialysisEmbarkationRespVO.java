package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/2 18:50
 * @description
 */
@Data
public class DialysisEmbarkationRespVO extends DialyzeArrangeBaseVO {
    /**
     * 透析处方json
     */
    private String prescription;
    /**
     * 透前json
     */
    private String beforeEvaluate;
    /**
     * 处方状态
     */
    private String prescriptionState;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeigh;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 处方脱水量
     */
    private String prescriptionDehydratedLevel;
    /**
     * 目标脱水量
     */
    private String targetDehydratedLevel;
    /**
     * 置换总量
     */
    private String substituteTodal;
    /**
     * 超滤总量
     */
    private String ultrafiltrationTotal;
    /**
     * 抗凝剂(首剂)(维持/追加)(总量)
     */
    private String anticoagulant;
    /**
     * 血管通路
     */
    private String vascularAccess;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 透析方式
     */
    private String dialyzeWayValue;
    /**
     * 透析时长
     */
    private String duration;
    /**
     * 血滤器=人工肾
     */
    private String bloodFilter;
    /**
     * 钙
     */
    private String calcium;
    /**
     * 甲
     */
    private String potassium;
    /**
     * 配方钠
     */
    private String formulaSodium;
    /**
     * 处方钠
     */
    private String prescriptionSodium;
    /**
     * 碳酸氢根
     */
    private String bicarbonate;
    /**
     * 葡萄糖
     */
    private String glucose;
    /**
     * 备注
     */
    private String remark;
}
