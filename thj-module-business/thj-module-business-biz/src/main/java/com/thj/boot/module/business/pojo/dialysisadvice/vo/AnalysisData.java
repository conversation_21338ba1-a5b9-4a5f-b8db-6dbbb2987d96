package com.thj.boot.module.business.pojo.dialysisadvice.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AnalysisData {
    /**
     * id
     */
    private Long id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 设备唯一号，用于区分不同的设备
     */
    private String deviceCode;

    /**
     * 治疗时间
     */
    private Integer therapyTime;

    /**
     * 动脉压
     */
    private Integer arterialPressure;

    /**
     * 静脉压
     */
    private Integer venousPressure;

    /**
     * 跨膜压
     */
    private Integer tmp;

    /**
     * 收缩压
     */
    private Integer sbp;

    /**
     * 舒张压
     */
    private Integer dbp;

    /**
     * 脉搏
     */
    private Integer pulse;

    /**
     * 钠浓度
     */
    private Integer sodium;

    /**
     * 透析液流量
     */
    private Integer dialysateFlowRate;

    /**
     * 透析液电导度
     */
    private BigDecimal dialysateCond;

    /**
     * 透析液温度
     */
    private BigDecimal dialysateTemp;

    /**
     * 超滤率
     */
    private Integer ufr;

    /**
     * 超滤量
     */
    private Integer ufv;

    /**
     * 血流量
     */
    private Integer bloodFlowVolume;

    /**
     * 肝素量
     */
    private Integer heparinVolume;

    /**
     * 设定治疗时间
     */
    private Integer therapyTimeSet;

    /**
     * 设定超滤量
     */
    private Integer ufvSet;

    /**
     * 设定血流量
     */
    private Integer bloodFlowRateSet;

    /**
     * 设定肝素速率
     */
    private Integer heparinRateSet;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 置换量
     */
    private String displacementQuantity;

    /**
     * 置换率
     */
    private String replacementRate;

    /**
     * ktv
     */
    private String ktv;
    /**
     * 血容量
     */
    private String bloodVolume;
    /**
     * 在线尿素
     */
    private String lineUrea;

    private String breath;
}
