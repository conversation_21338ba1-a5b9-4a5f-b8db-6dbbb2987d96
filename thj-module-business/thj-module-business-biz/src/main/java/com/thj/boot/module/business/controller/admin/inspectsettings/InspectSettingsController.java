package com.thj.boot.module.business.controller.admin.inspectsettings;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.inspectsettings.InspectSettingsConvert;
import com.thj.boot.module.business.dal.datado.inspectsettings.InspectSettingsDO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsCreateReqVO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsPageReqVO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsRespVO;
import com.thj.boot.module.business.pojo.inspectsettings.vo.InspectSettingsUpdateReqVO;
import com.thj.boot.module.business.service.inspectsettings.InspectSettingsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 检验设置
 */
@RestController
@RequestMapping("/business/inspect-settings")
@Validated
public class InspectSettingsController {

    @Resource
    private InspectSettingsService inspectSettingsService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createInspectSettings(@RequestBody InspectSettingsCreateReqVO createReqVO) {
        return success(inspectSettingsService.createInspectSettings(createReqVO));
    }

    /**
     * 新增
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateInspectSettings(@RequestBody InspectSettingsUpdateReqVO updateReqVO) {
        inspectSettingsService.updateInspectSettings(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteInspectSettings(@RequestParam("id") Long id) {
        inspectSettingsService.deleteInspectSettings(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<InspectSettingsRespVO> getInspectSettings(@RequestParam("id") Long id) {
        InspectSettingsDO inspectSettings = inspectSettingsService.getInspectSettings(id);
        return success(InspectSettingsConvert.INSTANCE.convert(inspectSettings));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<InspectSettingsRespVO>> getInspectSettingsList(InspectSettingsCreateReqVO createReqVO) {
        List<InspectSettingsDO> list = inspectSettingsService.getInspectSettingsList(createReqVO);
        return success(InspectSettingsConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<InspectSettingsRespVO>> getInspectSettingsPage(@RequestBody InspectSettingsPageReqVO pageVO) {
        PageResult<InspectSettingsDO> pageResult = inspectSettingsService.getInspectSettingsPage(pageVO);
        return success(InspectSettingsConvert.INSTANCE.convertPage(pageResult));
    }


}
