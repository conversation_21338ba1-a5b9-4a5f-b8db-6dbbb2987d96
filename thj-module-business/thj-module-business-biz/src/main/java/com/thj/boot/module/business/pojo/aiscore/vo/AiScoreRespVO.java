package com.thj.boot.module.business.pojo.aiscore.vo;

import com.thj.boot.module.business.pojo.medicalpage.vo.MedicalPageBaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AiScoreRespVO extends MedicalPageBaseVO {

    private String content;

    /**
     * 专家点评
     */
    private String expertComment;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 评分等级
     */
    private String level;

    /**
     * 详细评分结果
     */
    private AiScoreDetailsVO details;

    /**
     * 原始AI返回的JSON字符串
     */
    private String scoreResult;


    /**
     * 病历查验结果
     */
    private String checkResult;

    /**
     * 风险等级评分
     */
    private String riskResult;

    private String aiRequestJson;

    private  String aiReasoning;
    private  String aiPrompt;

    /**
     * AI报告生成处理时间(秒)
     * 前端可用于展示处理耗时，支持小数位(如2.350秒)
     */
    private BigDecimal processTime;

}
