package com.thj.boot.module.business.controller.admin.facilitysubarea;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.facilitysubarea.FacilitySubareaConvert;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaCreateReqVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaPageReqVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaRespVO;
import com.thj.boot.module.business.pojo.facilitysubarea.vo.FacilitySubareaUpdateReqVO;
import com.thj.boot.module.business.service.facilitysubarea.FacilitySubareaService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/facility-subarea")
@Validated
public class FacilitySubareaController {

    @Resource
    private FacilitySubareaService facilitySubareaService;

    @PostMapping("/create")
    public CommonResult<Long> createFacilitySubarea(@RequestBody FacilitySubareaCreateReqVO createReqVO) {
        return success(facilitySubareaService.createFacilitySubarea(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateFacilitySubarea(@RequestBody FacilitySubareaUpdateReqVO updateReqVO) {
        facilitySubareaService.updateFacilitySubarea(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteFacilitySubarea(@RequestParam("id") Long id) {
        facilitySubareaService.deleteFacilitySubarea(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<FacilitySubareaRespVO> getFacilitySubarea(@RequestParam("id") Long id) {
        return success(facilitySubareaService.getFacilitySubarea(id));
    }

    @GetMapping("/list")
    public CommonResult<List<FacilitySubareaRespVO>> getFacilitySubareaList(FacilitySubareaCreateReqVO createReqVO) {
        List<FacilitySubareaDO> list = facilitySubareaService.getFacilitySubareaList(createReqVO);
        return success(FacilitySubareaConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<FacilitySubareaRespVO>> getFacilitySubareaPage(@RequestBody FacilitySubareaPageReqVO pageVO) {
        return success(facilitySubareaService.getFacilitySubareaPage(pageVO));
    }


}
