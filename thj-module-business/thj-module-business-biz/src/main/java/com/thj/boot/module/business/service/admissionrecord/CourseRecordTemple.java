package com.thj.boot.module.business.service.admissionrecord;

import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.dal.datado.admissionrecord.AdmissionRecord;
import org.springframework.beans.factory.InitializingBean;

public abstract class CourseRecordT<PERSON>ple implements InitializingBean {
    public abstract AdmissionRecordVo getList(Long patientId);
    public abstract boolean saveOrUpdate(AdmissionRecordVo AdmissionRecordVo);
}
