package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/26 11:41
 * @description
 */
@Data
public class PreparationExcelVO implements Serializable {
    /**
     * 出生日期
     */
    @ExcelProperty("出生日期")
    private String birthday;
    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;
    /**
     * 年龄
     */
    @ExcelProperty("年龄")
    private Integer age;
    /**
     * 住院号
     */
    @ExcelProperty("住院号")
    private String hospitalNo;
    /**
     * 透析号
     */
    @ExcelProperty("透析号")
    private String dialyzeNo;
    /**
     * 1-男，2-女
     */
    @ExcelProperty("性别")
    private String sex;
    /**
     * 分区名称
     */
    @ExcelProperty("分区")
    private String subareaName;
    /**
     * 血透器名称
     */
    @ExcelProperty("血透器")
    private String hemodialysisDeviceName;
    /**
     * 血滤器名称
     */
    @ExcelProperty("血滤器")
    private String bloodFilterName;
    /**
     * 灌流器名称
     */
    @ExcelProperty("灌流器")
    private String perfumerName;
    /**
     * 管路
     */
    @ExcelProperty("血管通路")
    private String pipeline;
    /**
     * 穿刺针
     */
    @ExcelProperty("穿刺针")
    private String punctureNeedle;
    /**
     * 穿刺针型号
     */
    @ExcelProperty("穿刺针型号")
    private String punctureNeedleModel;
    /**
     * 透前体重
     */
    @ExcelProperty("透前体重")
    private String dialyzeBeforeWeight;
    /**
     * 干体重
     */
    @ExcelProperty("干体重")
    private String dryWeight;
    /**
     * 透前血压
     */
    @ExcelProperty("透前血压")
    private String bp;
    /**
     * 目标脱水
     */
    @ExcelProperty("目标脱水")
    private String dehydration;
    /**
     * 处方脱水量
     */
    @ExcelProperty("处方脱水量")
    private String prescriptionEhydratedLevel;
    /**
     * 置换总量
     */
    @ExcelProperty("置换总量")
    private String substituteTodal;
    /**
     * 超滤总量
     */
    @ExcelProperty("超滤总量")
    private String beforeUltrafiltrationtotal;
    /**
     * 抗凝剂类型名称
     */
    @ExcelProperty("抗凝剂类型")
    private String anticoagulantName;
    /**
     * 血流量
     */
    @ExcelProperty("血流量")
    private String bloodFlow;
    /**
     * 透析方式名称
     */
    @ExcelProperty("透析方式")
    private String dialyzeWayValueName;
    /**
     * 透析时长
     */
    @ExcelProperty("透析时长")
    private String dialysisDuration;
    /**
     * 配方钠(mmol/L)
     */
    @ExcelProperty("配方钠(mmol/L)")
    private String formulaSodium;
    /**
     * 处方钠
     */
    @ExcelProperty("处方钠")
    private String prescriptionSodium;
    /**
     * 碳酸氢根(mmol/L)
     */
    @ExcelProperty("碳酸氢根(mmol/L)")
    private String bicarbonate;
    /**
     * 促红素(推送)
     */
    @ExcelProperty("促红素(推送)")
    private String erythropoietinPush;
    /**
     * 促红素(医嘱)
     */
    @ExcelProperty("促红素(医嘱)")
    private String erythropoietin;
    /**
     * 左卡尼汀(推送)
     */
    @ExcelProperty("左卡尼汀(推送)")
    private String carnitinePush;
    /**
     * 左卡尼汀(医嘱)
     */
    @ExcelProperty("左卡尼汀(医嘱)")
    private String carnitine;
    /**
     * 蔗糖铁 (推送)
     */
    @ExcelProperty("蔗糖铁 (推送)")
    private String ironSucrosePush;
    /**
     * 蔗糖铁 (医嘱)
     */
    @ExcelProperty("蔗糖铁 (医嘱)")
    private String ironSucrose;
    /**
     * 帕立骨化醇(推送)
     */
    @ExcelProperty("帕立骨化醇(推送)")
    private String paricalcitolPush;
    /**
     * 帕立骨化醇(医嘱)
     */
    @ExcelProperty("帕立骨化醇(医嘱)")
    private String paricalcitol;
    /**
     * 骨化三醇(推送)
     */
    @ExcelProperty("骨化三醇(推送)")
    private String calcitriolPush;
    /**
     * 骨化三醇(医嘱)
     */
    @ExcelProperty("骨化三醇(医嘱)")
    private String calcitriol;
    /**
     * 尿激酶 (推送)
     */
    @ExcelProperty("尿激酶 (推送)")
    private String urokinasePush;
    /**
     * 尿激酶 (医嘱)
     */
    @ExcelProperty("尿激酶 (医嘱)")
    private String urokinase;
    /**
     * 葡萄糖酸钙(推送)
     */
    @ExcelProperty("葡萄糖酸钙(推送)")
    private String calciumGluconatePush;
    /**
     * 葡萄糖酸钙(医嘱)
     */
    @ExcelProperty("葡萄糖酸钙(医嘱)")
    private String calciumGluconate;
}
