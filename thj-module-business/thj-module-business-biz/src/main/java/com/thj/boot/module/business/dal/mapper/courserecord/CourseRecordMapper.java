package com.thj.boot.module.business.dal.mapper.courserecord;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.dal.datado.courserecord.SourceCourseRecordDO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordPageReqVO;
import com.thj.boot.module.business.service.departmentControl.param.WorkloadStatParams;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 病程记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CourseRecordMapper extends BaseMapperX<CourseRecordDO> {
    @Update({
            "<script>",
            "UPDATE wl_course_record SET status = #{status}",
            "WHERE id IN",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    int batchUpdateStatusByIds(@Param("status") String status, @Param("ids") List<Long> ids);

    default PageResult<CourseRecordDO> selectPage(CourseRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CourseRecordDO>()
                .eqIfPresent(CourseRecordDO::getPatientId, reqVO.getPatientId())
                .likeIfPresent(CourseRecordDO::getPatientName, reqVO.getPatientName())
                .likeIfPresent(CourseRecordDO::getPatientNickName, reqVO.getPatientNickName())
                .eqIfPresent(CourseRecordDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(CourseRecordDO::getUserId, reqVO.getUserId())
                .likeIfPresent(CourseRecordDO::getName, reqVO.getName())
                .eqIfPresent(CourseRecordDO::getContent, reqVO.getContent())
                .eqIfPresent(CourseRecordDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CourseRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(CourseRecordDO::getStartTime, reqVO.getStartTime() != null ? DateUtil.beginOfDay(reqVO.getStartTime()) : reqVO.getStartTime(), reqVO.getEndTime() != null ? DateUtil.endOfDay(reqVO.getEndTime()) : reqVO.getEndTime())
                .orderByDesc(CourseRecordDO::getStartTime));
    }

    default List<CourseRecordDO> selectList(CourseRecordCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CourseRecordDO>()
                .eqIfPresent(CourseRecordDO::getPatientId, reqVO.getPatientId())
                .likeIfPresent(CourseRecordDO::getPatientName, reqVO.getPatientName())
                .likeIfPresent(CourseRecordDO::getPatientNickName, reqVO.getPatientNickName())
                .eqIfPresent(CourseRecordDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(CourseRecordDO::getUserId, reqVO.getUserId())
                .likeIfPresent(CourseRecordDO::getName, reqVO.getName())
                .eqIfPresent(CourseRecordDO::getContent, reqVO.getContent())
                .eqIfPresent(CourseRecordDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CourseRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(CourseRecordDO::getStartTime, reqVO.getRecordStartTime() != null ? DateUtil.beginOfDay(reqVO.getRecordStartTime()) : reqVO.getRecordStartTime(),
                        reqVO.getRecordEndTime() != null ? DateUtil.endOfDay(reqVO.getRecordEndTime()) : reqVO.getRecordEndTime())
                .orderByDesc(CourseRecordDO::getId));
    }

    List<Map<String, Object>> getDoctorWorkloadCount(@Param("req") WorkloadStatParams workloadStatParams);

    List<Map<String, Object>> getDoctorWorkloadPage(@Param("req") WorkloadStatParams workloadStatParams);

    List<CourseRecordDO> selectSourceCourseRecord();


    int updateRecord(CourseRecordDO courseRecord);
}
