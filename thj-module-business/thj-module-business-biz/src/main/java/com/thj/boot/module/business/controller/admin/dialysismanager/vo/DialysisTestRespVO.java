package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/4 11:54
 * @description
 */
@Data
public class DialysisTestRespVO extends DialyzeArrangeBaseVO{
    /**
     * 透析处方
     */
    private String prescription;
    /**
     * 透析模式
     */
    private String dialyzeDictValue;
    /**
     * 脉搏
     */
    private String pulse;
    /**
     * 呼吸
     */
    private String breathe;
    /**
     * KT/V(在线)
     */
    private String ktv;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 动脉压
     */
    private String arterialPressure;
    /**
     * 静脉压
     */
    private String venousPressure;
    /**
     * 跨膜压
     */
    private String transmembranePressure;
    /**
     * 超滤率
     */
    private String ultrafiltrationRate;
    /**
     * 超滤量
     */
    private String ultrafiltrationCapacity;
    /**
     * 钠浓度
     */
    private String sodiumConcentration;
    /**
     * 电导度
     */
    private String conductance;
    /**
     * 透析液温度
     */
    private String dialysateTemperature;
    /**
     * 置换率
     */
    private String replacementRate;
    /**
     * 置换量
     */
    private String displacementQuantity;
}
