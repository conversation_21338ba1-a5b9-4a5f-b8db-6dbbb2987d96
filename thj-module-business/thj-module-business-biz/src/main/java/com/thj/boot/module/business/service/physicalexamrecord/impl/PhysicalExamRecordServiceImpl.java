package com.thj.boot.module.business.service.physicalexamrecord.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.thj.boot.module.business.dal.mapper.PhysicalExamRecordMapper;
import com.thj.boot.module.business.service.physicalexamrecord.PhysicalExamRecordService;
import org.springframework.stereotype.Service;
import com.thj.boot.module.business.dal.datado.PhysicalExamRecord;
/**
* <AUTHOR>
* @description 针对表【physical_exam_record(病人体格检查表)】的数据库操作Service实现
* @createDate 2025-07-17 15:23:24
*/
@Service
public class PhysicalExamRecordServiceImpl extends ServiceImpl<PhysicalExamRecordMapper, PhysicalExamRecord>
    implements PhysicalExamRecordService {

}




