package com.thj.boot.module.business.controller.admin.admissionrecord;

import com.deepoove.poi.XWPFTemplate;


import com.thj.boot.module.business.controller.admin.admissionrecord.qry.AdmissionRecordQry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Controller
public class WordController {
    @Autowired
    private ResourceLoader resourceLoader;
    @PostMapping("admin-api/print")
    public void print(@RequestBody AdmissionRecordQry qry, HttpServletResponse response) throws IOException {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy年MM月dd日 HH:mm");
        if("rongshuiAdmissionrecord.docx".equals(qry.getFileName()) || "rongshuiCourseRecord.docx".equals(qry.getFileName())){
            formatter = DateTimeFormatter
                    .ofPattern("yyyy年MM月dd日HH时mm分ss秒");
        }
        if("medicalDischargeSummary.docx".equals(qry.getFileName())){
            formatter = DateTimeFormatter
                    .ofPattern("yyyy年MM月dd日");
        }
        // 3. 格式化输出
//        String result = dateTime.format(formatter);
        if(qry.getAdmissionDate() != null) {
            qry.setAdmissionDateName(qry.getAdmissionDate().format(formatter));
        }
        if(qry.getDiagnosisDate() != null) {
            qry.setDiagnosisDateName(qry.getDiagnosisDate().format(formatter));
        }
        if(qry.getRecordTime() != null) {
            qry.setRecordTimeName(qry.getRecordTime().format(formatter));
        }
        if(qry.getLeaveDate() != null) {
            qry.setLeaveDateName(qry.getLeaveDate().format(formatter));
        }
        if(qry.getLeaveRecordDate() != null) {
            qry.setLeaveRecordDateName(qry.getLeaveRecordDate().format(formatter));
        }
        String templatePath = "classpath:templates/" + qry.getFileName();
        Resource resource = resourceLoader.getResource(templatePath);
//        Resource resource = resourceLoader.getResource(templatePath);


        // 1. 优化文件名编码（兼容所有浏览器）
        String fileName = "导出文档.docx";
        String encodedFileName = "=?UTF-8?B?" +
                Base64.getEncoder().encodeToString(fileName.getBytes(StandardCharsets.UTF_8)) + "?=";

        // 2. 设置响应头（增加缓存控制和跨域支持）
        response.setContentType("application/x-msdownload");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // 禁用缓存
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("Access-Control-Allow-Origin", "*"); // 允许跨域

        // 3. 确保资源关闭并刷新流
        try (InputStream is = resource.getInputStream();
             XWPFTemplate template = XWPFTemplate.compile(is).render(qry);
             OutputStream os = response.getOutputStream()) {

            template.write(os);
            os.flush(); // 强制刷新
        } catch (Exception e) {
            response.reset(); // 重置响应状态
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }
}
