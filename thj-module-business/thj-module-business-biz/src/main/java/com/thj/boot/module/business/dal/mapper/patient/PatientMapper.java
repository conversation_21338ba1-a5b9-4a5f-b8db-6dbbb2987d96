package com.thj.boot.module.business.dal.mapper.patient;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.renalproject.StatisticsVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientFlags;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.service.departmentControl.param.AggregateAnalysisParams;
import com.thj.boot.module.business.service.departmentControl.param.ComplicationResultParams;
import com.thj.boot.module.business.service.departmentControl.param.InfectiousParams;
import com.thj.boot.module.business.service.departmentControl.param.QualityIndexParams;
import com.thj.boot.module.business.service.departmentControl.vo.PatientOutComeVO;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import com.thj.starter.mybatis.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 患者管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PatientMapper extends BaseMapperX<PatientDO> {

    default PageResult<PatientDO> selectPageExport(PatientPageReqVO reqVO) {
        return selectPage(reqVO, new QueryWrapperX<PatientDO>()
                .inIfPresent("id", reqVO.getPatientIdList())
                .likeIfPresent("name", reqVO.getName())
                .likeIfPresent("nick_name", reqVO.getNickName())
                .eqIfPresent("dialyze_no", reqVO.getDialyzeNo())
                .eqIfPresent("sex", reqVO.getSex())
                .eqIfPresent("id_card_type", reqVO.getIdCardType())
                .eqIfPresent("id_card", reqVO.getIdCard())
                .eqIfPresent("birthday", reqVO.getBirthday())
                .eqIfPresent("age", reqVO.getAge())
                .eq("1".equals(reqVO.getPatientSource()) || "2".equals(reqVO.getPatientSource()), "patient_source", reqVO.getPatientSource())
                .eq(StrUtil.isNotEmpty(reqVO.getPatientTypeSource()), "patient_type_source", reqVO.getPatientTypeSource())
                .eqIfPresent("hospital_no", reqVO.getHospitalNo())
//                .likeIfPresent(PatientDO::getEndemicArea, CollectionUtil.isNotEmpty(reqVO.getEndemicAreas()) ? reqVO.getEndemicAreas().stream().collect(Collectors.joining(",")) : null)

                .eqIfPresent("bed_no", reqVO.getBedNo())
                .eqIfPresent("marriage", reqVO.getMarriage())
                .eqIfPresent("apply_way", reqVO.getApplyWay())
                .eqIfPresent("bao_no", reqVO.getBaoNo())
                .eqIfPresent("stature", reqVO.getStature())
                .eqIfPresent("init_weight", reqVO.getInitWeight())
                .eqIfPresent("blood_type", reqVO.getBloodType())
                .eqIfPresent("rh", reqVO.getRh())
                .eqIfPresent("culture", reqVO.getCulture())
                .eqIfPresent("occupation", reqVO.getOccupation())
                .eqIfPresent("smoking", reqVO.getSmoking())
                .eqIfPresent("eyesight", reqVO.getEyesight())
                .eqIfPresent("tipple", reqVO.getTipple())
                .eqIfPresent("religion", reqVO.getReligion())
                .eqIfPresent("nation", reqVO.getNation())
                .eqIfPresent("mobile", reqVO.getMobile())
                .eqIfPresent("mobile_man", reqVO.getMobileMan())
                .eqIfPresent("address", reqVO.getAddress())
                .eqIfPresent("work_unit", reqVO.getWorkUnit())
                .eqIfPresent("dialyze_age", reqVO.getDialyzeAge())
                .eqIfPresent("init_dialyze_no", reqVO.getInitDialyzeNo())
                .eqIfPresent("dialyze_total", reqVO.getDialyzeTotal())
                .likeIfPresent("infect", reqVO.getInfect())
                .eqIfPresent("medic", reqVO.getMedic())
                .eqIfPresent("nurse", reqVO.getNurse())
                .eqIfPresent("describe", reqVO.getDescribe())
                .eqIfPresent("remark", reqVO.getRemark())
                .eqIfPresent("allergy", reqVO.getAllergy())
                .eqIfPresent("tumor", reqVO.getTumor())
                .eqIfPresent("erythrocyte", reqVO.getErythrocyte())
                .eqIfPresent("auth_role", reqVO.getAuthRole())
                .likeIfPresent("auth_name", reqVO.getAuthName())
                .eqIfPresent("auth_id_card", reqVO.getAuthIdCard())
                .eqIfPresent("auth_mobile", reqVO.getAuthMobile())
                .eqIfPresent("avatar", reqVO.getAvatar())
                .likeIfPresent("labels", reqVO.getLabels())
                .in(CollectionUtil.isNotEmpty(reqVO.getEndemicAreas()), "endemic_areas", reqVO.getEndemicAreas())
                .and(StrUtil.isNotEmpty(reqVO.getMore()), i -> i
                        .like("name", reqVO.getMore())
                        .or()
                        .like("spell_name", reqVO.getMore())
                        .or()
                        .like("dialyze_no", reqVO.getMore()))
                .apply(reqVO.getStartTime() != null && reqVO.getEndTime() != null
                        , "id in (select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and classes_time BETWEEN {0} and {1} )"
                        , reqVO.getStartTime() != null ? DateUtil.beginOfDay(reqVO.getStartTime()) : reqVO.getStartTime()
                        , reqVO.getEndTime() != null ? DateUtil.endOfDay(reqVO.getEndTime()) : reqVO.getEndTime())
                .orderByDesc("id")
                .select(reqVO.getFields()));
    }

    default PageResult<PatientDO> selectPage(PatientPageReqVO reqVO) {
        boolean b = reqVO.getDate() != null && reqVO.getDate().length > 1;
        LambdaQueryWrapper<PatientDO> lambdaQueryWrapper = new LambdaQueryWrapperX<PatientDO>()
                .inIfPresent(PatientDO::getId, reqVO.getPatientIdList())
                .likeIfPresent(PatientDO::getName, reqVO.getName())
                .likeIfPresent(PatientDO::getNickName, reqVO.getNickName())
                .eqIfPresent(PatientDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(PatientDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(PatientDO::getSex, reqVO.getSex())
                .eqIfPresent(PatientDO::getCnrdsSyncFlag, reqVO.getCnrdsSyncFlag())
                .eqIfPresent(PatientDO::getIdCardType, reqVO.getIdCardType())
                .eqIfPresent(PatientDO::getIdCard, reqVO.getIdCard())
                .eqIfPresent(PatientDO::getBirthday, reqVO.getBirthday())
                .eqIfPresent(PatientDO::getAge, reqVO.getAge())
                .eq("1".equals(reqVO.getPatientSource()) || "2".equals(reqVO.getPatientSource()), PatientDO::getPatientSource, reqVO.getPatientSource())
                .eq(StrUtil.isNotEmpty(reqVO.getPatientTypeSource()), PatientDO::getPatientTypeSource, reqVO.getPatientTypeSource())
                .eqIfPresent(PatientDO::getHospitalNo, reqVO.getHospitalNo())
//                .likeIfPresent(PatientDO::getEndemicArea, CollectionUtil.isNotEmpty(reqVO.getEndemicAreas()) ? reqVO.getEndemicAreas().stream().collect(Collectors.joining(",")) : null)

                .eqIfPresent(PatientDO::getBedNo, reqVO.getBedNo())
                .eqIfPresent(PatientDO::getMarriage, reqVO.getMarriage())
                .eqIfPresent(PatientDO::getApplyWay, reqVO.getApplyWay())
                .eqIfPresent(PatientDO::getBaoNo, reqVO.getBaoNo())
                .eqIfPresent(PatientDO::getStature, reqVO.getStature())
                .eqIfPresent(PatientDO::getInitWeight, reqVO.getInitWeight())
                .eqIfPresent(PatientDO::getBloodType, reqVO.getBloodType())
                .eqIfPresent(PatientDO::getRh, reqVO.getRh())
                .eqIfPresent(PatientDO::getCulture, reqVO.getCulture())
                .eqIfPresent(PatientDO::getOccupation, reqVO.getOccupation())
                .eqIfPresent(PatientDO::getSmoking, reqVO.getSmoking())
                .eqIfPresent(PatientDO::getEyesight, reqVO.getEyesight())
                .eqIfPresent(PatientDO::getTipple, reqVO.getTipple())
                .eqIfPresent(PatientDO::getReligion, reqVO.getReligion())
                .eqIfPresent(PatientDO::getNation, reqVO.getNation())
                .eqIfPresent(PatientDO::getMobile, reqVO.getMobile())
                .eqIfPresent(PatientDO::getMobileMan, reqVO.getMobileMan())
                .eqIfPresent(PatientDO::getAddress, reqVO.getAddress())
                .eqIfPresent(PatientDO::getWorkUnit, reqVO.getWorkUnit())
                .eqIfPresent(PatientDO::getDialyzeAge, reqVO.getDialyzeAge())
                .eqIfPresent(PatientDO::getInitDialyzeNo, reqVO.getInitDialyzeNo())
                .eqIfPresent(PatientDO::getDialyzeTotal, reqVO.getDialyzeTotal())
                .likeIfPresent(PatientDO::getInfect, reqVO.getInfect())
                .eqIfPresent(PatientDO::getMedic, reqVO.getMedic())
                .eqIfPresent(PatientDO::getNurse, reqVO.getNurse())
                .eqIfPresent(PatientDO::getDescribes, reqVO.getDescribe())
                .eqIfPresent(PatientDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PatientDO::getAllergy, reqVO.getAllergy())
                .eqIfPresent(PatientDO::getTumor, reqVO.getTumor())
                .eqIfPresent(PatientDO::getErythrocyte, reqVO.getErythrocyte())
                .eqIfPresent(PatientDO::getAuthRole, reqVO.getAuthRole())
                .likeIfPresent(PatientDO::getAuthName, reqVO.getAuthName())
                .eqIfPresent(PatientDO::getAuthIdCard, reqVO.getAuthIdCard())
                .eqIfPresent(PatientDO::getAuthMobile, reqVO.getAuthMobile())
                .eqIfPresent(PatientDO::getAvatar, reqVO.getAvatar())
                .likeIfPresent(PatientDO::getLabels, reqVO.getLabels())
                .in(CollectionUtil.isNotEmpty(reqVO.getEndemicAreas()), PatientDO::getEndemicArea, reqVO.getEndemicAreas())
                .eq(PatientDO::getPatientType, "1")
                .and(StrUtil.isNotEmpty(reqVO.getMore()), i -> i
                        .like(PatientDO::getName, reqVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, reqVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, reqVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, reqVO.getSpellShort()))
                .apply(reqVO.getStartTime() != null && reqVO.getEndTime() != null
                        , "id in (select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and classes_time BETWEEN {0} and {1} )"
                        , reqVO.getStartTime() != null ? DateUtil.beginOfDay(reqVO.getStartTime()) : reqVO.getStartTime()
                        , reqVO.getEndTime() != null ? DateUtil.endOfDay(reqVO.getEndTime()) : reqVO.getEndTime());
                if(b){
                    lambdaQueryWrapper.apply("id in (\n" +
                                    "select patient_id from wl_outcome_record \n" +
                                    "where type = '16'\n" +
                                    "group by patient_id HAVING\n" +
                                    "max(prognosis_time) >= {0} and max(prognosis_time) <= {1}\n" +
                                    ")", reqVO.getDate()[0] ,
                            reqVO.getDate()[1] );
                }

        return selectPage(reqVO, lambdaQueryWrapper
        );
    }

    default List<PatientDO> selectList(PatientCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PatientDO>()
                .inIfPresent(PatientDO::getId, reqVO.getPatientIds())
                .likeIfPresent(PatientDO::getName, reqVO.getName())
                .likeIfPresent(PatientDO::getNickName, reqVO.getNickName())
                .likeIfPresent(PatientDO::getEndemicArea, CollectionUtil.isNotEmpty(reqVO.getEndemicAreas()) ? reqVO.getEndemicAreas().stream().collect(Collectors.joining(",")) : null)
                .eqIfPresent(PatientDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(PatientDO::getSex, reqVO.getSex())
                .eqIfPresent(PatientDO::getIdCardType, reqVO.getIdCardType())
                .eqIfPresent(PatientDO::getIdCard, reqVO.getIdCard())
                .eqIfPresent(PatientDO::getBirthday, reqVO.getBirthday())
                .eqIfPresent(PatientDO::getAge, reqVO.getAge())
                .eqIfPresent(PatientDO::getPatientSource, reqVO.getPatientSource())
                .eq(StrUtil.isNotEmpty(reqVO.getPatientTypeSource()), PatientDO::getPatientTypeSource, reqVO.getPatientTypeSource())
                .eqIfPresent(PatientDO::getHospitalNo, reqVO.getHospitalNo())
                .eqIfPresent(PatientDO::getEndemicArea, reqVO.getEndemicArea())
                .eqIfPresent(PatientDO::getBedNo, reqVO.getBedNo())
                .eqIfPresent(PatientDO::getMarriage, reqVO.getMarriage())
                .eqIfPresent(PatientDO::getApplyWay, reqVO.getApplyWay())
                .eqIfPresent(PatientDO::getBaoNo, reqVO.getBaoNo())
                .eqIfPresent(PatientDO::getStature, reqVO.getStature())
                .eqIfPresent(PatientDO::getInitWeight, reqVO.getInitWeight())
                .eqIfPresent(PatientDO::getBloodType, reqVO.getBloodType())
                .eqIfPresent(PatientDO::getRh, reqVO.getRh())
                .eqIfPresent(PatientDO::getCulture, reqVO.getCulture())
                .eqIfPresent(PatientDO::getOccupation, reqVO.getOccupation())
                .eqIfPresent(PatientDO::getSmoking, reqVO.getSmoking())
                .eqIfPresent(PatientDO::getEyesight, reqVO.getEyesight())
                .eqIfPresent(PatientDO::getTipple, reqVO.getTipple())
                .eqIfPresent(PatientDO::getReligion, reqVO.getReligion())
                .eqIfPresent(PatientDO::getNation, reqVO.getNation())
                .eqIfPresent(PatientDO::getMobile, reqVO.getMobile())
                .eqIfPresent(PatientDO::getMobileMan, reqVO.getMobileMan())
                .eqIfPresent(PatientDO::getAddress, reqVO.getAddress())
                .eqIfPresent(PatientDO::getWorkUnit, reqVO.getWorkUnit())
                .eqIfPresent(PatientDO::getDialyzeAge, reqVO.getDialyzeAge())
                .eqIfPresent(PatientDO::getInitDialyzeNo, reqVO.getInitDialyzeNo())
                .eqIfPresent(PatientDO::getDialyzeTotal, reqVO.getDialyzeTotal())
                .eqIfPresent(PatientDO::getMedic, reqVO.getMedic())
                .eqIfPresent(PatientDO::getNurse, reqVO.getNurse())
                .eqIfPresent(PatientDO::getDescribes, reqVO.getDescribes())
                .eqIfPresent(PatientDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PatientDO::getAllergy, reqVO.getAllergy())
                .eqIfPresent(PatientDO::getTumor, reqVO.getTumor())
                .eqIfPresent(PatientDO::getErythrocyte, reqVO.getErythrocyte())
                .eqIfPresent(PatientDO::getAuthRole, reqVO.getAuthRole())
                .likeIfPresent(PatientDO::getAuthName, reqVO.getAuthName())
                .eqIfPresent(PatientDO::getAuthIdCard, reqVO.getAuthIdCard())
                .eqIfPresent(PatientDO::getAuthMobile, reqVO.getAuthMobile())
                .eqIfPresent(PatientDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(PatientDO::getLabels, reqVO.getLabels())
                .isNotNull(StrUtil.isNotEmpty(reqVO.getInfect()) && "1".equals(reqVO.getInfect()), PatientDO::getInfect)
                .and(StrUtil.isNotEmpty(reqVO.getMore()), i -> i
                        .like(PatientDO::getName, reqVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, reqVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, reqVO.getMore()))
                .apply(reqVO.getStartTime() != null && reqVO.getEndTime() != null
                        , "id in (select DISTINCT patient_id from wl_arrange_classes where temp_type =0 and classes_time BETWEEN {0} and {1} )"
                        , reqVO.getStartTime() != null ? DateUtil.beginOfDay(reqVO.getStartTime()) : reqVO.getStartTime()
                        , reqVO.getEndTime() != null ? DateUtil.endOfDay(reqVO.getEndTime()) : reqVO.getEndTime())
                .apply(StrUtil.isNotEmpty(reqVO.getInfect()) && "1".equals(reqVO.getInfect()), " LENGTH(trim(infect))>0")
                .apply(StrUtil.isNotEmpty(reqVO.getInfect()) && "2".equals(reqVO.getInfect()), " infect='' or infect is null")
                .orderByDesc(PatientDO::getId));
    }


    @Select("<script> SELECT a.id, a.name,a.dialyze_no as dialyzeNo, a.dialyze_total as dialyzeTotal,a.patient_state,b.education_methods as educationalForms," +
            "b" +
            ".education_time as EducationTime  FROM wl_patient a LEFT JOIN (SELECT MAX(id) AS id, patient_id,education_methods, create_time," +
            "education_time FROM wl_jk_education GROUP BY patient_id ORDER BY MAX(id) DESC) b ON a.id = b.patient_id where 1=1 " +
            "<if test='keyWord !=null'> " +
            "and(a.name like concat(\"%\",#{keyWord},\"%\") or a.spell_name like concat(\"%\",#{keyWord},\"%\") or a.dialyze_no like " +
            "concat(\"%\",#{keyWord},\"%\") ) \n" +
            "</if>" +
            "</script>")
    List<CollectiveVo> getCollective(String keyWord);

    Long getInfectionCompletionRate(@Param("req") InfectiousParams infectiousParams);

    Long getInfectionCompletionRateNew(@Param("req") InfectiousParams infectiousParams);

    Page<Map<String, Object>> getInfectionCompletionRatePage(@Param("page") Page<Object> page, @Param("req") InfectiousParams infectiousParams);

    Page<Map<String, Object>> getInfectionCompletionRatePageNew(@Param("page") Page<Object> page, @Param("req") InfectiousParams infectiousParams);

    Long getQualityIndexCompletionRate(@Param("req") QualityIndexParams qualityIndexParams);

    List<StatisticsVO> getQualityIndexCompletionStatisticsRate(@Param("req") QualityIndexParams qualityIndexParams);

    Page<Map<String, Object>> getCompletionRatePage(@Param("page") Page<Object> page, @Param("req") QualityIndexParams qualityIndexParams);

    Long getKTVURRControlRateCount(@Param("req") QualityIndexParams qualityIndexParams);

    List<StatisticsVO> getKTVURRControlStatisticsRateCount(@Param("req") QualityIndexParams qualityIndexParams);

    Page<Map<String, Object>> getKTVURRControlRatePage(@Param("page") Page<Object> page, @Param("req") QualityIndexParams qualityIndexParams);


    Long getTotalPatientAnalysisInfoTotal(@Param(value = "req") AggregateAnalysisParams params);

    Page<Map<String, Object>> getTotalPatientAnalysisInfoPage(@Param(value = "page") IPage page, @Param(value = "req") AggregateAnalysisParams params);

    Map<String, Object> getPatientInfoJoinReason(@Param("patientId") Long patientId);


    List<PatientRespVO> selectTransferoutPatients(@Param(value = "req") AggregateAnalysisParams clone);

    void updateAgeById(PatientDO patientDO);


    List<PatientOutComeVO> getPatientListWithPrognosisTime(@Param(value = "req") ComplicationResultParams complicationResultParams);

    @Select("SELECT * FROM wl_patient a LEFT JOIN wl_outcome_record b on a.id = b.patient_id WHERE b.id is NULL AND a.patient_type_source = '00' and a.deleted = 0")
    List<PatientDO> getWithOutOutCome();

    Map<String, BigDecimal> selectAgeTotal(AggregateAnalysisParams aggregateAnalysisParams);

    List<PatientFlags> selectPatientFlags(@Param("patientIds") List<PatientRespVO> list);

    void updatePatientType();
}
