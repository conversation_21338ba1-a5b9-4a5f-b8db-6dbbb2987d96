package com.thj.boot.module.business.controller.admin.gk;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.gktrain.GkTrainConvert;
import com.thj.boot.module.business.dal.datado.gktrain.GkTrainDO;
import com.thj.boot.module.business.dal.mapper.gkinroles.GkInRolesMapper;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainCreateReqVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainPageReqVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainRespVO;
import com.thj.boot.module.business.pojo.gktrain.vo.GkTrainUpdateReqVO;
import com.thj.boot.module.business.service.gktrain.GkTrainService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 感控培训
 */

@RestController
@RequestMapping("/business/gk-train")
@Validated
public class GkTrainController {

    @Resource
    private GkTrainService gkTrainService;

    @Resource
    private GkInRolesMapper gkInRolesMapper;

    /**
     * 新增
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    public CommonResult<Long> createGkTrain(@RequestBody GkTrainCreateReqVO createReqVO) {
        if(createReqVO.getPlanTime() == null){
            createReqVO.setPlanTime(new Date());
        }
//        createReqVO.setPlanTime(new Date());
        return success(gkTrainService.createGkTrain(createReqVO));
    }

    /**
     * 修改
     * @param gkTrainDO
     * @return
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateGkTrain(@RequestBody GkTrainDO gkTrainDO) {
        gkTrainService.updateGkTrain(gkTrainDO);
        return success(true);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteGkTrain(@RequestParam("id") Long id) {
        gkTrainService.deleteGkTrain(id);
        return success(true);
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @GetMapping("/get")
    public CommonResult<GkTrainDO> getGkTrain(@RequestParam("id") Long id) {
        GkTrainDO gkTrain = gkTrainService.getGkTrain(id);
        return success(gkTrain);
    }

    /**
     * 分页列表
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    public CommonResult<PageResult<GkTrainRespVO>> getGkTrainPage(GkTrainPageReqVO pageVO) {
        PageResult<GkTrainDO> pageResult = gkTrainService.getGkTrainPage(pageVO);
        return success(GkTrainConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 统计当每年每月培训数量
     * @param year
     * @return
     */

    @GetMapping("/count")
    public CommonResult<List<GkTrainRespVO>> count(@RequestParam String year) {
        List<GkTrainRespVO> mouthList = gkTrainService.getMouthList(year);
        return success(mouthList);
    }

    /**
     * 根据年月份查询感控培训列表
     * @param vo
     * @return
     */
    @GetMapping("/list")
    public CommonResult<List<GkTrainDO>> getGkTrainList(GkTrainUpdateReqVO vo) {
        if("0".equals(vo.getMouth())){
            vo.setMouth(null);
        }
        List<GkTrainDO> list = gkTrainService.queryListOnMouth(vo);
        return success(list);
    }

    /**
     * 年份
     * @return
     */
    @GetMapping("/getByYear")
    public CommonResult<List<Integer>> getByYear(){
        List<Integer> list = new ArrayList<>();
        int currentYear = LocalDate.now().getYear();  // 获取当前年份
        list.add(currentYear-4);
        list.add(currentYear-3);
        list.add(currentYear-2);
        list.add(currentYear-1);
        list.add(currentYear);
        list.add(currentYear+1);
        return success(list);
    }

    /**
     * 每个月数据列表以及统计
     * @param year
     * @return
     */
    @GetMapping("/eachmonth")
    public CommonResult<List<GkTrainRespVO>> eachmonth(@RequestParam String year) {
        List<GkTrainRespVO> list =  new ArrayList<>();
        for(Integer i = 1; i<13; i++){ //一到12个月
            GkTrainRespVO vo = new GkTrainRespVO();
            vo.setMouthTime(i.toString());
            GkTrainUpdateReqVO gkTrainUpdateReqVO = new GkTrainUpdateReqVO();
            gkTrainUpdateReqVO.setYear(year);
            gkTrainUpdateReqVO.setMouth(i.toString());
            List<GkTrainDO> gkTrainDOS = gkTrainService.queryListOnMouth(gkTrainUpdateReqVO);
            vo.setList(gkTrainDOS);
            list.add(vo);
        }
        return success(list);
    }


}
