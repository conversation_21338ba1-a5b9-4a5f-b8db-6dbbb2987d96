package com.thj.boot.module.business.controller.admin.vindicateregister;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.vindicateregister.VindicateRegisterConvert;
import com.thj.boot.module.business.dal.datado.vindicateregister.VindicateRegisterDO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterPageReqVO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterRespVO;
import com.thj.boot.module.business.pojo.vindicateregister.vo.VindicateRegisterUpdateReqVO;
import com.thj.boot.module.business.service.vindicateregister.VindicateRegisterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 维护登记透析机-水处理机-CCDS-CDDS
 */
@RestController
@RequestMapping("/business/vindicate-register")
@Validated
public class VindicateRegisterController {

    @Resource
    private VindicateRegisterService vindicateRegisterService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createVindicateRegister(@RequestBody VindicateRegisterCreateReqVO createReqVO) {
        return success(vindicateRegisterService.createVindicateRegister(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateVindicateRegister(@RequestBody VindicateRegisterUpdateReqVO updateReqVO) {
        vindicateRegisterService.updateVindicateRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteVindicateRegister(@RequestParam("id") Long id) {
        vindicateRegisterService.deleteVindicateRegister(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<VindicateRegisterRespVO> getVindicateRegister(@RequestParam("id") Long id) {
        VindicateRegisterDO vindicateRegister = vindicateRegisterService.getVindicateRegister(id);
        return success(VindicateRegisterConvert.INSTANCE.convert(vindicateRegister));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<VindicateRegisterRespVO>> getVindicateRegisterList(VindicateRegisterCreateReqVO createReqVO) {
        List<VindicateRegisterDO> list = vindicateRegisterService.getVindicateRegisterList(createReqVO);
        return success(VindicateRegisterConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<VindicateRegisterRespVO>> getVindicateRegisterPage(@RequestBody VindicateRegisterPageReqVO pageVO) {
        PageResult<VindicateRegisterDO> pageResult = vindicateRegisterService.getVindicateRegisterPage(pageVO);
        return success(VindicateRegisterConvert.INSTANCE.convertPage(pageResult));
    }


}
