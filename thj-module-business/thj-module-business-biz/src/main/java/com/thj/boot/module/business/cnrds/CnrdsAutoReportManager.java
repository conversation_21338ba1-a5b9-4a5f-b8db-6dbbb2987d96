package com.thj.boot.module.business.cnrds;

import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.thj.boot.common.dataobject.BaseDO;
import com.thj.boot.common.enums.YnEnum;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.pojo.PageParam;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.DateUtils;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.common.utils.aes.EncryptUtils;
import com.thj.boot.common.utils.redis.RedisUtils;
import com.thj.boot.module.business.cnrds.dto.CarResponseResultDTO;
import com.thj.boot.module.business.cnrds.dto.CmrdsAutoReportDataDTO;
import com.thj.boot.module.business.cnrds.dto.CmrdsAutoReportDataFormDTO;
import com.thj.boot.module.business.cnrds.form.*;
import com.thj.boot.module.business.dal.datado.cnrds.CnrdsAccountConfigDO;
import com.thj.boot.module.business.dal.datado.cnrds.CnrdsReportManagerDO;
import com.thj.boot.module.business.dal.datado.cnrds.CnrdsReportRecordDO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.dal.datado.outcomerecord.OutcomeRecordDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.mapper.cnrds.CnrdsAccountConfigMapper;
import com.thj.boot.module.business.dal.mapper.cnrds.CnrdsReportManagerMapper;
import com.thj.boot.module.business.dal.mapper.cnrds.CnrdsReportRecordMapper;
import com.thj.boot.module.business.dal.mapper.dialysisrecord.DialysisRecordMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.mottosimple.MottoSimpleMapper;
import com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadCreateReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadRespVO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolRespVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectRespVO;
import com.thj.boot.module.business.pojo.renalprojectinfo.vo.RenalProjectInfoRespVO;
import com.thj.boot.module.business.service.bloodroad.BloodRoadService;
import com.thj.boot.module.business.service.dialysisprotocol.DialysisProtocolService;
import com.thj.boot.module.business.service.dialyzeoption.DialyzeOptionService;
import com.thj.boot.module.business.service.mottohard.MottoHardService;
import com.thj.boot.module.business.service.renalproject.RenalProjectService;
import com.thj.boot.module.business.utils.HttpUtils;
import com.thj.boot.module.business.utils.ProxyIpManager;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.dal.datado.dept.DeptDO;
import com.thj.boot.module.system.dal.datado.dict.DictDataDO;
import com.thj.boot.module.system.dal.mapper.dept.DeptMapper;
import com.thj.boot.module.system.service.dict.DictDataService;
import com.thj.starter.mybatis.query.QueryWrapperX;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class CnrdsAutoReportManager {

    private static String CNRDS_CACHE_TOKEN_KEY = "cnrdsAccessToken:";

    @Value("${cnrds.getAccessTokenUrl}")
    private String getAccessTokenUrl;

    @Value("${cnrds.checkFormDataUrl}")
    private String checkFormDataUrl;

    @Value("${cnrds.reportDataUrl}")
    private String reportDataUrl;

    @Resource
    private PatientMapper patientMapper;

    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private CnrdsReportRecordMapper cnrdsReportRecordMapper;

    @Autowired
    private CnrdsReportManagerMapper cnrdsReportManagerMapper;

    @Resource
    private DialyzeOptionService dialyzeOptionService;

    @Resource
    private DialysisProtocolService dialysisProtocolService;

    @Resource
    private RenalProjectService renalProjectService;

    @Resource
    private MottoSimpleMapper mottoSimpleMapper;

    @Resource
    private OutcomeRecordMapper outcomeRecordMapper;

    @Resource
    private DialysisRecordMapper dialysisRecordMapper;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private BloodRoadService bloodRoadService;

    @Resource
    private MottoHardService mottoHardService;

    @Resource
    private CnrdsAccountConfigMapper cnrdsAccountConfigMapper;

    @Resource
    private DeptMapper deptMapper;


    public CnrdsTokenInfo getAccessToken(Long deptId){
        Object token = RedisUtils.getCacheObject(CNRDS_CACHE_TOKEN_KEY + deptId);
        if(token != null){
            return (CnrdsTokenInfo)token;
        }
        QueryWrapper<CnrdsAccountConfigDO> acQueryWrapper = new QueryWrapperX<>();
        acQueryWrapper.lambda().eq(CnrdsAccountConfigDO::getConfigDeptId, deptId).last("limit 1");
        CnrdsAccountConfigDO cnrdsAccountConfigDO = cnrdsAccountConfigMapper.selectOne(acQueryWrapper);
        if(cnrdsAccountConfigDO == null){
            throw new ServiceException(500, "国网上报账号未配置");
        }

        String content = EncryptUtils.enCode(cnrdsAccountConfigDO.getReportSecret(), cnrdsAccountConfigDO.getEncryptionKey());
        Map<String, String> headers = new HashMap<>();
        headers.put("reportId", cnrdsAccountConfigDO.getReportId());
        JSONObject body = new JSONObject();
        body.put("reportSecret", content);
        String result = "";
        ProxyIpManager proxyIpManager = null;
        if(StringUtils.isNotEmpty(cnrdsAccountConfigDO.getIpHost())){
            proxyIpManager = new ProxyIpManager();
            proxyIpManager.setProxyHost(cnrdsAccountConfigDO.getIpHost());
            proxyIpManager.setProxyPort(cnrdsAccountConfigDO.getIpPort());
            proxyIpManager.setProxyUsername(cnrdsAccountConfigDO.getIpUsername());
            proxyIpManager.setProxyPassword(cnrdsAccountConfigDO.getIpPwd());
            result = HttpUtils.postJsonProxy(getAccessTokenUrl, headers, body.toString(), proxyIpManager);
        }else{
            result = HttpUtils.postJson(getAccessTokenUrl, headers, body.toString());
        }

        JSONObject resultObject = JSONObject.parseObject(result);
        if(resultObject.getBoolean("success")){
            JSONObject data = JSONObject.parseObject(resultObject.get("data").toString());
            String accessToken = data.getString("accessToken");
            CnrdsTokenInfo cnrdsTokenInfo = new CnrdsTokenInfo(cnrdsAccountConfigDO.getReportId(), accessToken
                    , cnrdsAccountConfigDO.getReportSecret(), cnrdsAccountConfigDO.getEncryptionKey(), proxyIpManager);
            RedisUtils.setCacheObject(CNRDS_CACHE_TOKEN_KEY + deptId, cnrdsTokenInfo, Duration.ofSeconds(6000));
            return cnrdsTokenInfo;
        }
        throw new ServiceException(500, "调用cnrds接口凭证异常." + resultObject.getString("msg"));
    }



    /**
     * 表单数据校验
     * @param cmrdsAutoReportDataDTO
     */
    public CarResponseResultDTO checkFormData(CmrdsAutoReportDataDTO cmrdsAutoReportDataDTO, Long deptId){
        CnrdsTokenInfo tokenInfo = getAccessToken(deptId);
        Map<String, String> headers = new HashMap<>();
        headers.put("reportId", tokenInfo.getReportId());
        headers.put("reportAuthorization", tokenInfo.getAccessToken());

        String dataStr = JSONObject.toJSON(cmrdsAutoReportDataDTO).toString();
        String dataEnc = EncryptUtils.enCode(dataStr, tokenInfo.getEncryptionKey());
        JSONObject body = new JSONObject();
        body.put("data", dataEnc);
        String result = HttpUtils.postJson(checkFormDataUrl, headers, body.toString());
        return JSONObject.parseObject(result, CarResponseResultDTO.class);
    }

    /**
     * 上报数据
     * @param cmrdsAutoReportDataDTO
     */
    public CarResponseResultDTO reportData(CmrdsAutoReportDataDTO cmrdsAutoReportDataDTO, Long deptId){
        CnrdsTokenInfo tokenInfo = getAccessToken(deptId);
        Map<String, String> headers = new HashMap<>();
        headers.put("reportId", tokenInfo.getReportId());
        headers.put("reportAuthorization", tokenInfo.getAccessToken());

        String dataStr = JSONObject.toJSON(cmrdsAutoReportDataDTO).toString();
        String dataEnc = EncryptUtils.enCode(dataStr, tokenInfo.getEncryptionKey());
        JSONObject body = new JSONObject();
        body.put("data", dataEnc);
        String result = "";
        if(tokenInfo.getProxyIpManager() != null && StringUtils.isNotEmpty(tokenInfo.getProxyIpManager().getProxyHost())){
            result = HttpUtils.postJsonProxy(reportDataUrl, headers, body.toString(), tokenInfo.getProxyIpManager());
        }else{
            result = HttpUtils.postJson(reportDataUrl, headers, body.toString());
        }
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println( dataStr + "+++++++++++" +result);
/*        if (result.contains("json格式错误,通路")) {
            System.out.println("\""+ cmrdsAutoReportDataDTO.getPatIdNum()+ "\"");
        }*/

        return JSONObject.parseObject(result, CarResponseResultDTO.class);
    }

    /**
     * 上报数据定时任务
     * @param configMonth 测试使用参数
     */
    public void reportDataTask(Long reportManagerId, Integer configMonth, Long localDeptId, Integer configYear){
        //当前月
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MARCH) + 1;

        //当前年份
        int currentYear = calendar.get(Calendar.YEAR);
        //上报的季度
        int reportQuarter;
        //上报的月份
        int reportMonth;
        //上报的半年度
        int reportHy;
        //半年度上报的年份
        int hyReportYear;
        //月报表上报的年份
        int monthReportYear;
        //季报表上报的年份
        int quarterReportYear;
        //月
        if(currentMonth == 1){
            reportMonth = 12;
            monthReportYear = currentYear - 1;
        }else{
            reportMonth = currentMonth - 1;
            monthReportYear = currentYear;
        }
        //季度
        if(currentMonth >= 1 && currentMonth <=3){
            reportQuarter = 4;
            quarterReportYear = currentYear - 1;
        }else if(currentMonth >= 4 && currentMonth <=6){
            reportQuarter = 1;
            quarterReportYear = currentYear;
        }else if(currentMonth >= 7 && currentMonth <=9){
            reportQuarter = 2;
            quarterReportYear = currentYear;
        }else{
            reportQuarter = 3;
            quarterReportYear = currentYear;
        }
        //上半年1/下半年2
        if(currentMonth >= 1 && currentMonth <= 6){
            reportHy = 2;
            hyReportYear = currentYear - 1;
        }else{
            reportHy = 1;
            hyReportYear = currentYear;
        }

        //指定月份上报。 自动上报为，当月初上报上个月，当季度初上报上季度
        if(configMonth != null && configMonth > 0){
            reportMonth = configMonth;
            monthReportYear = currentYear;
            hyReportYear = currentYear;
            quarterReportYear = currentYear;

            //指定年份不为空
            if(configYear != null){
                monthReportYear = configYear;
                hyReportYear = configYear;
                quarterReportYear = configYear;
            }

            //季度
            if(configMonth >= 1 && configMonth <=3){
                reportQuarter = 1;
            }else if(configMonth >= 4 && configMonth <=6){
                reportQuarter = 2;
            }else if(configMonth >= 7 && configMonth <=9){
                reportQuarter = 3;
            }else{
                reportQuarter = 4;
            }
            //上半年1/下半年2
            if(configMonth >= 1 && configMonth <= 6){
                reportHy = 1;
            }else{
                reportHy = 2;
            }
        }

        //查询所有门店
        List<DeptDO> deptDOS;
        if(configMonth != null && localDeptId != null){
            DeptDO deptDO = deptMapper.selectById(localDeptId);
            deptDOS = ListUtils.newArrayList(deptDO);
        } else if(reportManagerId == null){
            deptDOS = deptMapper.selectList();
        }else{
            CnrdsReportManagerDO cnrdsReportManagerDO = cnrdsReportManagerMapper.selectById(reportManagerId);
            DeptDO deptDO = deptMapper.selectById(cnrdsReportManagerDO.getDeptId());
            deptDOS = ListUtils.newArrayList(deptDO);
        }
        for (DeptDO deptDO : deptDOS) {
            //查询当前门店的数据
            QueryWrapper<CnrdsReportManagerDO> reportManagerQueryWrapper = new QueryWrapper<>();
            reportManagerQueryWrapper.lambda().eq(BaseDO::getDeptId, deptDO.getId());
            if(reportManagerId != null){
                reportManagerQueryWrapper.lambda().eq(CnrdsReportManagerDO::getId, reportManagerId);
            }else{
                reportManagerQueryWrapper.lambda().eq(CnrdsReportManagerDO::getSyncFlag, "N")
                        .orderByDesc(CnrdsReportManagerDO::getId);
            }

            int pageNo = 1;
            PageParam pageParam = new PageParam();
            pageParam.setPageSize(1000);
            int updateCount = 0;
            while (true){
                pageParam.setPageNo(pageNo);
                PageResult<CnrdsReportManagerDO> pageResult = cnrdsReportManagerMapper.selectPage(pageParam, reportManagerQueryWrapper);
                if(pageResult == null || CollectionUtils.isEmpty(pageResult.getList())){
                    break;
                }
                //最大调用次数
                if(updateCount == 1000){
                    break;
                }

                List<CnrdsReportManagerDO> list = pageResult.getList().stream().sorted(Comparator.comparingLong(v->v.getPatientId())).collect(Collectors.toList());
               //list = list.stream().filter(v->v.getPatientName().equals("杨友勇")).collect(Collectors.toList());
                //list = list.stream().filter(v->"陈燕珍".equals(v.getPatientName())).collect(Collectors.toList());
                for (CnrdsReportManagerDO managerDO : list) {
                    List<CmrdsAutoReportDataFormDTO> dataList = new ArrayList<>();
                    PatientDO patientDO = patientMapper.selectById(managerDO.getPatientId());
                    //患者转出
//                    if("01".equals(patientDO.getPatientTypeSource())){
//                        continue;
//                    }

                    //记录的年份
                    int year = Integer.valueOf(managerDO.getYear());
                    //血压
                    JSONObject bloodPressureJson = null;
                    //透析处方
                    JSONObject dialysisPrescriptionJson = null;
                    //抗凝剂
                    JSONObject decoagulantJson = null;
                    //透析充分性
                    JSONObject dialysisAdequacyJson = null;
                    //实验室检查
                    JSONObject laboratoryInspectionJson = null;
                    //血管通路
                    JSONObject vascularAccessJson = null;

                    //月上报数据
                    if(monthReportYear == year){
                        //血压
                        String bloodPressure = managerDO.getBloodPressure();
                        bloodPressureJson = JSONObject.parseObject(bloodPressure);
                        if(!bloodPressureJson.getBoolean("M" + 4) ){
                            BloodPressureForm form = buildBloodPressureForm(managerDO.getPatientId(), "" + 2025, 4);
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO bloodPressureForm = new CmrdsAutoReportDataFormDTO();
                                bloodPressureForm.setFormKey("XueYaCeLiang");
                                bloodPressureForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(bloodPressureForm);
                            }
                        }
                        if(!bloodPressureJson.getBoolean("M" + 5) ){
                            BloodPressureForm form = buildBloodPressureForm(managerDO.getPatientId(), "" + 2025, 5);
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO bloodPressureForm = new CmrdsAutoReportDataFormDTO();
                                bloodPressureForm.setFormKey("XueYaCeLiang");
                                bloodPressureForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(bloodPressureForm);
                            }
                        }
                        if(!bloodPressureJson.getBoolean("M" + 6) ){
                            BloodPressureForm form = buildBloodPressureForm(managerDO.getPatientId(), "" + 2025, 6);
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO bloodPressureForm = new CmrdsAutoReportDataFormDTO();
                                bloodPressureForm.setFormKey("XueYaCeLiang");
                                bloodPressureForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(bloodPressureForm);
                            }
                        }

                    }
                    //季度上报数据
                    if(quarterReportYear == year && (reportMonth == 3 || reportMonth == 6 || reportMonth == 9 || reportMonth == 12)){
                        //透析处方
                        /*String dialysisPrescription = managerDO.getDialysisPrescription();
                        dialysisPrescriptionJson = JSONObject.parseObject(dialysisPrescription);
                        if(!dialysisPrescriptionJson.getBoolean("Q" + reportQuarter) ){
                            DialysisPrescriptionForm form = buildDialysisPrescriptionForm(monthReportYear + ""
                                    , "Q" + reportQuarter, managerDO.getPatientId());
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO dialysisPrescriptionForm = new CmrdsAutoReportDataFormDTO();
                                dialysisPrescriptionForm.setFormKey("TouXiChuFang");
                                dialysisPrescriptionForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(dialysisPrescriptionForm);
                            }
                        }

                        //抗凝剂
                        String decoagulant = managerDO.getDecoagulant();
                        decoagulantJson = JSONObject.parseObject(decoagulant);
                        if(!decoagulantJson.getBoolean("Q" + reportQuarter) ){
                            AnticoagulantForm form = buildAnticoagulantForm( String.valueOf(monthReportYear), "Q" + reportQuarter, managerDO.getPatientId());
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO anticoagulantForm = new CmrdsAutoReportDataFormDTO();
                                anticoagulantForm.setFormKey("KangNingJi");
                                anticoagulantForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(anticoagulantForm);
                            }
                        }*/

                        //透析充分性
                      /* String dialysisAdequacy = managerDO.getDialysisAdequacy();
                        dialysisAdequacyJson = JSONObject.parseObject(dialysisAdequacy);
                        if(!dialysisAdequacyJson.getBoolean("Q" + reportQuarter)){
                            DialysisAdequacyForm form = buildDialysisAdequacyForm(managerDO.getPatientId(), String.valueOf(monthReportYear), reportQuarter);
                            if(form != null) {
                                CmrdsAutoReportDataFormDTO dialysisAdequacyForm = new CmrdsAutoReportDataFormDTO();
                                dialysisAdequacyForm.setFormKey("TouXiChongFenXing");
                                dialysisAdequacyForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(dialysisAdequacyForm);
                            }
                        }*/
                        //实验室检查
                       String laboratoryInspection = managerDO.getLaboratoryInspection();
                        laboratoryInspectionJson = JSONObject.parseObject(laboratoryInspection);
                        if(!laboratoryInspectionJson.getBoolean("Q" + reportQuarter) ){
                            LabExamForm form = buildLabExamForm(monthReportYear+"", "Q" + reportQuarter, managerDO.getPatientId());
                            if(form != null){
                                CmrdsAutoReportDataFormDTO labExamForm = new CmrdsAutoReportDataFormDTO();
                                labExamForm.setFormKey("ShiYanShiJianCha");
                                labExamForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(labExamForm);
                            }
                        }
                        //血管通路
                        /*String vascularAccess = managerDO.getVascularAccess();
                        vascularAccessJson = JSONObject.parseObject(vascularAccess);
                        if(!vascularAccessJson.getBoolean("HY" + reportHy)){
                            VascularAccessForm form = buildVascularAccessForm(monthReportYear+"", "HY" + reportHy, managerDO.getPatientId());
                            if(form != null){
                                CmrdsAutoReportDataFormDTO vascularAccessForm = new CmrdsAutoReportDataFormDTO();
                                vascularAccessForm.setFormKey("TongLu");
                                vascularAccessForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(vascularAccessForm);
                            }
                        }*/
                    }
                    //半年度上报数据
                    if(hyReportYear == year && (reportMonth == 6 || reportMonth == 12)){
                        //血管通路
/*                        String vascularAccess = managerDO.getVascularAccess();
                        vascularAccessJson = JSONObject.parseObject(vascularAccess);
                        if(!vascularAccessJson.getBoolean("HY" + reportHy)){
                            VascularAccessForm form = buildVascularAccessForm(monthReportYear+"", "HY" + reportHy, managerDO.getPatientId());
                            if(form != null){
                                CmrdsAutoReportDataFormDTO vascularAccessForm = new CmrdsAutoReportDataFormDTO();
                                vascularAccessForm.setFormKey("TongLu");
                                vascularAccessForm.setJson(JSONObject.toJSONString(form));
                                dataList.add(vascularAccessForm);
                            }
                        }*/
                    }

                    if(CollectionUtils.isNotEmpty(dataList)) {
                        CmrdsAutoReportDataDTO dataDTO = new CmrdsAutoReportDataDTO();
                        dataDTO.setPatIdNum(managerDO.getIdCard());
                        dataDTO.setPatName(managerDO.getPatientName());
                        dataDTO.setYear("" + monthReportYear);
                        dataDTO.setList(dataList);

                        //保存上报记录
                        CnrdsReportRecordDO cnrdsReportRecordDO = new CnrdsReportRecordDO();
                        cnrdsReportRecordDO.setPatientId(managerDO.getPatientId());
                        cnrdsReportRecordDO.setPatientName(managerDO.getPatientName());
                        cnrdsReportRecordDO.setDeptId(deptDO.getId());
                        cnrdsReportRecordDO.setReportMonth(String.valueOf(reportMonth));
                        cnrdsReportRecordDO.setReportYear(String.valueOf(monthReportYear));
                        cnrdsReportRecordDO.setReportData(JSONArray.toJSONString(dataDTO));
                        cnrdsReportRecordMapper.insert(cnrdsReportRecordDO);

                        CnrdsReportRecordDO updateCnrdsReportRecordDO = new CnrdsReportRecordDO();
                        updateCnrdsReportRecordDO.setId(cnrdsReportRecordDO.getId());
                        CarResponseResultDTO carResponseResultDTO = null;
                        try{
                            //上报数据
                            carResponseResultDTO = reportData(dataDTO, patientDO.getDeptId());
                            updateCnrdsReportRecordDO.setReturnResult(JSONObject.toJSONString(carResponseResultDTO));
                        }catch (Exception e){
                            log.error(e.getMessage(), e);
                            updateCnrdsReportRecordDO.setReturnResult(e.getMessage());
                        }
                        //更新上报记录
                        cnrdsReportRecordMapper.updateById(updateCnrdsReportRecordDO);

                        //更新上报数据管理
                        if(carResponseResultDTO != null
                                && "200".equals(carResponseResultDTO.getCode())
                                && CollectionUtils.isEmpty(carResponseResultDTO.getErrorMsgList())
                        ){
                            CnrdsReportManagerDO updateManagerDO = new CnrdsReportManagerDO();
                            updateManagerDO.setId(managerDO.getId());
                            updateManagerDO.setUpdateTime(new Date());
                            //血压
                            if(bloodPressureJson != null){
                                bloodPressureJson.put("M" + reportMonth, true);
                                updateManagerDO.setBloodPressure(bloodPressureJson.toJSONString());
                            }
                            //透析处方
                            if(dialysisPrescriptionJson != null){
                                dialysisPrescriptionJson.put("Q" + reportQuarter, true);
                                updateManagerDO.setDialysisPrescription(dialysisPrescriptionJson.toJSONString());
                            }
                            //抗凝剂
                            if(decoagulantJson != null){
                                decoagulantJson.put("Q" + reportQuarter, true);
                                updateManagerDO.setDecoagulant(decoagulantJson.toJSONString());
                            }
                           //透析充分性
                            if(dialysisAdequacyJson != null){
                                dialysisAdequacyJson.put("Q" + reportQuarter, true);
                                updateManagerDO.setDialysisAdequacy(dialysisAdequacyJson.toJSONString());
                            }
                            //实验室检查
                            if(laboratoryInspectionJson != null){
                                laboratoryInspectionJson.put("Q" + reportQuarter, true);
                                updateManagerDO.setLaboratoryInspection(laboratoryInspectionJson.toJSONString());
                            }
                            //血管通路
                            if(vascularAccessJson != null){
                                vascularAccessJson.put("HY" + reportHy, true);
                                updateManagerDO.setVascularAccess(vascularAccessJson.toJSONString());
                            }

                            //更新上报管理数据
                            if(CollectionUtils.isNotEmpty(carResponseResultDTO.getErrorMsgList())){
                                updateManagerDO.setSyncFlag("N");
                            }else{
                                updateManagerDO.setSyncFlag("Y");
                            }
                            cnrdsReportManagerMapper.updateById(updateManagerDO);

                        }

                        updateCount++;
                    }
                }
                pageNo++;
            }
        }
    }


    /**
     * 根据年份初始化所有患者的同步信息数据
     */
    public void initAllPatientSyncData(String year){
        List<DeptDO> deptDOS = deptMapper.selectList(DeptDO::getId,102L);
        for (DeptDO deptDO : deptDOS) {
            int pageNo = 1;
            PatientPageReqVO reqVO = new PatientPageReqVO();
            //reqVO.setPatientTypeSource("00");
            reqVO.setPageSize(1000);
            reqVO.setDeptId(deptDO.getId());
            while (true){
                reqVO.setPageNo(pageNo);
                PageResult<PatientDO> patientDOPageResult = patientMapper.selectPage(reqVO);
                if(patientDOPageResult == null || CollectionUtils.isEmpty(patientDOPageResult.getList())){
                    break;
                }
                List<PatientDO> list = patientDOPageResult.getList();
                for (PatientDO patientDO : list) {
                    CnrdsReportManagerDO cnrdsReportManagerDO = buildCnrdsReportManagerDO(patientDO.getId(), patientDO.getName(), patientDO.getIdCard(), year);
                    cnrdsReportManagerDO.setDeptId(deptDO.getId());
                    cnrdsReportManagerMapper.saveCnrdsReportManager(cnrdsReportManagerDO);
                }
                pageNo++;
            }
        }

    }

    /**
     * 根据年份初始化患者的同步信息数据
     */
    public void initPatientSyncData(Long patientId, String patientName, String idCard, String year){
        CnrdsReportManagerDO cnrdsReportManagerDO = buildCnrdsReportManagerDO(patientId, patientName, idCard, year);
        cnrdsReportManagerMapper.saveCnrdsReportManager(cnrdsReportManagerDO);
    }

    /**
     * 更新同步标识状态
     * @param year
     */
    public void updateReportManagerSyncFlag(String year, Long systemdeptid){
        UpdateWrapper<CnrdsReportManagerDO> updateWrapper = new UpdateWrapper();
        updateWrapper.lambda().set(CnrdsReportManagerDO::getSyncFlag, "N")
                .eq(CnrdsReportManagerDO::getYear, year)
                .eq(BaseDO::getDeptId, systemdeptid);
        cnrdsReportManagerMapper.update(null, updateWrapper);
    }

    public void delTokenCache(){
        List<DeptDO> deptDOS = deptMapper.selectList();
        for (DeptDO deptDO : deptDOS) {
            RedisUtils.deleteObject(CNRDS_CACHE_TOKEN_KEY + deptDO.getId());
        }
    }


    public void reportDeptPatientInfo(Long deptId){
        //系统患者
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = simpleDateFormat.parse("2025-04-10");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        QueryWrapper<PatientDO> patientDOQueryWrapper = new QueryWrapperX<>();
        patientDOQueryWrapper.lambda().eq(PatientDO::getDeptId, deptId)
        .eq(PatientDO::getPatientTypeSource,"00")
        .gt(PatientDO::getCreateTime,parse);
        List<PatientDO> patientDOS = patientMapper.selectList(patientDOQueryWrapper);
        for (PatientDO patientDO : patientDOS) {
            CarResponseResultDTO carResponseResultDTO = reportPatientInfo(patientDO.getId());
            log.info("carResponseResultDTO={}", carResponseResultDTO);
        }

    }

    /**
     * 上报患者的基本信息
     * @param patientId
     */
    @Async
    public CarResponseResultDTO reportPatientInfo(Long patientId){
        Calendar calendar = Calendar.getInstance();
        //当前年份
        int currentYear = calendar.get(Calendar.YEAR);
        currentYear = 2025;
        //基本信息表单
        BasicInfoForm basicInfoForm = buildBasicInfoForm(patientId);

        CmrdsAutoReportDataDTO dataDTO = new CmrdsAutoReportDataDTO();
        dataDTO.setPatIdNum(basicInfoForm.getInfoId());
        dataDTO.setYear("" + currentYear);
        List<CmrdsAutoReportDataFormDTO> dataList = new ArrayList<>();
        //基本信息
        CmrdsAutoReportDataFormDTO dataFormDTO = new CmrdsAutoReportDataFormDTO();
        dataFormDTO.setFormKey("JiBenXinXi");
        dataFormDTO.setJson(JSONObject.toJSONString(basicInfoForm));
        dataList.add(dataFormDTO);

        //诊断情况表单
        CmrdsAutoReportDataFormDTO diagnosisInfoForm = new CmrdsAutoReportDataFormDTO();
        diagnosisInfoForm.setFormKey("zhenDuanXinXi");
        diagnosisInfoForm.setJson(JSONObject.toJSONString(buildDiagnosisInfoForm(dataDTO.getYear())));
        dataList.add(diagnosisInfoForm);
        dataDTO.setList(dataList);

        PatientDO patientDO = patientMapper.selectById(patientId);

        //保存上报记录
        CnrdsReportRecordDO cnrdsReportRecordDO = new CnrdsReportRecordDO();
        cnrdsReportRecordDO.setPatientId(patientId);
        cnrdsReportRecordDO.setPatientName(basicInfoForm.getInfoName());
        cnrdsReportRecordDO.setReportData(JSONArray.toJSONString(dataDTO));
        cnrdsReportRecordDO.setDeptId(patientDO.getDeptId());
        cnrdsReportRecordMapper.insert(cnrdsReportRecordDO);

        //上报数据
        //CarResponseResultDTO carResponseResultDTO = reportData(dataDTO, patientDO.getDeptId());

        //更新上报记录
        CnrdsReportRecordDO updateCnrdsReportRecordDO = new CnrdsReportRecordDO();
        updateCnrdsReportRecordDO.setId(cnrdsReportRecordDO.getId());
        //updateCnrdsReportRecordDO.setReturnResult(JSONObject.toJSONString(carResponseResultDTO));
        cnrdsReportRecordMapper.updateById(updateCnrdsReportRecordDO);

        //初始化上报国网管理数据
        initPatientSyncData(patientId, basicInfoForm.getInfoName(),basicInfoForm.getInfoId(), dataDTO.getYear());

        return null;
    }

    /**
     * 上报转归信息到国网
     * @param patientId
     * @return
     */
    public CarResponseResultDTO reportOutcomeSituation(Long patientId){
        Calendar calendar = Calendar.getInstance();
        //当前年份
        int currentYear = calendar.get(Calendar.YEAR);
        //患者信息
        PatientDO patientDO = patientMapper.selectById(patientId);

        CmrdsAutoReportDataDTO dataDTO = new CmrdsAutoReportDataDTO();
        dataDTO.setPatIdNum(patientDO.getIdCard());
        dataDTO.setYear("" + currentYear);
        List<CmrdsAutoReportDataFormDTO> dataList = new ArrayList<>();

        OutcomeSituationForm form = buildOutcomeSituationForm(patientId);
        CmrdsAutoReportDataFormDTO outcomeSituationForm = new CmrdsAutoReportDataFormDTO();
        outcomeSituationForm.setFormKey("ZhuanGuiQingKuang");
        outcomeSituationForm.setJson(JSONObject.toJSONString(form));
        dataList.add(outcomeSituationForm);
        dataDTO.setList(dataList);

        //保存上报记录
        CnrdsReportRecordDO cnrdsReportRecordDO = new CnrdsReportRecordDO();
        cnrdsReportRecordDO.setPatientId(patientId);
        cnrdsReportRecordDO.setPatientName(patientDO.getName());
        cnrdsReportRecordDO.setReportData(JSONArray.toJSONString(dataDTO));
        cnrdsReportRecordMapper.insert(cnrdsReportRecordDO);

        //上报数据
        CarResponseResultDTO carResponseResultDTO = reportData(dataDTO, patientDO.getDeptId());

        //更新上报记录
        CnrdsReportRecordDO updateCnrdsReportRecordDO = new CnrdsReportRecordDO();
        updateCnrdsReportRecordDO.setId(cnrdsReportRecordDO.getId());
        updateCnrdsReportRecordDO.setReturnResult(JSONObject.toJSONString(carResponseResultDTO));
        cnrdsReportRecordMapper.updateById(updateCnrdsReportRecordDO);

        return carResponseResultDTO;
    }


    private CnrdsReportManagerDO buildCnrdsReportManagerDO(Long patientId, String patientName,String idCard, String year){
        CnrdsReportManagerDO managerDO = new CnrdsReportManagerDO();
        managerDO.setPatientId(patientId);
        managerDO.setPatientName(patientName);
        managerDO.setIdCard(idCard);
        managerDO.setYear(year);
        managerDO.setBloodPressure("{'M1':false,'M2':false,'M3':false,'M4':false,'M5':false,'M6':false,'M7':false,'M8':false,'M9':false,'M10':false,'M11':false,'M12':false}");
        managerDO.setVascularAccess("{'HY1':false,'HY2':false}");
        managerDO.setDialysisPrescription("{'Q1':false,'Q2':false,'Q3':false,'Q4':false}");
        managerDO.setDecoagulant("{'Q1':false,'Q2':false,'Q3':false,'Q4':false}");
        managerDO.setDialysisAdequacy("{'Q1':false,'Q2':false,'Q3':false,'Q4':false}");
        managerDO.setLaboratoryInspection("{'Q1':false,'Q2':false,'Q3':false,'Q4':false}");
        managerDO.setSyncFlag("N");
        return managerDO;
    }


    /**
     * 测试表单
     * @param patientId
     * @return
     */
    public CarResponseResultDTO testCheckFormData(Long patientId, Long deptId){
        //基本信息表单
        BasicInfoForm basicInfoForm = buildBasicInfoForm(patientId);

        CmrdsAutoReportDataDTO dataDTO = new CmrdsAutoReportDataDTO();
        dataDTO.setPatIdNum(basicInfoForm.getInfoId());
        dataDTO.setYear("2024");

        List<CmrdsAutoReportDataFormDTO> dataList = new ArrayList<>();
        //基本信息
        CmrdsAutoReportDataFormDTO dataFormDTO = new CmrdsAutoReportDataFormDTO();
        dataFormDTO.setFormKey("JiBenXinXi");
        dataFormDTO.setJson(JSONObject.toJSONString(basicInfoForm));
        dataList.add(dataFormDTO);

        //转归情况表单
        CmrdsAutoReportDataFormDTO outcomeSituationForm = new CmrdsAutoReportDataFormDTO();
        outcomeSituationForm.setFormKey("ZhuanGuiQingKuang");
        outcomeSituationForm.setJson(JSONObject.toJSONString(buildOutcomeSituationForm()));
        dataList.add(outcomeSituationForm);

        //诊断情况表单
        CmrdsAutoReportDataFormDTO diagnosisInfoForm = new CmrdsAutoReportDataFormDTO();
        diagnosisInfoForm.setFormKey("zhenDuanXinXi");
        diagnosisInfoForm.setJson(JSONObject.toJSONString(buildDiagnosisInfoForm()));
        dataList.add(diagnosisInfoForm);

        //血管通路表单
        CmrdsAutoReportDataFormDTO vascularAccessForm = new CmrdsAutoReportDataFormDTO();
        vascularAccessForm.setFormKey("TongLu");
        vascularAccessForm.setJson(JSONObject.toJSONString(buildVascularAccessForm()));
        dataList.add(vascularAccessForm);

        //透析处方表单
        CmrdsAutoReportDataFormDTO dialysisPrescriptionForm = new CmrdsAutoReportDataFormDTO();
        dialysisPrescriptionForm.setFormKey("TouXiChuFang");
        dialysisPrescriptionForm.setJson(JSONObject.toJSONString(buildDialysisPrescriptionForm()));
        dataList.add(dialysisPrescriptionForm);

        //抗凝剂表单
        CmrdsAutoReportDataFormDTO anticoagulantForm = new CmrdsAutoReportDataFormDTO();
        anticoagulantForm.setFormKey("KangNingJi");
        anticoagulantForm.setJson(JSONObject.toJSONString(buildAnticoagulantForm()));
        dataList.add(anticoagulantForm);

        //血压表单
        CmrdsAutoReportDataFormDTO bloodPressureForm = new CmrdsAutoReportDataFormDTO();
        bloodPressureForm.setFormKey("XueYaCeLiang");
        bloodPressureForm.setJson(JSONObject.toJSONString(buildBloodPressureForm()));
        dataList.add(bloodPressureForm);

        //透析充分性表单
        CmrdsAutoReportDataFormDTO dialysisAdequacyForm = new CmrdsAutoReportDataFormDTO();
        dialysisAdequacyForm.setFormKey("TouXiChongFenXing");
        dialysisAdequacyForm.setJson(JSONObject.toJSONString(buildDialysisAdequacyForm()));
        dataList.add(dialysisAdequacyForm);

        //ESA表单
        CmrdsAutoReportDataFormDTO esaForm = new CmrdsAutoReportDataFormDTO();
        esaForm.setFormKey("CuHongSu");
        esaForm.setJson(JSONObject.toJSONString(buildEsaForm()));
        dataList.add(esaForm);

        //HIF-PHI表单
        CmrdsAutoReportDataFormDTO hifPhiForm = new CmrdsAutoReportDataFormDTO();
        hifPhiForm.setFormKey("HIF-PHI");
        hifPhiForm.setJson(JSONObject.toJSONString(buildHifPhiForm()));
        dataList.add(hifPhiForm);

        //铁剂表单
        CmrdsAutoReportDataFormDTO ironAgentForm = new CmrdsAutoReportDataFormDTO();
        ironAgentForm.setFormKey("TieJi");
        ironAgentForm.setJson(JSONObject.toJSONString(buildIronAgentForm()));
        dataList.add(ironAgentForm);

        //抗高血压药表单
        CmrdsAutoReportDataFormDTO antihtAgentForm = new CmrdsAutoReportDataFormDTO();
        antihtAgentForm.setFormKey("KangGaoXueYa");
        antihtAgentForm.setJson(JSONObject.toJSONString(buildAntihtAgentForm()));
        dataList.add(antihtAgentForm);

        //MBD干预药表单
        CmrdsAutoReportDataFormDTO mbdForm = new CmrdsAutoReportDataFormDTO();
        mbdForm.setFormKey("kangGuKuangWuZhiDaiXie");
        mbdForm.setJson(JSONObject.toJSONString(buildMbdForm()));
        dataList.add(mbdForm);

        //实验室检查表单
        CmrdsAutoReportDataFormDTO labExamForm = new CmrdsAutoReportDataFormDTO();
        labExamForm.setFormKey("ShiYanShiJianCha");
        labExamForm.setJson(JSONObject.toJSONString(buildLabExamForm()));
        dataList.add(labExamForm);

        //辅助检查表单
        CmrdsAutoReportDataFormDTO assistExamForm = new CmrdsAutoReportDataFormDTO();
        assistExamForm.setFormKey("FuZhuJianCha");
        assistExamForm.setJson(JSONObject.toJSONString(buildAssistExamForm()));
        dataList.add(assistExamForm);

        dataDTO.setList(dataList);

        //保存记录
        CnrdsReportRecordDO cnrdsReportRecordDO = new CnrdsReportRecordDO();
        cnrdsReportRecordDO.setPatientId(patientId);
        cnrdsReportRecordDO.setPatientName(basicInfoForm.getInfoName());
        cnrdsReportRecordDO.setReportData(JSONArray.toJSONString(dataDTO));
        cnrdsReportRecordMapper.insert(cnrdsReportRecordDO);

        CarResponseResultDTO carResponseResultDTO = checkFormData(dataDTO, deptId);

        //更新记录
        CnrdsReportRecordDO updateCnrdsReportRecordDO = new CnrdsReportRecordDO();
        updateCnrdsReportRecordDO.setId(cnrdsReportRecordDO.getId());
        updateCnrdsReportRecordDO.setReturnResult(JSONObject.toJSONString(carResponseResultDTO));
        cnrdsReportRecordMapper.updateById(updateCnrdsReportRecordDO);

        return carResponseResultDTO;
    }

    /**
     * 基本信息表单  JiBenXinXi
     * @param patientId
     * @return
     */
    private BasicInfoForm buildBasicInfoForm(Long patientId){
        //查询患者
        PatientDO patientDO = patientMapper.selectById(patientId);

        BasicInfoForm basicInfoForm = new BasicInfoForm();
        basicInfoForm.setInfoName(patientDO.getName());
        basicInfoForm.setInfoId(patientDO.getIdCard());
        basicInfoForm.setFirstRrtDate(DateUtils.parseDateStr(patientDO.getFirstReceiveTime(), DateUtils.FORMAT_YEAR_MONTH_DAY));
        basicInfoForm.setFirstRrtStart("住院期间透析室诱导透析");
        basicInfoForm.setHeight(patientDO.getStature());
        basicInfoForm.setSex("1".equals(patientDO.getSex()) ? "男" : "女");
        basicInfoForm.setBirthday(patientDO.getBirthday());
        basicInfoForm.setCareer(null); //跟系统不匹配，不设置
        basicInfoForm.setPhone(patientDO.getMobile());
        basicInfoForm.setResidByStr(patientDO.getAddress());

        //通路类型
        BloodRoadCreateReqVO bloodRoadCreateReqVO = new BloodRoadCreateReqVO();
        bloodRoadCreateReqVO.setPatientId(patientId);
        List<BloodRoadRespVO> bloodRoadList = bloodRoadService.getBloodRoadList(bloodRoadCreateReqVO);
        if (CollectionUtils.isNotEmpty(bloodRoadList)) {
            BloodRoadRespVO bloodRoadRespVO = bloodRoadList.get(0);
            String partType = bloodRoadRespVO.getPartType();
            basicInfoForm.setFirstErtAcc(convertCnrdsVascularAccessType(partType));
        }else{
            basicInfoForm.setFirstErtAcc("其它");
        }

        //教育程度
        if(StringUtils.isNotEmpty(patientDO.getCulture())){
            MottoSimpleDO mottoSimpleDO = mottoSimpleMapper.selectById(patientDO.getCulture());
            if(mottoSimpleDO != null) {
                if("小学毕业".equals(mottoSimpleDO.getPname())){
                    basicInfoForm.setEdu("小学及以下");
                }else if("本科".equals(mottoSimpleDO.getPname())){
                    basicInfoForm.setEdu("本科");
                }else if("博士毕业".equals(mottoSimpleDO.getPname())){
                    basicInfoForm.setEdu("博士及以上");
                }
            }
        }
        //民族
        if(StringUtils.isNotEmpty(patientDO.getNation())) {
            DictDataDO dictDataDO = dictDataService.getDictData("nation", patientDO.getNation());
            if(dictDataDO != null) {
                basicInfoForm.setRace(dictDataDO.getLabel());
            }
        }
        //婚姻状况
        if(StringUtils.isNotEmpty(patientDO.getMarriage())) {
            DictDataDO dictDataDO = dictDataService.getDictData("marriage", patientDO.getMarriage());
            if(dictDataDO != null) {
                basicInfoForm.setMarriage(dictDataDO.getLabel());
            }
        }
        //费别
        if(StringUtils.isNotEmpty(patientDO.getApplyWay())) {
            DictDataDO dictDataDO = dictDataService.getDictData("applyWay", patientDO.getApplyWay());
            if(dictDataDO != null) {
                if("自费".equals(dictDataDO.getLabel())){
                    basicInfoForm.setFee(ListUtils.newArrayList("自费医疗"));
                }else if("社保卡".equals(dictDataDO.getLabel()) || "居民医保".equals(dictDataDO.getLabel())){
                    basicInfoForm.setFee(ListUtils.newArrayList("基本医保"));
                }else{
                    basicInfoForm.setFee(ListUtils.newArrayList("其它"));
                }
            }
        }

        return basicInfoForm;
    }


    /**
     * 转归情况表单  ZhuanGuiQingKuang
     * @return
     */
    private OutcomeSituationForm buildOutcomeSituationForm(Long patientId){
        QueryWrapper<OutcomeRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OutcomeRecordDO::getPatientId, patientId)
                .orderByDesc(OutcomeRecordDO::getId).last("limit 1");
        OutcomeRecordDO outcomeRecordDO = outcomeRecordMapper.selectOne(queryWrapper);
        OutcomeSituationForm form = new OutcomeSituationForm();
        if(outcomeRecordDO != null){
            if("17".equals(outcomeRecordDO.getType())){
                String reason = outcomeRecordDO.getReason();
                MottoSimpleDO mottoSimpleDO = mottoSimpleMapper.selectById(reason);
                if(mottoSimpleDO != null && mottoSimpleDO.getPname().indexOf("死亡") != -1){
                    form.setOutcomeStatusLatest("死亡");
                    List<Outcome> outcomeList = new ArrayList<>();
                    Outcome outcome = new Outcome();
                    outcome.setOutcomeStatus("死亡");
                    outcome.setOutcomeDate(DateUtils.getTimeToDay(outcomeRecordDO.getCreateTime()));
                    outcome.setDeathCause("病因不详");
                    outcomeList.add(outcome);
                    form.setOutcomeList(outcomeList);
                }
            }else{
                form.setOutcomeStatusLatest("在透");
            }
        }else{
            form.setOutcomeStatusLatest("在透");
        }

        return form;
    }

    /**
     * 诊断情况表单 zhenDuanXinXi
     * @return
     */
    private DiagnosisInfoForm buildDiagnosisInfoForm(String year){
        DiagnosisInfoForm form = new DiagnosisInfoForm();
        DiagnosisPricauTime diagnosisPricauTime = new DiagnosisPricauTime();
        diagnosisPricauTime.setPricauYear(year);
        form.setPricauTime(diagnosisPricauTime);
        form.setPrimaryCause("原发病不明确");
        form.setPathoYesNo("否");
        return form;
    }

    /**
     * 血管通路表单 TongLu
     * @return
     */
    public VascularAccessForm buildVascularAccessForm(String year, String reportHy, Long patientId){
        // 获取指定年的季度 开始时间和结束时间
        List<Date> localDateListByHy = getLocalDateListByHy(year, reportHy);
        Date startDate = localDateListByHy.get(0);
        Date endDate = localDateListByHy.get(1);

        VascularAccessForm form = new VascularAccessForm();
        form.setYear(year);
        form.setDimension(reportHy);

        BloodRoadCreateReqVO bloodRoadCreateReqVO = new BloodRoadCreateReqVO();
        bloodRoadCreateReqVO.setPatientId(patientId);
        List<BloodRoadRespVO> bloodRoadList = bloodRoadService.getBloodRoadList(bloodRoadCreateReqVO);
        // 筛选出在 reportHy 范围的数据，并按照 建立时间 降序排序
        List<BloodRoadRespVO> filterBloodRoadList = bloodRoadList.stream()
                .filter(bloodRoad ->  bloodRoad.getStartTime().after(startDate) && bloodRoad.getStartTime().before(endDate))
                .sorted((respVO1, respVO2) -> respVO2.getStartTime().compareTo(respVO1.getStartTime()))
                .collect(Collectors.toList());
        BloodRoadRespVO bloodRoadRespVO = null;
        if (CollectionUtils.isNotEmpty(filterBloodRoadList)) {
            bloodRoadRespVO = filterBloodRoadList.get(0);
            if (org.springframework.util.StringUtils.isEmpty(bloodRoadRespVO.getInitTime()) && filterBloodRoadList.size() > 1) {
                bloodRoadRespVO = filterBloodRoadList.get(1);
            }
            form.setChangeYn("有");
        }else if(CollectionUtils.isNotEmpty(bloodRoadList)){
            bloodRoadRespVO = bloodRoadList.get(0);
            form.setChangeYn("无");
        }
        if(bloodRoadRespVO != null) {
            form.setInitialDate(DateUtils.parseDateStr(bloodRoadRespVO.getInitTime(), DateUtils.FORMAT_YEAR_MONTH_DAY));
            String partType = bloodRoadRespVO.getPartType();
            form.setType(convertCnrdsVascularAccessType(partType));
        }

        String lastFormJson = getLastFormJson("TongLu", patientId);
        if(StringUtils.isNotEmpty(lastFormJson)) {
            VascularAccessForm lastForm = JSONObject.parseObject(lastFormJson, VascularAccessForm.class);
            if(lastForm.equals(form)){
                form.setChangeYn("无");
            }else{
                form.setChangeYn("有");
            }
        }else{
            form.setChangeYn("有");
        }

        return form;
    }

    private String convertCnrdsVascularAccessType(String partType){
        if (StringUtils.isNotBlank(partType)) {
            if (partType.contains("无涤纶套") && partType.contains("导管")) {
                return "临时中心静脉置管";
            } else if (partType.contains("带隧道带涤纶套") && partType.contains("导管")) {
                return "长期中心静脉置管";
            } else if (partType.contains("自体动静脉内瘘")) {
                return "自体内瘘";
            } else if (partType.contains("移植血管动静脉内瘘")) {
                return "移植血管";
            } else {
                return "其它";
            }
        }
        return null;
    }

    /**
     * 透析处方表单 TouXiChuFang
     * @return
     */
    public DialysisPrescriptionForm buildDialysisPrescriptionForm(String year, String quarter, Long patientId){
        // 获取指定年的季度 开始时间和结束时间
        List<Date> localDateListByDimension = getLocalDateListByQuarter(year, quarter);
        Date quarterStartDate = localDateListByDimension.get(0);
        Date quarterEndDate = localDateListByDimension.get(1);

        // 分装参数，查询该患者的透析方案
        DialyzeOptionCreateReqVO createReqVO = new DialyzeOptionCreateReqVO();
        createReqVO.setPatientId(patientId);
        List<DialyzeOptionRespVO> dialyzeOptionList = dialyzeOptionService.getDialyzeOptionList(createReqVO);
        if(CollectionUtils.isEmpty(dialyzeOptionList)){
            return null;
        }

        // 透析处方 表单对象
        DialysisPrescriptionForm form = new DialysisPrescriptionForm();
        form.setDiaYear(year);
        form.setDimension(quarter);
        //form.setChangeYn(YnEnum.YES.getValue());
        // 通量 系统目前只有 高通量
        form.setDialyzerFlux("高通量");
        // 类型 系默认 国产
        form.setDialyzerType(ListUtils.newArrayList("国产"));
        // 填充透析治疗默认值
        form.setHdfYn(YnEnum.NO.getValue());
        form.setHpYn(YnEnum.NO.getValue());

        // 定义HD、HDF 透析次数（key：系统展示值，value：国网上报的值）
        Map<String, String> HdHdfMap = Stream.of(
                new AbstractMap.SimpleEntry<>("每周1次", "1次/周"),
                new AbstractMap.SimpleEntry<>("每周3次", "3 次/周"),
                new AbstractMap.SimpleEntry<>("二周1次", "1次/2周"),
                new AbstractMap.SimpleEntry<>("四周1次", "1次/4周")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        //获取该透析方案更新时间（即执行方案的开始时间）且 该方案处于开启状态
        List<DialyzeOptionRespVO> filterRecordList = dialyzeOptionList.stream().filter(
                v -> "0".equals(v.getStateDictValue())).collect(Collectors.toList());
        //不存在可用透析方案
        if(CollectionUtils.isEmpty(filterRecordList)){
            return null;
        }

        // 遍历透析方案，判断是否在当前季度内
        for (DialyzeOptionRespVO dialyzeOptionRespVO : filterRecordList) {
            // 透析模式为 HD
            if ("1".equals(dialyzeOptionRespVO.getDialyzeDictValue())) {
                // 设置 透析治疗频次（已HD透析模式为准）
                Map<String, String> freqMap = Stream.of(
                        new AbstractMap.SimpleEntry<>("每天1次", "1 次/天"),
                        new AbstractMap.SimpleEntry<>("每周1次", "1 次/周"),
                        new AbstractMap.SimpleEntry<>("每周2次", "2 次/周"),
                        new AbstractMap.SimpleEntry<>("每周3次", "3 次/周"),
                        new AbstractMap.SimpleEntry<>("每周4次", "4 次/周"),
                        new AbstractMap.SimpleEntry<>("每周5次", "5-6 次/周"),
                        new AbstractMap.SimpleEntry<>("每周6次", "5-6 次/周"),
                        new AbstractMap.SimpleEntry<>("二周3次", "3 次/2周"),
                        new AbstractMap.SimpleEntry<>("二周5次", "5 次/2周")
                ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                form.setFreq(
                        freqMap.getOrDefault(dialyzeOptionRespVO.getFrequencyName(), "其它")
                );

                // 设置 血液透析每次治疗时间（已HD透析模式为准）
                DialysisProtocolCreateReqVO dialysisProtocolCreateReqVO = new DialysisProtocolCreateReqVO();
                dialysisProtocolCreateReqVO.setPatientDialyzeId(dialyzeOptionRespVO.getId());
                dialysisProtocolCreateReqVO.setDialyzeId(Long.parseLong(dialyzeOptionRespVO.getDialyzeDictValue()));
                dialysisProtocolCreateReqVO.setProtocolType(1);
                // 获取 透析方案 详情
                DialysisProtocolRespVO dialysisProtocolRespVO = dialysisProtocolService.getDialysisProtocol(dialysisProtocolCreateReqVO);
                // 获取 透析时长
                double hour = Optional
                        .ofNullable(dialysisProtocolRespVO.getDuration())
                        .filter(s -> !StringUtils.isBlank(s))
                        .map(Double::parseDouble)
                        .orElse(0.0);
                double min = Optional
                        .ofNullable(dialysisProtocolRespVO.getDurationMin())
                        .filter(s -> !StringUtils.isBlank(s))
                        .map(Double::parseDouble)
                        .orElse(0.0);
                // 由于 血液透析每次治疗时间 的范围为 2-8 小时，且步长为0.5，所以需要做处理
                if (min != 0 && min % 30 != 0) {
                    min = 30.0;
                }
                // 校验 透析时长 是否在区间范围
                double dialysisDuration = hour + min / 60;
                if (dialysisDuration < 2) {
                    dialysisDuration = 2;
                } else if (dialysisDuration > 8) {
                    dialysisDuration = 8;
                }
                form.setDuration(String.valueOf(dialysisDuration).replaceAll("\\.0", ""));
            }

            // 透析模式为 HDF
            if ("2".equals(dialyzeOptionRespVO.getDialyzeDictValue())) {
                // 设置 HDF治疗为 有
                form.setHdfYn(YnEnum.YES.getValue());
                form.setDiaHdfFreq(
                        HdHdfMap.getOrDefault(dialyzeOptionRespVO.getFrequencyName(), "其它")
                );
            }

            // 透析模式为 HP
            if ("3".equals(dialyzeOptionRespVO.getDialyzeDictValue()) || "4".equals(dialyzeOptionRespVO.getDialyzeDictValue())) {
                // 设置 HP治疗为 有
                form.setHpYn(YnEnum.YES.getValue());
                form.setHpFreq(
                        HdHdfMap.getOrDefault(dialyzeOptionRespVO.getFrequencyName(), "其它")
                );
            }

        }

        String lastFormJson = getLastFormJson("TouXiChuFang", patientId);
        if(StringUtils.isNotEmpty(lastFormJson)) {
            DialysisPrescriptionForm lastForm = JSONObject.parseObject(lastFormJson, DialysisPrescriptionForm.class);
            if(lastForm.equals(form)){
                form.setChangeYn("无");
            }else{
                form.setChangeYn("有");
            }
        }else{
            form.setChangeYn("有");
        }

        return form;
    }

    /**
     * 抗凝剂表单 KangNingJi
     * @return
     */
    public AnticoagulantForm buildAnticoagulantForm(String year, String quarter, Long patientId){
        // 获取指定年的季度 开始时间和结束时间
        List<Date> localDateListByDimension = getLocalDateListByQuarter(year, quarter);
        Date quarterStartDate = localDateListByDimension.get(0);
        Date quarterEndDate = localDateListByDimension.get(1);

        AnticoagulantForm form = new AnticoagulantForm();
        form.setYear(year);
        form.setDimension(quarter);
        //form.setChangeYn("有");
        // 设置抗凝剂（默认值）
        form.setAgent("无抗凝剂");

        // 分装参数，查询该患者的透析方案（从透析处方获取抗凝剂）
        DialyzeOptionCreateReqVO createReqVO = new DialyzeOptionCreateReqVO();
        createReqVO.setPatientId(patientId);
        List<DialyzeOptionRespVO> dialyzeOptionList = dialyzeOptionService.getDialyzeOptionList(createReqVO);

        // 过滤只剩 HD 透析模式的信息
        dialyzeOptionList = dialyzeOptionList.stream()
                .filter(dialyzeOption -> dialyzeOption.getStateDictValue().equals("0")) // 过滤未启用的方案
                //.filter(dialyzeOption -> dialyzeOption.getUpdateTime().after(quarterStartDate)) // 过滤时间在当前季度内的信息
                .filter(dialyzeOption -> dialyzeOption.getDialyzeDictValue().equals("1")) // 过滤非 HD 透析模式的信息
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dialyzeOptionList)) {
            DialyzeOptionRespVO dialyzeOptionRespVO = dialyzeOptionList.get(0);

            // 获取 透析方案 详情信息
            DialysisProtocolCreateReqVO dialysisProtocolCreateReqVO = new DialysisProtocolCreateReqVO();
            dialysisProtocolCreateReqVO.setPatientDialyzeId(dialyzeOptionRespVO.getId());
            dialysisProtocolCreateReqVO.setDialyzeId(Long.parseLong(dialyzeOptionRespVO.getDialyzeDictValue()));
            dialysisProtocolCreateReqVO.setProtocolType(1);
            DialysisProtocolRespVO dialysisProtocol = dialysisProtocolService.getDialysisProtocol(dialysisProtocolCreateReqVO);

            // 详情信息 不为空
            if (dialysisProtocol != null && CollectionUtils.isNotEmpty(dialysisProtocol.getContradictDOS())) {
                ContradictRespVO contradictRespVO = dialysisProtocol.getContradictDOS().get(0);
                String drugNameStr = contradictRespVO.getDrugNameStr();
                String firstDoseValue = contradictRespVO.getFirstDoseValue();

                // 定义 国网上报的有效值
                Map<String, String> cnrdsDrugMap = Stream.of(
                        new AbstractMap.SimpleEntry<>("无抗凝剂", "无抗凝剂"),
                        new AbstractMap.SimpleEntry<>("普通肝素", "普通肝素"),
                        new AbstractMap.SimpleEntry<>("低分子肝素", "低分子肝素"),
                        new AbstractMap.SimpleEntry<>("枸橼酸钠", "枸橼酸钠"),
                        new AbstractMap.SimpleEntry<>("阿加曲班", "阿加曲班"),
                        new AbstractMap.SimpleEntry<>("其它", "其它")
                ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


                if (StringUtils.isNotBlank(drugNameStr)) {
                    // 设置抗凝剂
                    form.setAgent(
                            cnrdsDrugMap.getOrDefault(drugNameStr, "其它")
                    );
                    if(form.getAgent().equals("普通肝素")){
                        AnticoHeparin heparin = new AnticoHeparin();
                        heparin.setHeparinInitialUnit("IU");
                        heparin.setHeparinInitial(firstDoseValue);
                        form.setHeparin(heparin);
                    }else if(form.getAgent().equals("低分子肝素")){
                        AnticoLmwh lmwh = new AnticoLmwh();
                        lmwh.setLmwhUnit("IU");
                        lmwh.setLmwhDose(firstDoseValue);
                        form.setLmwh(lmwh);
                    }else if(form.getAgent().equals("枸橼酸钠")){
                        AnticoCitrate citrate = new AnticoCitrate();

                    }else if(form.getAgent().equals("阿加曲班")){
                        AnticoArgatro argatro = new AnticoArgatro();
                        argatro.setArgatroInitialUnit("IU");
                        argatro.setArgatroInitial(firstDoseValue);
                        form.setArgatro(argatro);
                    }

                }
            }
        }

        String lastFormJson = getLastFormJson("KangNingJi", patientId);
        if(StringUtils.isNotEmpty(lastFormJson)) {
            AnticoagulantForm lastForm = JSONObject.parseObject(lastFormJson, AnticoagulantForm.class);
            if(lastForm.equals(form)){
                form.setChangeYn("无");
            }else{
                form.setChangeYn("有");
            }
        }else{
            form.setChangeYn("有");
        }
        return form;
    }

    /**
     * 血压表单 XueYaCeLiang
     * @return
     */
    private BloodPressureForm buildBloodPressureForm(Long patientId, String year, Integer month){
        List<Date> dateIntervals = getDateIntervalByYearMonth(Integer.valueOf(year), month);
        HemodialysisManagerCreateReqVO reqVO = new HemodialysisManagerCreateReqVO();
        reqVO.setPatientId(patientId);
        reqVO.setHemodialysisTimeStart(dateIntervals.get(0));
        reqVO.setHemodialysisTimeEnd(dateIntervals.get(1));
        List<HemodialysisManagerDO> dialysisRecordDOS = hemodialysisManagerMapper.selectList(reqVO);
        if(CollectionUtils.isNotEmpty(dialysisRecordDOS)){
            HemodialysisManagerDO hemodialysisManagerDO = dialysisRecordDOS.stream().filter(hemodialysisManagerDO1->
                !StringUtils.isEmpty(hemodialysisManagerDO1.getBpNoOne()) && !StringUtils.isEmpty(hemodialysisManagerDO1.getBpNoTwo())
                        &&!StringUtils.isEmpty(hemodialysisManagerDO1.getAfterBpOne()) && !StringUtils.isEmpty(hemodialysisManagerDO1.getAfterBpTwo())
            ).findFirst().orElse(null);

            BloodPressureForm form = new BloodPressureForm();
            form.setYear(year);
            form.setDimension("M" + month);
            form.setPosition("上肢");
            form.setPresbp(hemodialysisManagerDO.getBpNoOne());
            form.setPredbp(hemodialysisManagerDO.getBpNoTwo());
            form.setPossbp(hemodialysisManagerDO.getAfterBpOne());
            form.setPosdbp(hemodialysisManagerDO.getAfterBpTwo());
            return form;
        }else{
            return null;
        }
    }


    /**
     * 透析充分性表单 TouXiChongFenXing
     * @return
     */
    private DialysisAdequacyForm buildDialysisAdequacyForm(Long patientId, String year, Integer quarter){
        // 获取指定年的季度 开始时间和结束时间
        List<Date> localDateListByDimension = getLocalDateListByQuarter(year, "Q" + quarter);
        Date quarterStartDate = localDateListByDimension.get(0);
        Date quarterEndDate = localDateListByDimension.get(1);

        // 0-肾科检验 738- KT/V检查
        RenalProjectRespVO renalProjectForBloodRoutine = renalProjectService.getRenalProjectByIds(patientId, 738L, "0");
        if (renalProjectForBloodRoutine != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForBloodRoutine.getRenalProjectInfoRespVOList();
            if(CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 筛选出在 quarter 范围的数据
                renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                        .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                        .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());
                DialysisAdequacyForm form = new DialysisAdequacyForm();
                form.setYear(year);
                form.setDimension("Q" + quarter);
                form.setWeightAssessment("是");
                form.setPreurea(projectInfo.getString("ktvCheck1"));
                form.setPosurea(projectInfo.getString("ktvCheck2"));
                System.out.println(projectInfo.getString("ktvCheck3"));
                if (projectInfo.getString("ktvCheck3").contains(".5")) {
                    form.setDuration(projectInfo.getString("ktvCheck3"));
                }else {
                    String ktvCheck3 = projectInfo.getString("ktvCheck3");
                    String s = ktvCheck3.split("\\.")[0];
                    form.setDuration(s);
                }

                //form.setDuration(projectInfo.getString("ktvCheck3"));
                form.setUf(projectInfo.getString("ktvCheck4"));
                String ktvCheck6 = projectInfo.getString("ktvCheck6");
                if(StringUtils.isNotEmpty(ktvCheck6)){
                    String replace = ktvCheck6.replace("%", "");
                    form.setUrr(StringUtils.isNotEmpty(replace) ? Double.valueOf(replace) : 0);
                }
                form.setNonsysFlag("是");
                form.setKtv(projectInfo.getDouble("ktvCheck7"));

                //查询干体重
                QueryWrapper<HemodialysisManagerDO> drQueryWrapper = new QueryWrapper<>();
                drQueryWrapper.lambda().eq(HemodialysisManagerDO::getPatientId, patientId)
                        .isNotNull(HemodialysisManagerDO::getDialyzeBeforeWeight)
                        .orderByDesc(BaseDO::getCreateTime).last("limit 2");
                List<HemodialysisManagerDO> hemodialysisManagerDOList = hemodialysisManagerMapper.selectList(drQueryWrapper);
                if(CollectionUtils.isNotEmpty(hemodialysisManagerDOList)){
                    form.setWeightValue(hemodialysisManagerDOList.get(0).getDryWeight());
                    if(hemodialysisManagerDOList.size() == 2){
                        try{
                            Double aDouble = Double.valueOf(hemodialysisManagerDOList.get(0).getDryWeight());
                            Double bDouble = Double.valueOf(hemodialysisManagerDOList.get(1).getDryWeight());
                            if(aDouble - bDouble < 0.05){
                                form.setWeightAssessment("是");
                            }else{
                                form.setWeightAssessment("否");
                            }
                        }catch (Exception e){
                            log.info("buildDialysisAdequacyForm干体重转换异常，patientId={}", patientId);
                            form.setWeightAssessment("否");
                        }
                    }else{
                        form.setWeightAssessment("是");
                    }

                }else{
                    log.info("buildDialysisAdequacyForm数据不完整，干体重为空，patientId={}", patientId);
                    return null;
                }

                return form;
            }
        }

        return null;
    }

    /**
     * ESA表单 CuHongSu
     * @return
     */
    private EsaForm buildEsaForm(){
        EsaForm form = new EsaForm();
        form.setEsaYear("2024");
        form.setEsaDimension("Q4");
        form.setEsaChangeYN("有");
        form.setEsaTreatment("使用");
        form.setEsaType("达依泊汀");
        return form;
    }

    /**
     * HIF-PHI表单 HIF-PHI
     * @return
     */
    private HifPhiForm buildHifPhiForm(){
        HifPhiForm form = new HifPhiForm();
        form.setHifYear("2024");
        form.setHifDimension("Q4");
        form.setHifChangeYN("有");
        form.setHifTreatment("使用");
        return form;
    }

    /**
     * 铁剂表单 TieJi
     * @return
     */
    private IronAgentForm buildIronAgentForm(){
        IronAgentForm form = new IronAgentForm();
        form.setFeYear("2024");
        form.setFeDimension("Q4");
        form.setFeChangeYN("有");
        form.setFeTreatment("使用");
        form.setFeAdmin(ListUtils.newArrayList("口服"));
        return form;
    }

    /**
     * 抗高血压药表单 KangGaoXueYa
     * @return
     */
    private AntihtAgentForm buildAntihtAgentForm(){
        AntihtAgentForm form = new AntihtAgentForm();
        form.setAntihtAgentYear("2024");
        form.setAntihtAgentDimension("Q4");
        form.setAntihtAgentChangeYN("有");
        form.setAntihtAgentTreatment("使用");
        return form;
    }


    /**
     * MBD干预药表单 kangGuKuangWuZhiDaiXie
     * @return
     */
    private MbdForm buildMbdForm(){
        MbdForm form = new MbdForm();
        form.setMbdYear("2024");
        form.setMbdDimension("Q4");
        form.setMbdChangeYN("有");
        form.setMbdTreatment("使用");
        form.setMbdVitdTreatment("使用");
        form.setMbdCaBasedPBinderTr("使用");
        form.setMbdNonCaBasedPBinderTr("未使用");
        return form;
    }

    /**
     * 实验室检查表单  ShiYanShiJianCha
     * @return
     */
    public LabExamForm buildLabExamForm(String year, String quarter, Long patientId){
        final String NO_CHECK = "未查";

        // 获取指定年的季度 开始时间和结束时间
        List<Date> localDateListByDimension = getLocalDateListByQuarter(year, quarter);
        Date quarterStartDate = localDateListByDimension.get(0);
        Date quarterEndDate = localDateListByDimension.get(1);

        // 实验室检查 表单对象
        LabExamForm form = new LabExamForm();
        form.setLabYear(year);
        form.setLabDimension(quarter);
        // 填充默认值
        form.setLabBloodRtYN(NO_CHECK);
        form.setLabRodCapYn(NO_CHECK);
        form.setLabIronTestTsYn(NO_CHECK);
        form.setLabIronTestSfYn(NO_CHECK);
        form.setLabRodPthYn(NO_CHECK);
        form.setLabBiochemicalYn(NO_CHECK);
        form.setLabNutrInflamCrpYn(NO_CHECK);
        form.setLabNutrInflamPabYn(NO_CHECK);
        form.setLabNutrInflamBeta2mgYn(NO_CHECK);
        form.setLabInfectHbsagYn(NO_CHECK);
        form.setLabInfectAntihcvYn(NO_CHECK);
        form.setLabInfectHivYn(NO_CHECK);
        form.setLabInfectSyphilisYn(NO_CHECK);

        // 0-肾科检验 728-血常规
        RenalProjectRespVO renalProjectForBloodRoutine = renalProjectService.getRenalProjectByIds(patientId, 728L, "0");
        if (renalProjectForBloodRoutine != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForBloodRoutine.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 血红蛋白 的值
                String bloodRoutine1 = projectInfo.getString("BloodRoutine2");
                // 设置 血红蛋白（10-200）
                if (!StringUtils.isBlank(bloodRoutine1)) {
                    bloodRoutine1 = bloodRoutine1.trim();
                    try {
                        if (bloodRoutine1.contains("<")) {
                            bloodRoutine1 = bloodRoutine1.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(bloodRoutine1);
                        if (bloodRoutineCount >= 10 && bloodRoutineCount <= 200) {
                            form.setLabBloodRtYN(null);
                            form.setLabBloodRtHb(bloodRoutine1);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 红细胞计数 的值
                String BloodRoutine5 = projectInfo.getString("BloodRoutine5");
                // 设置 红细胞计数(0-10)
                if (!StringUtils.isBlank(BloodRoutine5)) {
                    BloodRoutine5 = BloodRoutine5.trim();
                    try {
                        if (BloodRoutine5.contains("<")) {
                            BloodRoutine5 = BloodRoutine5.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(BloodRoutine5);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 10) {
                            form.setLabBloodRtYN(null);
                            form.setLabBloodRtRbc(BloodRoutine5);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 红细胞比容 的值
                String BloodRoutine13 = projectInfo.getString("BloodRoutine3");
                // 设置 红细胞比容(0-100)
                if (!StringUtils.isBlank(BloodRoutine13)) {
                    BloodRoutine13 = BloodRoutine13.trim();
                    try {
                        if (BloodRoutine13.contains("<")) {
                            BloodRoutine13 = BloodRoutine13.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(BloodRoutine13);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 100) {
                            form.setLabBloodRtYN(null);
                            bloodRoutineCount = BigDecimal.valueOf(bloodRoutineCount).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            form.setLabBloodRtHct(String.valueOf(bloodRoutineCount));
                        }
                    } catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 血小板 的值
                String BloodRoutine4 = projectInfo.getString("BloodRoutine4");
                // 设置 血小板(0-1000)
                if (!StringUtils.isBlank(BloodRoutine4)) {
                    BloodRoutine4 = BloodRoutine4.trim();
                    try {
                        if (BloodRoutine4.contains("<")) {
                            BloodRoutine4 = BloodRoutine4.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(BloodRoutine4);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 1000) {
                            form.setLabBloodRtYN(null);
                            bloodRoutineCount = BigDecimal.valueOf(bloodRoutineCount).setScale(2, RoundingMode.HALF_UP).doubleValue();
                            form.setLabBloodRtPlt(String.valueOf(bloodRoutineCount));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }

        // 0-肾科检验 729-铁五项
        RenalProjectRespVO renalProjectForIron = renalProjectService.getRenalProjectByIds(patientId, 729L, "0");
        if (renalProjectForIron != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForIron.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 铁蛋白 的值
                String anemia2 = projectInfo.getString("IronPentathlon2");
                // 设置 铁蛋白(0-5000)
                if (!StringUtils.isBlank(anemia2)) {
                    anemia2 = anemia2.trim();
                    try {
                        if (anemia2.contains("<")) {
                            anemia2 = anemia2.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(anemia2);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 5000) {
                            form.setLabIronTestSfYn(null);
                            form.setLabIronTestSf(anemia2);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


                // 获取 转铁饱和度 的值
                String IronPentathlon4 = projectInfo.getString("IronPentathlon4");
                // 设置 转铁饱和度(0-100)
                if (!StringUtils.isBlank(IronPentathlon4)) {
                    IronPentathlon4 = IronPentathlon4.trim();
                    try{
                        double bloodRoutineCount = Double.parseDouble(IronPentathlon4);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 100) {
                            form.setLabIronTestTsYn(null);
                            form.setLabIronTestTs(IronPentathlon4);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }


            }
        }

        // 0-肾科检验 747-贫血三项
        /*RenalProjectRespVO renalProjectForAnemia = renalProjectService.getRenalProjectByIds(patientId, 747L, "0");
        if (renalProjectForAnemia != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForAnemia.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 铁蛋白 的值
                String anemia2 = projectInfo.getString("anemia2");
                // 设置 铁蛋白(0-5000)
                if (!StringUtils.isBlank(anemia2)) {
                    try {
                        double bloodRoutineCount = Double.parseDouble(anemia2);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 5000) {
                            form.setLabIronTestSfYn(null);
                            form.setLabIronTestSf(anemia2);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


            }
        }*/

        // 0-肾科检验 733-空腹血糖
        RenalProjectRespVO renalProjectForFbs = renalProjectService.getRenalProjectByIds(patientId, 733L, "0");
        if (renalProjectForFbs != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForFbs.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 血糖(0-80) 的值
                String fbs1 = projectInfo.getString("fbs1");
                // 设置 血糖(0-80)
                if (!StringUtils.isBlank(fbs1)) {
                    fbs1 = fbs1.trim();
                    try {
                        if (fbs1.contains("<")) {
                            fbs1 = fbs1.replace("<", "");
                        }
                        double bloodRoutineCount = Double.parseDouble(fbs1);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 80) {
                            form.setLabBiochemicalGlu(fbs1);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


            }
        }

        // 0-肾科检验 736-甲状旁腺激素
        RenalProjectRespVO renalProjectForThyroid = renalProjectService.getRenalProjectByIds(patientId, 736L, "0");
        if (renalProjectForThyroid != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForThyroid.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 甲状旁腺激素(PTH) 的值
                String thyroidHormone1 = projectInfo.getString("thyroidHormone1");
                if(StringUtils.isEmpty(thyroidHormone1)){
                    thyroidHormone1 = projectInfo.getString("thyroidHormone3");
                }
                // 设置 甲状旁腺激素(PTH)(0-5000)
                if (!StringUtils.isBlank(thyroidHormone1)) {
                    thyroidHormone1 = thyroidHormone1.trim();
                    try {
                        if (thyroidHormone1.contains("<")) {
                            thyroidHormone1 = thyroidHormone1.replace("<", "");
                        }
                        double bloodRoutineCount =  Double.parseDouble(thyroidHormone1);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 5000) {
                            form.setLabRodPthYn(null);
                            form.setLabRodPth(thyroidHormone1);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


            }
        }

        // 0-肾科检验 737-血脂全套
        RenalProjectRespVO renalProjectForBloodFat = renalProjectService.getRenalProjectByIds(patientId, 737L, "0");
        if (renalProjectForBloodFat != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForBloodFat.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 甘油三酯 的值
                String bloodFat1 = projectInfo.getString("bloodFat1");
                // 设置 甘油三酯(0-50)
                if (!StringUtils.isBlank(bloodFat1)) {
                    bloodFat1 = bloodFat1.trim();
                    try {
                        if (bloodFat1.contains("<")) {
                            bloodFat1 = bloodFat1.split("<")[1];
                        }
                        double bloodFatCount = Double.parseDouble(bloodFat1);
                        if (bloodFatCount >= 0 && bloodFatCount <= 50) {
                            //form.setLabBiochemicalYn(null);
                            form.setLabBiochemicalTg(bloodFat1);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 总胆固醇 的值
                String bloodFat2 = projectInfo.getString("bloodFat2");
                // 设置 总胆固醇(0-50)
                if (!StringUtils.isBlank(bloodFat2)) {
                    bloodFat2 = bloodFat2.trim();
                    try {
                        if (bloodFat2.contains("<")) {
                            bloodFat2 = bloodFat2.split("<")[1];
                        }
                        double bloodFatCount = Double.parseDouble(bloodFat2);
                        if (bloodFatCount >= 0 && bloodFatCount <= 50) {
                            //form.setLabBiochemicalYn(null);
                            form.setLabBiochemicalTc(bloodFat2);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }

                // 获取 低密度脂蛋白 的值
                String bloodFat4 = projectInfo.getString("bloodFat4");
                // 设置 低密度脂蛋白(0-50)
                if (!StringUtils.isBlank(bloodFat4)) {
                    bloodFat4 = bloodFat4.trim();
                    try {
                        if (bloodFat4.contains("<")) {
                            bloodFat4 = bloodFat4.split("<")[1];
                        }
                        double bloodFatCount = Double.parseDouble(bloodFat4);
                        if (bloodFatCount >= 0 && bloodFatCount <= 50) {
                            //form.setLabBiochemicalYn(null);
                            form.setLabBiochemicalLdl(bloodFat4);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 高密度脂蛋白 的值
                String bloodFat3 = projectInfo.getString("bloodFat3");
                // 设置 高密度脂蛋白(0-50)
                if (!StringUtils.isBlank(bloodFat3)) {
                    bloodFat3 = bloodFat3.trim();
                    try {
                        if (bloodFat3.contains("<")) {
                            bloodFat3 = bloodFat3.split("<")[1];
                        }
                        double bloodFatCount = Double.parseDouble(bloodFat3);
                        if (bloodFatCount >= 0 && bloodFatCount <= 50) {
                            //form.setLabBiochemicalYn(null);
                            form.setLabBiochemicalHdl(bloodFat3);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }

            }
        }

        // 0-肾科检验 735-电解质检查 electrolyte1 血钾
        RenalProjectRespVO renalProjectForDjz = renalProjectService.getRenalProjectByIds(patientId, 735L, "0");
        if (renalProjectForBloodFat != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForDjz.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 血钾 的值
                String bloodFat1 = projectInfo.getString("electrolyte1");
                // 设置 血钾(0-20)
                if (!StringUtils.isBlank(bloodFat1)) {
                    bloodFat1 = bloodFat1.trim();
                    try {
                        if (bloodFat1.contains("<")) {
                            bloodFat1 = bloodFat1.split("<")[1];
                        }
                        double bloodFatCount = Double.parseDouble(bloodFat1);
                        if (bloodFatCount >= 0 && bloodFatCount <= 20) {
                            form.setLabBiochemicalPotassium(String.valueOf(bloodFatCount));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }


                // 获取 血钙(0-10) 的值
                String electrolyte5 = projectInfo.getString("electrolyte5");
                // 设置 血钙(0-10)
                if (!StringUtils.isBlank(electrolyte5)) {
                    electrolyte5 = electrolyte5.trim();
                    try {
                        if (electrolyte5.contains("<")) {
                            electrolyte5 = electrolyte5.split("<")[1];
                        }
                        double electrolyte5Count = Double.parseDouble(electrolyte5);
                        if (electrolyte5Count >= 0 && electrolyte5Count <= 10) {
                            form.setLabRodCa(String.valueOf(electrolyte5Count));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 血钠(50-200) 的值
                String electrolyte10 = projectInfo.getString("electrolyte10");
                // 设置 血钠(50-200)
                if (!StringUtils.isBlank(electrolyte10)) {
                    electrolyte10 = electrolyte10.trim();
                    try {
                        if (electrolyte10.contains("<")) {
                            electrolyte10 = electrolyte10.split("<")[1];
                        }
                        double electrolyte10Count = Double.parseDouble(electrolyte10);
                        if (electrolyte10Count >= 50 && electrolyte10Count <= 200) {
                            form.setLabBiochemicalSodium(String.valueOf(electrolyte10Count));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 血磷(0-10) 的值
                String electrolyte8 = projectInfo.getString("electrolyte8");
                // 设置 血磷(0-10)
                if (!StringUtils.isBlank(electrolyte8)) {
                    electrolyte8 = electrolyte8.trim();
                    try {
                        if (electrolyte8.contains("<")) {
                            electrolyte8 = electrolyte8.split("<")[1];
                        }
                        double electrolyte8Count = Double.parseDouble(electrolyte8);
                        if (electrolyte8Count >= 0 && electrolyte8Count <= 20) {
                            form.setLabRodP(String.valueOf(electrolyte8Count));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                //血钙血磷_是否检查
                if(StringUtils.isNotEmpty(form.getLabRodCa()) && StringUtils.isNotEmpty(form.getLabRodP())){
                    form.setLabRodCapYn(null);
                }else if (StringUtils.isEmpty(form.getLabRodCa()) || StringUtils.isEmpty(form.getLabRodP())) {
                    form.setLabRodCa(null);
                    form.setLabRodP(null);
                }

            }
        }

        // 0-肾科检验 731-炎症指标
        RenalProjectRespVO renalProjectForInflammation = renalProjectService.getRenalProjectByIds(patientId, 731L, "0");
        if (renalProjectForInflammation != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForInflammation.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 C反应蛋白 的值
                String inflammation2 = projectInfo.getString("inflammation2");
                // 设置 C反应蛋白(0-5000)
                if (!StringUtils.isBlank(inflammation2)) {
                    inflammation2 = inflammation2.trim();
                    try {
                        if (inflammation2.contains("<")) {
                            inflammation2 = inflammation2.split("<")[1];
                        }
                        double bloodRoutineCount = Double.parseDouble(inflammation2);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 5000) {
                            form.setLabNutrInflamCrpYn(null);
                            form.setLabNutrInflamCrp(String.format("%.2f",bloodRoutineCount*10));
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


            }
        }

        // 0-肾科检验 732-肝功能全套
        RenalProjectRespVO renalProjectForLiver = renalProjectService.getRenalProjectByIds(patientId, 732L, "0");
        if (renalProjectForLiver != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForLiver.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 前白蛋白 的值
                String liverFunc3 = projectInfo.getString("liverFunc3");
                // 设置 前白蛋白(0-5000)
                if (!StringUtils.isBlank(liverFunc3)) {
                    liverFunc3 = liverFunc3.trim();
                    try {
                        if (liverFunc3.contains("<")) {
                            liverFunc3 = liverFunc3.split("<")[1];
                        }
                        double bloodRoutineCount = Double.parseDouble(liverFunc3);
                        if (bloodRoutineCount >= 0 && bloodRoutineCount <= 5000) {
                            form.setLabNutrInflamPabYn(null);
                            form.setLabNutrInflamPab(liverFunc3);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }

                }


                // 获取 血白蛋白 的值
                String liverFunc10 = projectInfo.getString("liverFunc10");
                // 设置 血白蛋白(10-70)
                if (!StringUtils.isBlank(liverFunc10)) {
                    liverFunc10 = liverFunc10.trim();
                    if (liverFunc10.contains("<")) {
                        liverFunc10 = liverFunc10.split("<")[1];
                    }
                    form.setLabBiochemicalStb(liverFunc10);
                }

                // 获取 血白蛋白 的值
                String liverFunc2 = projectInfo.getString("liverFunc2");
                // 设置 血白蛋白(10-70)
                if (!StringUtils.isBlank(liverFunc2)) {
                    liverFunc2 = liverFunc2.trim();
                    try {
                        if (liverFunc2.contains("<")) {
                            liverFunc2 = liverFunc2.split("<")[1];
                        }
                        double bloodRoutineCount = Double.parseDouble(liverFunc2);
                        if (bloodRoutineCount >= 10 && bloodRoutineCount <= 70) {
                            form.setLabBiochemicalYn(null);
                            form.setLabBiochemicalAlb(liverFunc2);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }

                // 获取 丙氨酸氨基转移酶(ALT) 的值
                String liverFunc6 = projectInfo.getString("liverFunc6");
                // 设置 丙氨酸氨基转移酶(ALT)
                if (!StringUtils.isBlank(liverFunc6)) {
                    liverFunc6 = liverFunc6.trim();
                    if (liverFunc6.contains("<")) {
                        liverFunc6 = liverFunc6.split("<")[1];
                    }
                    form.setLabBiochemicalAst(liverFunc6);
                }

                // 获取 天冬氨酸氨基转移酶(AST) 的值
                String liverFunc7 = projectInfo.getString("liverFunc7");
                // 设置 天冬氨酸氨基转移酶(AST)
                if (!StringUtils.isBlank(liverFunc7)) {
                    liverFunc7 = liverFunc7.trim();
                    if (liverFunc7.contains("<")) {
                        liverFunc7 = liverFunc7.split("<")[1];
                    }
                    form.setLabBiochemicalAlt(liverFunc7);
                }
            }
        }

        // 0-肾科检验 734-肾功能
        RenalProjectRespVO renalProjectForRenal = renalProjectService.getRenalProjectByIds(patientId, 734L, "0");
        if (renalProjectForRenal != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForRenal.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 β2微球蛋白 的值
                String renalFunction8 = projectInfo.getString("renalFunction8");
                // 设置 β2微球蛋白
                if (!StringUtils.isBlank(renalFunction8)) {
                    renalFunction8 = renalFunction8.trim();
                    if (renalFunction8.contains("<")) {
                        renalFunction8 = renalFunction8.split("<")[1];
                    }
                    form.setLabNutrInflamBeta2mgYn(null);
                    form.setLabNutrInflamBeta2mg(renalFunction8);
                }

                //生化检查  肌酐
                // 获取 肌酐 的值
                String renalFunction1 = projectInfo.getString("renalFunction1");
                // 设置 肌酐
                if (!StringUtils.isBlank(renalFunction1)) {
                    renalFunction1 = renalFunction1.trim();
                    if (renalFunction1.contains("<")) {
                        renalFunction1 = renalFunction1.split("<")[1];
                    }
                    form.setLabBiochemicalScr(renalFunction1);
                    form.setLabBiochemicalScrUnit("μmol/L");
                }

                //生化检查  尿素
                // 获取 尿素 的值
                String renalFunction3 = projectInfo.getString("renalFunction3");
                // 设置 尿素
                if (!StringUtils.isBlank(renalFunction3)) {
                    renalFunction3 = renalFunction3.trim();
                    if (renalFunction3.contains("<")) {
                        renalFunction3 = renalFunction3.split("<")[1];
                    }
                    form.setLabBiochemicalUrea(renalFunction3);
                    form.setLabBiochemicalUreaUnit("mmol/L");
                }

                //生化检查  尿酸
                // 获取 尿酸 的值
                String renalFunction5 = projectInfo.getString("renalFunction5");
                // 设置 尿酸
                if (!StringUtils.isBlank(renalFunction5)) {
                    renalFunction5 = renalFunction5.trim();
                    if (renalFunction5.contains("<")) {
                        renalFunction5 = renalFunction5.split("<")[1];
                    }
                    form.setLabBiochemicalUa(renalFunction5);
                }

            }

        }

        //生化检查  血钾&血白蛋白 必填
        if(StringUtils.isNotEmpty(form.getLabBiochemicalAlb()) && StringUtils.isNotEmpty(form.getLabBiochemicalPotassium())){
            form.setLabBiochemicalYn(null);
        }else{
            form.setLabBiochemicalYn(NO_CHECK);
            form.setLabBiochemicalUrea("");
            form.setLabBiochemicalUreaUnit("");
            form.setLabBiochemicalScr("");
            form.setLabBiochemicalScrUnit("");
            form.setLabBiochemicalUa("");
            form.setLabBiochemicalAlb("");
            form.setLabBiochemicalAst("");
            form.setLabBiochemicalAlt("");
            form.setLabBiochemicalStb("");
            form.setLabBiochemicalTg("");
            form.setLabBiochemicalTc("");
            form.setLabBiochemicalLdl("");
            form.setLabBiochemicalHdl("");
            form.setLabBiochemicalGlu("");
            form.setLabBiochemicalPotassium("");
            form.setLabBiochemicalSodium("");
        }


        // 0-肾科检验 742-传染病四项
        RenalProjectRespVO renalProjectForInfectious = renalProjectService.getRenalProjectByIds(patientId, 742L, "0");
        if (renalProjectForInfectious != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectForInfectious.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 HBsAg 的值
                String infectious1 = projectInfo.getString("infectious1");
                // 设置 HBsAg
                if (!StringUtils.isBlank(infectious1) && !"未查".equals(infectious1)) {
                    form.setLabInfectHbsagYn(null);

                    form.setLabInfectHbsag(yingxingOrYangxing(infectious1));
                }

                // 获取 AntiHCV 的值
                String infectious2 = projectInfo.getString("infectious2");
                // 设置 AntiHCV
                if (!StringUtils.isBlank(infectious2) && !"未查".equals(infectious2)) {
                    form.setLabInfectAntihcvYn(null);
                    form.setLabInfectAntihcv(yingxingOrYangxing(infectious2));
                }

                // 获取 HIV抗体 的值
                String infectious3 = projectInfo.getString("infectious3");
                // 设置 HIV抗体
                if (!StringUtils.isBlank(infectious3) && !"未查".equals(infectious3)) {
                    form.setLabInfectHivYn(null);
                    form.setLabInfectHiv(yingxingOrYangxing(infectious3));
                }

                // 获取 梅毒 的值
                String infectious4 = projectInfo.getString("infectious4");
                // 设置 梅毒
                if (!StringUtils.isBlank(infectious4) && !"未查".equals(infectious4)) {
                    form.setLabInfectSyphilisYn(null);
                    form.setLabInfectSyphilis(yingxingOrYangxing(infectious4));
                }


            }
        }

        // 0-肾科检验 743-肝炎系列全套
        /*RenalProjectRespVO renalProjectHepatitis = renalProjectService.getRenalProjectByIds(patientId, 743L, "0");
        if (renalProjectHepatitis != null) {
            List<RenalProjectInfoRespVO> renalProjectInfoRespVOList = renalProjectHepatitis.getRenalProjectInfoRespVOList();
            // 筛选出在 quarter 范围的数据，并按照 检查时间 降序排序
            renalProjectInfoRespVOList = renalProjectInfoRespVOList.stream()
                    .filter(respVO -> respVO.getCheckTime().after(quarterStartDate) && respVO.getCheckTime().before(quarterEndDate))
                    .sorted((respVO1, respVO2) -> respVO2.getCheckTime().compareTo(respVO1.getCheckTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(renalProjectInfoRespVOList)) {
                // 获取第一个元素（即为最新的一条数据）
                RenalProjectInfoRespVO renalProjectInfoRespVO = renalProjectInfoRespVOList.get(0);
                JSONObject projectInfo = JSONObject.parseObject(renalProjectInfoRespVO.getProjectInfo());

                // 获取 HBsAg 的值
                String infectious1 = projectInfo.getString("hepatitis1");
                // 设置 HBsAg
                if (!StringUtils.isBlank(infectious1) && !"未查".equals(infectious1)) {
                    form.setLabInfectHbsagYn(null);
                    form.setLabInfectHbsag(yingxingOrYangxing(infectious1));
                }

                // 获取 AntiHCV 的值
                String infectious2 = projectInfo.getString("hepatitis12");
                // 设置 AntiHCV
                if (!StringUtils.isBlank(infectious2) && !"未查".equals(infectious2)) {
                    form.setLabInfectAntihcvYn(null);
                    form.setLabInfectAntihcv(yingxingOrYangxing(infectious2));
                }
            }
        }*/

        return form;
    }


    private String yingxingOrYangxing(String str){
        if(StringUtils.isNotEmpty(str)){
            if(str.indexOf("阴性") != -1){
                return "阴性";
            }else if(str.indexOf("阳性") != -1){
                return "阳性";
            }
        }
        return "";
    }

    /**
     * 辅助检查表单  FuZhuJianCha   测试表单
     * @return
     */
    private AssistExamForm buildAssistExamForm(){
        AssistExamForm form = new AssistExamForm();
        form.setExamYear("2024");
        form.setExamDimension("HY2");
        form.setExamXrayYn("是");
        return form;
    }

    /**
     * 转归情况表单  ZhuanGuiQingKuang  测试表单
     * @return
     */
    private OutcomeSituationForm buildOutcomeSituationForm(){
        OutcomeSituationForm form = new OutcomeSituationForm();
        form.setOutcomeStatusLatest("在透");
        return form;
    }

    /**
     * 诊断情况表单 zhenDuanXinXi  测试表单
     * @return
     */
    private DiagnosisInfoForm buildDiagnosisInfoForm(){
        DiagnosisInfoForm form = new DiagnosisInfoForm();
        DiagnosisPricauTime diagnosisPricauTime = new DiagnosisPricauTime();
        diagnosisPricauTime.setPricauYear("2024");
        form.setPricauTime(diagnosisPricauTime);
        form.setPrimaryCause("原发病不明确");
        form.setPathoYesNo("否");
        return form;
    }

    /**
     * 血管通路表单 TongLu  测试表单
     * @return
     */
    private VascularAccessForm buildVascularAccessForm(){
        VascularAccessForm form = new VascularAccessForm();
        form.setYear("2024");
        form.setDimension("HY2");
        form.setChangeYn("无");
        form.setType("临时中心静脉置管");
        form.setInitialDate("2024-08-20");
        return form;
    }

    /**
     * 透析处方表单 TouXiChuFang  测试表单
     * @return
     */
    private DialysisPrescriptionForm buildDialysisPrescriptionForm(){
        DialysisPrescriptionForm form = new DialysisPrescriptionForm();
        form.setDiaYear("2024");
        form.setDimension("Q4");
        form.setChangeYn("有");
        form.setFreq("2 次/周");
        form.setDuration("4");
        form.setHdfYn("无");
        //form.setDiaHdfFreq("其它"); 无时，不允许有值
        form.setHpYn("有");
        form.setHpFreq("1次/周");
        form.setDialyzerType(ListUtils.newArrayList("国产"));
        form.setDialyzerFlux("低通量");
        return form;
    }

    /**
     * 抗凝剂表单 KangNingJi  测试表单
     * @return
     */
    private AnticoagulantForm buildAnticoagulantForm(){
        AnticoagulantForm form = new AnticoagulantForm();
        form.setYear("2024");
        form.setDimension("Q4");
        form.setChangeYn("有");
        form.setAgent("低分子肝素");
        form.setFirstYn(false);
        AnticoLmwh anticoLmwh = new AnticoLmwh();
        anticoLmwh.setLmwhDose("低分子肝素钠");
        anticoLmwh.setLmwhDose("10");
        anticoLmwh.setLmwhUnit("mg");
        form.setLmwh(anticoLmwh);
//        form.setHeparin(new AnticoHeparin());
//        form.setCitrate(new AnticoCitrate());
//        form.setArgatro(new AnticoArgatro());

        return form;
    }

    /**
     * 血压表单 XueYaCeLiang  测试表单
     * @return
     */
    private BloodPressureForm buildBloodPressureForm(){
        BloodPressureForm form = new BloodPressureForm();
        form.setYear("2024");
        form.setDimension("M12");
        form.setPresbp("200");
        form.setPredbp("150");
        return form;
    }

    /**
     * 透析充分性表单 TouXiChongFenXing  测试表单
     * @return
     */
    private DialysisAdequacyForm buildDialysisAdequacyForm(){
        DialysisAdequacyForm form = new DialysisAdequacyForm();
        form.setYear("2024");
        form.setDimension("Q4");
        form.setWeightValue("55");
        form.setWeightAssessment("是");
        form.setPreurea("100");
        form.setPosurea("100");
        form.setDuration("4");
        form.setUf("10");
        //form.setUrr(60);
        form.setKtv(4d);
        form.setNonsysFlag("是");
        return form;
    }


    /**
     * 实验室检查表单  ShiYanShiJianCha  测试表单
     * @return
     */
    private LabExamForm buildLabExamForm(){
        LabExamForm form = new LabExamForm();
        form.setLabYear("2024");
        form.setLabDimension("Q4");
        form.setLabBloodRtHb("150");
        form.setLabIronTestTs("80");
        form.setLabIronTestSf("4000");
        form.setLabRodCa("8");
        form.setLabRodP("8");
        form.setLabRodPth("4000");
        form.setLabNutrInflamCrp("4000");
        form.setLabNutrInflamPab("4100");
        form.setLabNutrInflamBeta2mg("100");
        form.setLabInfectHbsag("阳性");
        form.setLabInfectAntihcv("阳性");
        form.setLabInfectHiv("阳性");
        form.setLabInfectSyphilis("阳性");
        form.setLabBiochemicalPotassium("20");

        return form;
    }


    /**
     * 根据指定年份获取季度日期范围（Q1：第一季度， Q2：第二季度， Q3：第三季度， Q4：第四季度）
     * year为空时，则默认为今年
     * @param year
     * @param quarter
     * @return
     */
    private List<Date> getLocalDateListByQuarter(String year, String quarter) {
        int currentYear = LocalDate.now().getYear();
        if (year != null) {
            currentYear = Integer.parseInt(year);
        }
        switch (Objects.requireNonNull(quarter)) {
            case "Q1":
                return ListUtils.newArrayList(
                        convertToDate(LocalDate.of(currentYear, 1, 1)),
                        convertToDate(LocalDate.of(currentYear, 4, 1).minusDays(1))
                );
            case "Q2":
                return ListUtils.newArrayList(

                        convertToDate(LocalDate.of(currentYear, 4, 1)),
                        convertToDate(LocalDate.of(currentYear, 7, 1).minusDays(1))
                );
            case "Q3":
                return ListUtils.newArrayList(
                        convertToDate(LocalDate.of(currentYear, 7, 1)),
                        convertToDate(LocalDate.of(currentYear, 10, 1).minusDays(1))
                );
            case "Q4":
                return ListUtils.newArrayList(
                        convertToDate(LocalDate.of(currentYear, 10, 1)),
                        convertToDate(LocalDate.of(currentYear + 1, 1, 1).minusDays(1))
                );
            default:
                throw new RuntimeException(quarter + "季度不存在");
        }
    }

    /**
     * 获取月份的 1号到月底的时间区间 如 2024-01-01 00:00:00 2024-01-31 23:23:59
     * @param year
     * @param month
     * @return
     */
    public List<Date> getDateIntervalByYearMonth(Integer year, Integer month){
        return ListUtils.newArrayList(
                convertToDate(LocalDate.of(year, month, 1)),
                convertToDate(LocalDateTime.of(LocalDate.of(year, month, 1).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX)));
    }

    /**
     * 根据指定年份获取半年日期范围（HY1：上半年， HY2：下半年）
     * year为空时，则默认为今年
     * @param year
     * @param hy
     * @return
     */
    private List<Date> getLocalDateListByHy(String year, String hy) {
        int currentYear = LocalDate.now().getYear();
        if (year != null) {
            currentYear = Integer.parseInt(year);
        }
        switch (Objects.requireNonNull(hy)) {
            case "HY1":
                return ListUtils.newArrayList(
                        convertToDate(LocalDate.of(currentYear, 1, 1)),
                        convertToDate(LocalDate.of(currentYear, 7, 1).minusDays(1))
                );
            case "HY2":
                return ListUtils.newArrayList(
                        convertToDate(LocalDate.of(currentYear, 7, 1)),
                        convertToDate(LocalDate.of(currentYear + 1, 1, 1).minusDays(1))
                );
            default:
                throw new RuntimeException(hy + "半年不存在");
        }
    }

    /**
     * LocalDate转换为Date
     **/
    private Date convertToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    private Date convertToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 获取表单上报的最新一条数据，判断是否有变化
     * @param formName
     * @param patientId
     * @return
     */
    private String getLastFormJson(String formName, Long patientId){
        QueryWrapper<CnrdsReportRecordDO> queryWrapper = new QueryWrapper<>();
        if("TongLu".equals(formName)){
            queryWrapper.lambda().in(CnrdsReportRecordDO::getReportMonth,ListUtils.newArrayList("3", "6", "9", "12"));
        }else{
            queryWrapper.lambda().in(CnrdsReportRecordDO::getReportMonth,ListUtils.newArrayList( "6",  "12"));
        }
        queryWrapper.lambda().eq(CnrdsReportRecordDO::getPatientId, patientId).last("order by id desc limit 1");
        CnrdsReportRecordDO cnrdsReportRecordDO = cnrdsReportRecordMapper.selectOne(queryWrapper);
        if(cnrdsReportRecordDO != null){
            String reportData = cnrdsReportRecordDO.getReportData();
            List<CmrdsAutoReportDataFormDTO> dataFormDTOS = JSONArray.parseArray(reportData, CmrdsAutoReportDataFormDTO.class);
            if(CollectionUtils.isNotEmpty(dataFormDTOS)){
                Optional<CmrdsAutoReportDataFormDTO> first = dataFormDTOS.stream().filter(v -> formName.equals(v.getFormKey())).findFirst();
                if(first.isPresent()){
                    return first.get().getJson();
                }
            }
        }
        return null;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CnrdsTokenInfo{

        private String reportId;

        private String accessToken;

        private String reportSecret;

        private String encryptionKey;

        private ProxyIpManager proxyIpManager;
    }


   /* public static void main(String[] args) {
        CnrdsTokenInfo tokenInfo = new CnrdsTokenInfo();
        tokenInfo.setAccessToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7f2AUSoxFdvDoqVEaEPP0k3BO6zhASMDkcYcqsmUy7M");
        tokenInfo.setReportId("HDNNVN7JG34UOPLCW4");
        tokenInfo.setReportSecret("6F9N5P75DAPD2YU9SPZR79B0S2AROL35");
        tokenInfo.setEncryptionKey("MNEMXM4KKY2ORHYW");
        Map<String, String> headers = new HashMap<>();
        headers.put("reportId", tokenInfo.getReportId());
        headers.put("reportAuthorization", tokenInfo.getAccessToken());

        CmrdsAutoReportDataDTO dataDTO = new CmrdsAutoReportDataDTO();
        dataDTO.setYear("2025");
        dataDTO.setPatIdNum("441282198004281124");
        List<CmrdsAutoReportDataFormDTO> list = new ArrayList<>();
        CmrdsAutoReportDataFormDTO formDTO = new CmrdsAutoReportDataFormDTO();
        formDTO.setFormKey("ShiYanShiJianCha");
        formDTO.setJson("{\n" +
                "\"LAB_BLOOD_RT_HB\": \"103\",\n" +
                "\"LAB_BLOOD_RT_HCT\": \"32.4\",\n" +
                "\"LAB_BLOOD_RT_PLT\": \"32.4\",\n" +
                "\"LAB_DIMENSION\": \"Q1\",\n" +
                "\"LAB_INFECT_ANTIHCV\": \"阴性\",\n" +
                "\"LAB_INFECT_HBSAG\": \"阴性\",\n" +
                "\"LAB_INFECT_HIV\": \"阴性\",\n" +
                "\"LAB_INFECT_SYPHILIS\": \"阴性\",\n" +
                "\"LAB_IRON_TEST_SF_YN\": \"未查\",\n" +
                        "\"LAB_BIOCHEMICAL_YN\": \"未查\",\n" +
                "\"LAB_IRON_TEST_TS_YN\": \"未查\",\n" +
                "\"LAB_NUTR_INFLAM_BETA2MG_YN\": \"未查\",\n" +
                "\"LAB_NUTR_INFLAM_CRP_YN\": \"未查\",\n" +
                "\"LAB_NUTR_INFLAM_PAB_YN\": \"未查\",\n" +
                "\"LAB_ROD_CA\": \"2.96\",\n" +
                "\"LAB_ROD_P\": \"2.96\",\n" +
                "\"LAB_ROD_PTH_YN\": \"未查\",\n" +
                "\"LAB_YEAR\": \"2025\"\n" +
                "}");

        list.add(formDTO);
        dataDTO.setList(list);

        String dataStr = JSONObject.toJSON(dataDTO).toString();
        String dataEnc = EncryptUtils.enCode(dataStr, tokenInfo.getEncryptionKey());
        JSONObject body = new JSONObject();
        body.put("data", dataEnc);
        String result = "";
        if(tokenInfo.getProxyIpManager() != null && StringUtils.isNotEmpty(tokenInfo.getProxyIpManager().getProxyHost())){
            result = HttpUtils.postJsonProxy("https://openapi.cnrds.net/AutoReportOpen/ReportData", headers, body.toString(), tokenInfo.getProxyIpManager());
        }else{
            result = HttpUtils.postJson("https://openapi.cnrds.net/AutoReportOpen/ReportData", headers, body.toString());
        }
        System.out.println(result);
    }*/

}
