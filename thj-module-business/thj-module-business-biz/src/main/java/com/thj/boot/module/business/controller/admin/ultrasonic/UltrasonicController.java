package com.thj.boot.module.business.controller.admin.ultrasonic;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.ultrasonic.UltrasonicConvert;
import com.thj.boot.module.business.dal.datado.ultrasonic.UltrasonicDO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicCreateReqVO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicPageReqVO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicRespVO;
import com.thj.boot.module.business.pojo.ultrasonic.vo.UltrasonicUpdateReqVO;
import com.thj.boot.module.business.service.ultrasonic.UltrasonicService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/ultrasonic")
@Validated
public class UltrasonicController {

    @Resource
    private UltrasonicService ultrasonicService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createUltrasonic(@RequestBody UltrasonicCreateReqVO createReqVO) {
        return success(ultrasonicService.createUltrasonic(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateUltrasonic(@RequestBody UltrasonicUpdateReqVO updateReqVO) {
        ultrasonicService.updateUltrasonic(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteUltrasonic(@RequestParam("id") Long id) {
        ultrasonicService.deleteUltrasonic(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<UltrasonicRespVO> getUltrasonic(@RequestParam("id") Long id) {
        UltrasonicDO ultrasonic = ultrasonicService.getUltrasonic(id);
        return success(UltrasonicConvert.INSTANCE.convert(ultrasonic));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<UltrasonicRespVO>> getUltrasonicList(UltrasonicCreateReqVO createReqVO) {
        List<UltrasonicDO> list = ultrasonicService.getUltrasonicList(createReqVO);
        return success(UltrasonicConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<UltrasonicRespVO>> getUltrasonicPage(@RequestBody UltrasonicPageReqVO pageVO) {
        PageResult<UltrasonicDO> pageResult = ultrasonicService.getUltrasonicPage(pageVO);
        return success(UltrasonicConvert.INSTANCE.convertPage(pageResult));
    }


}
