package com.thj.boot.module.business.controller.admin.hisconsumables;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.hisconsumables.HisConsumablesConvert;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesCreateReqVO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesPageReqVO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesRespVO;
import com.thj.boot.module.business.pojo.hisconsumables.vo.HisConsumablesUpdateReqVO;
import com.thj.boot.module.business.service.hisconsumables.HisConsumablesService;
import com.thj.boot.module.system.api.dept.DeptApi;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 中心耗材信息同步表
 */
@RestController
@RequestMapping("/business/his-consumables")
@Validated
public class HisConsumablesController {

    @Resource
    private HisConsumablesService hisConsumablesService;

    @Resource
    private DeptApi deptApi;


    /**
     * 新增
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    public CommonResult<Long> createHisConsumables(@RequestBody HisConsumablesCreateReqVO createReqVO) {
        return success(hisConsumablesService.createHisConsumables(createReqVO));
    }

    /**
     * 修改
     *
     * @param updateReqVO
     * @return
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateHisConsumables(@RequestBody HisConsumablesUpdateReqVO updateReqVO) {
        hisConsumablesService.updateHisConsumables(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteHisConsumables(@RequestParam("id") Long id) {
        hisConsumablesService.deleteHisConsumables(id);
        return success(true);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    public CommonResult<HisConsumablesRespVO> getHisConsumables(@RequestParam("id") Long id) {
        HisConsumablesDO hisConsumables = hisConsumablesService.getHisConsumables(id);
        return success(HisConsumablesConvert.INSTANCE.convert(hisConsumables));
    }

    @GetMapping("/list")
    public CommonResult<List<HisConsumablesRespVO>> getHisConsumablesList(HisConsumablesCreateReqVO createReqVO) {
        List<HisConsumablesDO> list = hisConsumablesService.getHisConsumablesList(createReqVO);
        return success(HisConsumablesConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     *
     * @param pageVO
     * @return
     */
    @PostMapping("/page")
    public CommonResult<PageResult<HisConsumablesRespVO>> getHisConsumablesPage(@RequestBody HisConsumablesPageReqVO pageVO) {
        PageResult<HisConsumablesDO> pageResult = hisConsumablesService.getHisConsumablesPage(pageVO);
        return success(HisConsumablesConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 同步his信息接口
     *
     * @param request
     * @return
     */
    @GetMapping("/getHis")
    public CommonResult<Object> getHis(HttpServletRequest request) {
        return success(hisConsumablesService.asyncHisConsumables(request));
    }

    /**
     * 耗材分类
     */
    @GetMapping("/getConsumableTypeList")
    public CommonResult<List<HisConsumablesRespVO>> getConsumableTypeList() {
        return success(hisConsumablesService.getConsumableTypeList());
    }

    /**
     * 根据耗材类型获取对应的列表信息
     */
    @GetMapping("/getConsumableList")
    public CommonResult<List<HisConsumablesRespVO>> getConsumableList(String type) {
        return success(hisConsumablesService.getConsumableList(type));
    }

}
