package com.thj.boot.module.business.controller.admin.outpatientblood;

import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.outpatientblood.OutpatientBloodConvert;
import com.thj.boot.module.business.dal.datado.outpatientblood.OutpatientBloodDO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodCreateReqVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodPageReqVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodRespVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodUpdateReqVO;
import com.thj.boot.module.business.service.outpatientblood.OutpatientBloodService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 门诊血液透析简历
 */
@RestController
@RequestMapping("/business/outpatient-blood")
@Validated
public class OutpatientBloodController {

    @Resource
    private OutpatientBloodService outpatientBloodService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createOutpatientBlood(@RequestBody OutpatientBloodCreateReqVO createReqVO) {
        return success(outpatientBloodService.createOutpatientBlood(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateOutpatientBlood(@RequestBody OutpatientBloodUpdateReqVO updateReqVO) {
        outpatientBloodService.updateOutpatientBlood(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteOutpatientBlood(@RequestParam("id") Long id) {
        outpatientBloodService.deleteOutpatientBlood(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<OutpatientBloodRespVO> getOutpatientBlood(Long patientId) {
        return success(outpatientBloodService.getOutpatientBlood(patientId));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<OutpatientBloodRespVO>> getOutpatientBloodList(@RequestParam("ids") Collection<Long> ids) {
        List<OutpatientBloodDO> list = outpatientBloodService.getOutpatientBloodList(ids);
        return success(OutpatientBloodConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<OutpatientBloodRespVO>> getOutpatientBloodPage(@RequestBody OutpatientBloodPageReqVO pageVO) {
        PageResult<OutpatientBloodDO> pageResult = outpatientBloodService.getOutpatientBloodPage(pageVO);
        return success(OutpatientBloodConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 新增/修改
     */
    @PostMapping("/createOrUpdate")
    @RepeatSubmit
    public CommonResult<Boolean> createOrUpdateOutpatientBlood(@RequestBody OutpatientBloodUpdateReqVO updateReqVO) {
        outpatientBloodService.createOrUpdateOutpatientBlood(updateReqVO);
        return success(true);
    }


    /**
     * 修改门诊血液透析数据结构
     */
    @GetMapping("/updateContent")
    public CommonResult<Boolean> updateContent() {
        outpatientBloodService.updateContent();
        return success(true);
    }



}
