package com.thj.boot.module.business.controller.admin.informedconsent;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.informedconsent.InformedConsentConvert;
import com.thj.boot.module.business.dal.datado.informedconsent.InformedConsentDO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentCreateReqVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentPageReqVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentRespVO;
import com.thj.boot.module.business.pojo.informedconsent.vo.InformedConsentUpdateReqVO;
import com.thj.boot.module.business.service.informedconsent.InformedConsentService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 知情同意书
 */
@RestController
@RequestMapping("/business/informed-consent")
@Validated
public class InformedConsentController {

    @Resource
    private InformedConsentService informedConsentService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createInformedConsent(@RequestBody InformedConsentCreateReqVO createReqVO, HttpServletRequest request) {
        return success(informedConsentService.createInformedConsent(createReqVO,request));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateInformedConsent(@RequestBody InformedConsentUpdateReqVO updateReqVO) {
        informedConsentService.updateInformedConsent(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteInformedConsent(@RequestParam("id") Long id) {
        informedConsentService.deleteInformedConsent(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<InformedConsentRespVO> getInformedConsent(@RequestParam("id") Long id) {
        InformedConsentDO informedConsent = informedConsentService.getInformedConsent(id);
        return success(InformedConsentConvert.INSTANCE.convert(informedConsent));
    }

    /**
     * 模版不分页
     */
    @GetMapping("/list")
    public CommonResult<List<InformedConsentRespVO>> getInformedConsentList(InformedConsentCreateReqVO createReqVO) {
        return success(informedConsentService.getInformedConsentList(createReqVO));
    }

    /**
     * 模版分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<InformedConsentRespVO>> getInformedConsentPage(@RequestBody InformedConsentPageReqVO pageVO) {
        PageResult<InformedConsentDO> pageResult = informedConsentService.getInformedConsentPage(pageVO);
        return success(InformedConsentConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 新增app
     */
    @PostMapping("/createApp")
    public CommonResult<Long> createAppInformedConsent(@RequestBody InformedConsentCreateReqVO createReqVO) {
        return success(informedConsentService.createAppInformedConsent(createReqVO));
    }


}
