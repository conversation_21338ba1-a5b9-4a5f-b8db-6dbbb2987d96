package com.thj.boot.module.business.controller.admin.diseasereason;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.diseasereason.DiseaseReasonConvert;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonCreateReqVO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonPageReqVO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonRespVO;
import com.thj.boot.module.business.pojo.diseasereason.vo.DiseaseReasonUpdateReqVO;
import com.thj.boot.module.business.service.diseasereason.DiseaseReasonService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/disease-reason")
@Validated
public class DiseaseReasonController {

    @Resource
    private DiseaseReasonService diseaseReasonService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createDiseaseReason(@RequestBody DiseaseReasonCreateReqVO createReqVO) {
        diseaseReasonService.createDiseaseReason(createReqVO);
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDiseaseReason(@RequestBody DiseaseReasonUpdateReqVO updateReqVO) {
        diseaseReasonService.updateDiseaseReason(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDiseaseReason(@RequestParam("id") Long id) {
        diseaseReasonService.deleteDiseaseReason(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DiseaseReasonRespVO> getDiseaseReason(@RequestParam("id") Long id) {
        return success(diseaseReasonService.getDiseaseReason(id));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DiseaseReasonRespVO>> getDiseaseReasonList(DiseaseReasonCreateReqVO createReqVO) {
        List<DiseaseReasonDO> list = diseaseReasonService.getDiseaseReasonList(createReqVO);
        return success(DiseaseReasonConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DiseaseReasonRespVO>> getDiseaseReasonPage(@RequestBody DiseaseReasonPageReqVO pageVO) {
        return success(diseaseReasonService.getDiseaseReasonPage(pageVO));
    }


}
