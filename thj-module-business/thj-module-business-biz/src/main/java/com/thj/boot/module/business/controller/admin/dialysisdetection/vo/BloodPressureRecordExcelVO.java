package com.thj.boot.module.business.controller.admin.dialysisdetection.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/24 15:10
 * @description
 */
@Data
public class BloodPressureRecordExcelVO implements Serializable {
    /**
     * 透析号
     */
    @ExcelProperty("透析号")
    private String dialyzeNo;
    /**
     * 患者姓名
     */
    @ExcelProperty("透析号")
    private String patientName;
    /**
     * 机号名称
     */
    @ExcelProperty("透析号")
    private String facilityName;
    /**
     * 透析模式名称
     */
    @ExcelProperty("透析号")
    private String dialysisName;
    /**
     * 脉搏
     */
    @ExcelProperty("脉搏")
    private String pulse;
    /**
     * 呼吸
     */
    @ExcelProperty("呼吸")
    private String breathe;
    /**
     * KT/V(在线)
     */
    @ExcelProperty("KT/V(在线)")
    private String ktv;
    /**
     * 血流量
     */
    @ExcelProperty("血流量")
    private String bloodFlow;
    /**
     * 动脉压
     */
    @ExcelProperty("动脉压")
    private String arterialPressure;
    /**
     * 静脉压
     */
    @ExcelProperty("静脉压")
    private String venousPressure;
    /**
     * 跨膜压
     */
    @ExcelProperty("跨膜压")
    private String transmembranePressure;
    /**
     * 超滤率
     */
    @ExcelProperty("超滤率")
    private String ultrafiltrationRate;
    /**
     * 超滤量
     */
    @ExcelProperty("超滤量")
    private String ultrafiltrationCapacity;
    /**
     * 钠浓度
     */
    @ExcelProperty("钠浓度")
    private String sodiumConcentration;
    /**
     * 电导度
     */
    @ExcelProperty("电导度")
    private String conductance;
    /**
     * 透析液温度
     */
    @ExcelProperty("透析液温度")
    private String dialysateTemperature;
    /**
     * 置换率
     */
    @ExcelProperty("置换率")
    private String replacementRate;
    /**
     * 置换量
     */
    @ExcelProperty("置换量")
    private String displacementQuantity;
}
