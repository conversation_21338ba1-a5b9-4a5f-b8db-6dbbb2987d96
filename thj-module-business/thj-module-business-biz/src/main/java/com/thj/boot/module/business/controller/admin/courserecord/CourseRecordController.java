package com.thj.boot.module.business.controller.admin.courserecord;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.courserecord.CourseRecordConvert;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordBatch;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordRespVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordUpdateReqVO;
import com.thj.boot.module.business.service.courserecord.CourseRecordService;
import com.thj.boot.module.business.service.infra.InfraService;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 病程记录
 */
@RestController
@RequestMapping("/business/course-record")
@Validated
public class CourseRecordController {

    @Resource
    private CourseRecordService courseRecordService;

    @Resource
    private InfraService infraService;

    @Autowired
    private SpringTemplateEngine springTemplateEngine;
    @Resource
    private AdminUserApi adminUserApi;


    /**
     * 新增
     */
    @RepeatSubmit
    @PostMapping("/create")
    public CommonResult<Long> createCourseRecord(@RequestBody CourseRecordCreateReqVO createReqVO) {
        return success(courseRecordService.createCourseRecord(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateCourseRecord(@RequestBody CourseRecordUpdateReqVO updateReqVO) {
        courseRecordService.updateCourseRecord(updateReqVO);
        return success(true);
    }
    /**
     * 批量修改
     */
    @PostMapping("/updateBatch")
    public CommonResult<Boolean> updateCourseRecordBatch(@RequestBody CourseRecordBatch courseRecordBatch) {

        courseRecordService.updateBatch(courseRecordBatch);
        return success(true);
    }
    /**
     * 更新原医嘱修改
     */
    @PostMapping("/updateSource")
    public CommonResult<Boolean> updateSourceCourseRecord() {
        courseRecordService.updateSourceCourseRecord();
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteCourseRecord(@RequestParam("id") Long id) {
        courseRecordService.deleteCourseRecord(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<CourseRecordRespVO> getCourseRecord(@RequestParam("id") Long id) {
        CourseRecordDO courseRecord = courseRecordService.getCourseRecord(id);
        CourseRecordRespVO courseRecordRespVO = CourseRecordConvert.INSTANCE.convert(courseRecord);
        courseRecordRespVO.setInfraRespVOList(infraService.getInfraListByIds(StrUtil.isNotBlank(courseRecord.getInfraIds()) ? Arrays.stream(courseRecord.getInfraIds().split(StrUtil.COMMA)).map(Long::valueOf).collect(Collectors.toList()) : null));
        return success(courseRecordRespVO);
    }

    /**
     * 不分页
     */
    @PostMapping("/list")
    public CommonResult<List<CourseRecordRespVO>> getCourseRecordList(@RequestBody CourseRecordCreateReqVO createReqVO) {
        return success(CourseRecordConvert.INSTANCE.convertList(courseRecordService.getCourseRecordList(createReqVO)));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<CourseRecordRespVO>> getCourseRecordPage(@RequestBody CourseRecordPageReqVO pageVO) {
        PageResult<CourseRecordDO> courseRecordPage = courseRecordService.getCourseRecordPage(pageVO);
        List<CourseRecordDO> list = courseRecordPage.getList();

        if (CollUtil.isNotEmpty(list)) {
            List<CourseRecordDO> collect = list.stream().sorted(Comparator.comparing(CourseRecordDO::getStartTime).reversed()).collect(Collectors.toList());
            courseRecordPage.setList(collect);
        }
        PageResult<CourseRecordRespVO> courseRecordRespVOPageResult = CourseRecordConvert.INSTANCE.convertPage(courseRecordPage);
        List<CourseRecordRespVO> list1 = courseRecordRespVOPageResult.getList();
        if (!CollectionUtils.isEmpty(list1)) {
            list1.forEach(courseRecordRespVO -> {
                if (!StringUtils.isEmpty(courseRecordRespVO.getUserId())) {
                    UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(courseRecordRespVO.getUserId());
                    if (!StringUtils.isEmpty(adminUserInfo)) {
                        courseRecordRespVO.setDoctorName(adminUserInfo.getNickname());
                    }
                }
            });
        }
        courseRecordRespVOPageResult.setList(list1);
        return success(courseRecordRespVOPageResult);
    }

    @PostMapping("/exportWord")
    public CommonResult<Boolean> thymeleafExport(@RequestBody CourseRecordCreateReqVO createReqVO,HttpServletResponse response){

        courseRecordService.generateSummary(createReqVO,response);

        return  success(true);
    }

    @GetMapping("/getResult")
    public CommonResult<Boolean> getResult(){

        courseRecordService.getResult();

        return  success(true);
    }

}
