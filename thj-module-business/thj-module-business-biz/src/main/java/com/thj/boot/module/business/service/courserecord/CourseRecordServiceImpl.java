package com.thj.boot.module.business.service.courserecord;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.DateUtils;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.api.infra.vo.InfraResult;
import com.thj.boot.module.business.convert.courserecord.CourseRecordConvert;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordBatch;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.dal.datado.courserecord.SourceCourseRecordDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.renalproject.RenalProjectDO;
import com.thj.boot.module.business.dal.datado.renalprojectinfo.RenalProjectInfoDO;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.courserecord.CourseRecordMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper;
import com.thj.boot.module.business.dal.mapper.renalprojectinfo.RenalProjectInfoMapper;
import com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordUpdateReqVO;
import com.thj.boot.module.business.utils.HtmlTableUtils;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 病程记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CourseRecordServiceImpl implements CourseRecordService  {

    @Resource
    private CourseRecordMapper courseRecordMapper;

    @Resource
    private PatientMapper patientMapper;

    @Autowired
    private SpringTemplateEngine springTemplateEngine;

    @Autowired
    private BloodRoadMapper bloodRoadMapper;

    @Autowired
    private VascularAccessMapper vascularAccessMapper;

    @Autowired
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Autowired
    private RenalProjectMapper renalProjectMapper;

    @Autowired
    private RenalProjectInfoMapper renalProjectInfoMapper;

    @Autowired
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    RedissonClient redissonClient;

    private static final String QUEUE_NAME = "course_record_queue";

    @Override
    public Long createCourseRecord(CourseRecordCreateReqVO createReqVO) {
        // 插入
        CourseRecordDO courseRecord = CourseRecordConvert.INSTANCE.convert(createReqVO);
        setPatientInfo(courseRecord);
        courseRecordMapper.insert(courseRecord);
        updateSourceCourseRecordSingle(courseRecord.getId());
//        RBlockingQueue<String> blockingQueue = redissonClient.getBlockingQueue(QUEUE_NAME);
//        RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
//        delayedQueue.offer(courseRecord.getId().toString(),3, TimeUnit.SECONDS);
        // 返回
        return courseRecord.getId();
    }
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        RBlockingQueue<String> blockingQueue = redissonClient.getBlockingQueue(QUEUE_NAME);
//        RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
//        Executors.newSingleThreadExecutor().submit(()->{
//            while(true){
//                try{
//                    Long id = Long.valueOf(blockingQueue.take());
//                    updateSourceCourseRecordSingle(id);
//                }catch (Exception e){
//
//                }
//            }
//        });
//    }

    public void updateSourceCourseRecordSingle(Long id) {
//        System.out.println("开始执行：");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse("2025-03-01");
        } catch (ParseException e) {
//            System.out.println("错误信息1：" + e.getMessage());
        }
        List<CourseRecordDO> courseRecordDOS1 = new ArrayList<>();
        courseRecordDOS1.add(courseRecordMapper.selectById(id));
        if(CollectionUtils.isEmpty(courseRecordDOS1)){
            return;
        }
        List<CourseRecordDO> courseRecordDOS2 = null;
        try {
            courseRecordDOS2 = courseRecordMapper.selectList(
                    new LambdaQueryWrapper<CourseRecordDO>()
                            .in(CourseRecordDO::getPatientId,courseRecordDOS1.stream()
                                    .map(item->item.getPatientId())
                                    .collect(Collectors.toList()))
                            .ge(CourseRecordDO :: getStartTime,sdf.parse("2025-01-01"))
                            .like(CourseRecordDO :: getRecordName,"%季度小结%")
                            .eq(CourseRecordDO::getDeleted,0));
        } catch (ParseException e) {
//            System.out.println("错误信息2：" + e.getMessage());
        }
        if(CollectionUtils.isEmpty(courseRecordDOS2)){
            return;
        }
        courseRecordDOS1 = courseRecordDOS1.stream().map(item->{
            item.setIsCompleted(1);
            return item;
        }).collect(Collectors.toList());
        courseRecordMapper.updateBatch(courseRecordDOS1);
//        try {
        Map<Integer,Date> map = new HashMap<>();
        for(int i = 0;!CollectionUtils.isEmpty(courseRecordDOS1) && i < courseRecordDOS1.size();i++){
            CourseRecordDO courseRecordDO = courseRecordDOS1.get(i);
            String targetHtml = courseRecordDO.getContent();
            for(int j = 0;!CollectionUtils.isEmpty(courseRecordDOS2) && j < courseRecordDOS2.size();j++){
                try{
                    if(courseRecordDO.getPatientId().equals(courseRecordDOS2.get(j).getPatientId())){
                        Date date1 = map.getOrDefault(courseRecordDO.getPatientId(),courseRecordDOS2.get(j).getStartTime());
                        if(courseRecordDOS2.get(j).getStartTime().compareTo(date1) >= 0 &&
                                courseRecordDOS2.get(j).getStartTime().compareTo(courseRecordDO.getStartTime()) < 0){

                            String sourceHtml = courseRecordDOS2.get(j).getContent();

                            for(int k = HtmlTableUtils.START_ROW_INDEX;k <= HtmlTableUtils.END_ROW_INDEX;k++ ){
                                try{
                                    String cellContent = HtmlTableUtils.extractTableCell(sourceHtml, HtmlTableUtils.TABLE_INDEX, k, HtmlTableUtils.SOURCE_COL_INDEX);
                                    if(StrUtil.isNotEmpty(cellContent)){
                                        targetHtml = HtmlTableUtils.modifyTableCell(targetHtml, HtmlTableUtils.TABLE_INDEX, k, HtmlTableUtils.TARGET_COL_INDEX, cellContent);
                                    }
                                }catch (Exception e){
//                                        System.out.println("错误信息3：" + e.getMessage());
                                }
                            }
                            if(targetHtml.equals(courseRecordDO.getContent())){
                                continue;
                            }
                            map.put(courseRecordDO.getPatientId(),courseRecordDOS2.get(j).getStartTime());
                        }
//
                    }
                }catch (Exception e){
//                        System.out.println("错误信息4：" + e.getMessage());
                }

            }
            if(StrUtil.isNotEmpty(targetHtml)){
                CourseRecordDO courseRecord = CourseRecordDO.builder().content(targetHtml).id(courseRecordDO.getId())
                        .isCompleted(2).build();
                try{

                    int num = courseRecordMapper.updateRecord(courseRecord)
                            ;

                }catch (Exception e){

                }
            }

        }



//        } catch (Exception e) {
//
//        }
    }

    @Override
    public void updateCourseRecord(CourseRecordUpdateReqVO updateReqVO) {
        // 更新
        CourseRecordDO updateObj = CourseRecordConvert.INSTANCE.convert(updateReqVO);
        if (CollUtil.isNotEmpty(updateReqVO.getInfraResultList())) {
            //附件ID集合
            updateObj.setInfraIds(StringUtils.join(updateReqVO.getInfraResultList().stream().map(InfraResult::getId).collect(Collectors.toList()), StrUtil.COMMA));
        }
        setPatientInfo(updateObj);
        courseRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteCourseRecord(Long id) {
        // 删除
        courseRecordMapper.deleteById(id);
    }


    @Override
    public CourseRecordDO getCourseRecord(Long id) {
        return courseRecordMapper.selectById(id);
    }

    @Override
    public List<CourseRecordDO> getCourseRecordList(Collection<Long> ids) {
        return courseRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<CourseRecordDO> getCourseRecordPage(CourseRecordPageReqVO pageReqVO) {
        return courseRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CourseRecordDO> getCourseRecordList(CourseRecordCreateReqVO createReqVO) {
        return courseRecordMapper.selectList(createReqVO);
    }

    @Override
    public void generateSummary(CourseRecordCreateReqVO createReqVO, HttpServletResponse response) {


        String fileName = "quarterly_summary";

        Map<String,Object> map = new HashMap<>();
        // 查询基本信息
        PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
        if (StringUtils.isNotNull(patientDO)) {
            //String synFlag = redisTemplate.opsForValue().get("summary:" + patientDO.getDeptId());

            //if (StringUtils.isNotEmpty(synFlag)) {


                Integer generateYear = createReqVO.getGenerateYear();
                Integer generateSeason = createReqVO.getGenerateSeason();

                Date receiveTime = patientDO.getReceiveTime();
                if (StringUtils.isNotNull(receiveTime)) {
                    Calendar instance = Calendar.getInstance();
                    instance.setTime(receiveTime);
                    int year = instance.get(Calendar.YEAR);
                    int month = instance.get(Calendar.MONTH) + 1;
                    int day = instance.get(Calendar.DAY_OF_MONTH);
                    map.put("firstYear", year);
                    map.put("firstMonth", month);
                    map.put("firstDay", day);
                    map.put("dialysisAge", patientDO.getDialyzeAge());
                }
                BloodRoadDO bloodRoadDO = bloodRoadMapper.selectOne(new LambdaQueryWrapperX<BloodRoadDO>()
                        .eq(BloodRoadDO::getPatientId, createReqVO.getPatientId())
                        .orderByDesc(BloodRoadDO::getCreateTime)
                        .last("limit 1"));
                if (StringUtils.isNotNull(bloodRoadDO)) {
                    Long part = bloodRoadDO.getPart();
                    VascularAccessDO vascularAccessDO = vascularAccessMapper.selectById(part);
                    Long accessRoad = bloodRoadDO.getType();
                    VascularAccessDO vascularAccessDO1 = vascularAccessMapper.selectById(accessRoad);
                    if (StringUtils.isNotNull(vascularAccessDO) && StringUtils.isNotNull(vascularAccessDO1)) {
                        map.put("blood", vascularAccessDO.getName() + " " + vascularAccessDO1.getName());
                    }
                }

                // 获取hd数量
                LocalDate now = LocalDate.now();
                int nowYear = now.getYear();
                map.put("nowYear", nowYear);
                int month = now.getMonthValue();
                map.put("nowMonth", month);
                int dayOfMonth = now.getDayOfMonth();
                map.put("nowDay", dayOfMonth);
                Date startTime = null;
                Date endTime = null;
                Date lastStartTime = null;
                Date lastEndTime = null;
                if (generateYear.equals(nowYear)) {
                    if (month >= 1 && month <= 3) {
                        if (generateSeason > 1) {
                            throw new ServiceException(-1, "超过当前季度");
                        }
                    }
                    if (month > 3 && month <= 6) {
                        if (generateSeason > 2) {
                            throw new ServiceException(-1, "超过当前季度");
                        }
                    }
                    if (month > 6 && month <= 9) {
                        if (generateSeason > 3) {
                            throw new ServiceException(-1, "超过当前季度");
                        }
                    }
                }

                if (1 == generateSeason) {
                    startTime = DateUtils.buildTime(generateYear, 1, 1);
                    endTime = DateUtils.buildTime(generateYear, 3, 31);

                    lastStartTime = DateUtils.buildTime(generateYear - 1, 10, 1);
                    lastEndTime = DateUtils.buildTime(generateYear - 1, 12, 31);
                    map.put("season", 1);
                } else if (2 == generateSeason) {
                    startTime = DateUtils.buildTime(generateYear, 4, 1);
                    endTime = DateUtils.buildTime(generateYear, 6, 30);
                    lastStartTime = DateUtils.buildTime(generateYear, 1, 1);
                    lastEndTime = DateUtils.buildTime(generateYear, 3, 31);
                    map.put("season", 2);
                } else if (3 == generateSeason) {
                    startTime = DateUtils.buildTime(generateYear, 7, 1);
                    endTime = DateUtils.buildTime(generateYear, 9, 30);
                    lastStartTime = DateUtils.buildTime(generateYear, 4, 1);
                    lastEndTime = DateUtils.buildTime(generateYear, 6, 30);
                    map.put("season", 3);
                } else {
                    startTime = DateUtils.buildTime(generateYear, 10, 1);
                    endTime = DateUtils.buildTime(generateYear, 12, 31);
                    lastStartTime = DateUtils.buildTime(generateYear, 7, 1);
                    lastEndTime = DateUtils.buildTime(generateYear, 9, 30);
                    map.put("season", 4);
                }
                List<HemodialysisManagerDO> hemodialysisManagerDOS = hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .between(HemodialysisManagerDO::getHemodialysisTime, startTime, endTime));
                map.put("hdNum", 0);
                map.put("hdfNum", 0);
                map.put("hdpNum", 0);
                if (!CollectionUtils.isEmpty(hemodialysisManagerDOS)) {
                    List<HemodialysisManagerDO> collect = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "1".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        map.put("hdNum", collect.size());
                    }

                    List<HemodialysisManagerDO> collect1 = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "2".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect1)) {
                        map.put("hdfNum", collect1.size());
                    }

                    List<HemodialysisManagerDO> collect2 = hemodialysisManagerDOS.stream().filter(hemodialysisManagerDO -> "3".equals(hemodialysisManagerDO.getDialyzeWayValue())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect2)) {
                        map.put("hdpNum", collect2.size());
                    }
                }

                List<HemodialysisManagerDO> lastHemodialysisManagerDOS = hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .eq(HemodialysisManagerDO::getHealState,1)
                        .eq(HemodialysisManagerDO::getDeleted,0)
                        .between(HemodialysisManagerDO::getHemodialysisTime, lastStartTime, lastEndTime));

                List<HemodialysisManagerDO> thisHemodialysisManagerDOS = hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eq(HemodialysisManagerDO::getPatientId, createReqVO.getPatientId())
                        .eq(HemodialysisManagerDO::getHealState,1)
                        .eq(HemodialysisManagerDO::getDeleted,0)
                        .between(HemodialysisManagerDO::getHemodialysisTime, startTime, endTime));

                if (!CollectionUtils.isEmpty(lastHemodialysisManagerDOS)) {


                    //干体重上次	lastWeight
                    BigDecimal divide = getResult(lastHemodialysisManagerDOS, 1);
                    map.put("lastWeight", divide);

                    //体重增长量上次	lastWeightAdd
                    BigDecimal divide1 = getResult(lastHemodialysisManagerDOS, 2);
                    map.put("lastWeightAdd", divide1);

                    //上次血压透前60岁以下<140/90	lastPressureLess60
                    BigDecimal bpOneResult = getResult(lastHemodialysisManagerDOS, 3);
                    BigDecimal bpTwoResult = getResult(lastHemodialysisManagerDOS, 4);

                    if (patientDO.getAge() <= 60) {
                        map.put("lastPressureLess60", bpOneResult + "/" + bpTwoResult);

                    }
                    //上次血压透前60岁以上<160/90	lastPressureMore60
                    if (patientDO.getAge() > 60) {
                        map.put("lastPressureMore60", bpOneResult + "/" + bpTwoResult);
                    }
                }

                if (!CollectionUtils.isEmpty(thisHemodialysisManagerDOS)) {
                    //干体重本次	thisWeight
                    BigDecimal divide = getResult(thisHemodialysisManagerDOS, 1);
                    map.put("thisWeight", divide);
                    //体重增长量本次	thisWeightAdd
                    BigDecimal divide1 = getResult(thisHemodialysisManagerDOS, 2);
                    map.put("thisWeightAdd", divide1);
                    //体重增长量是否达标	weightTarget
                    BigDecimal weightTarget = divide1.divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP).divide(divide, 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP);
                    if (weightTarget.compareTo(BigDecimal.valueOf(5)) < 0) {
                        map.put("weightTarget", "是");
                    } else {
                        map.put("weightTarget", "否");
                    }

                    BigDecimal bpOneResult = getResult(thisHemodialysisManagerDOS, 3);
                    BigDecimal bpTwoResult = getResult(thisHemodialysisManagerDOS, 4);

                    if (patientDO.getAge() <= 60) {
                        //本次血压透前60岁以下<140/90	thisPressureLess60
                        map.put("thisPressureLess60", bpOneResult + "/" + bpTwoResult);
                        if (bpOneResult.compareTo(BigDecimal.valueOf(140)) <= 0 && bpTwoResult.compareTo(BigDecimal.valueOf(90)) <= 0) {
                            // 本次血压透前60岁以下是否达标       thisPressureTarget
                            map.put("thisPressureTarget", "是");
                        } else {
                            map.put("thisPressureTarget", "否");
                        }

                    }
                    //本次血压透前60岁以上<160/90	lastPressureMore60
                    if (patientDO.getAge() > 60) {
                        map.put("thisPressureMore60", bpOneResult + "/" + bpTwoResult);
                        if (bpOneResult.compareTo(BigDecimal.valueOf(160)) <= 0 && bpTwoResult.compareTo(BigDecimal.valueOf(90)) <= 0) {
                            // 本次血压透前60岁以下是否达标       thisPressureTarget
                            map.put("thisPressureMoreTarget", "是");
                        } else {
                            map.put("thisPressureMoreTarget", "否");
                        }
                    }

                }

                //上次URR	lastURR
                //本次URR	thisURR
                //本次URR是否达标	urrTarget
                //上次kt/v	lastKtv
                //本次kt/v	thisKtv
                //本次kt/v是否达标	ktvTarget
                List<RenalProjectInfoDO> ktvLastResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "738");

                if (!CollectionUtils.isEmpty(ktvLastResultList)) {
                    ktvLastResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(97).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次URR	lastURR
                            map.put("lastURR", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(98).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次kt/v	lastKtv
                            map.put("lastKtv", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }
                List<RenalProjectInfoDO> ktvThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "738");

                if (!CollectionUtils.isEmpty(ktvThisResultList)) {
                    ktvThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(97).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次URR	thisURR
                            map.put("thisURR", renalProjectInfoDO.getJsonValue());

                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                map.put("thisURR", renalProjectInfoDO.getJsonValue().split("%")[0]);
                                //本次URR是否达标	urrTarget
                                double thisURR = Double.valueOf(renalProjectInfoDO.getJsonValue().split("%")[0]);
                                if (thisURR >= 68) {
                                    map.put("urrTarget", "是");
                                } else {
                                    map.put("urrTarget", "否");
                                }
                            }

                        }
                        if (Long.valueOf(98).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次kt/v	thisKtv
                            map.put("thisKtv", renalProjectInfoDO.getJsonValue());
                            //本次kt/v是否达标	ktvTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                double thisKtv = Double.valueOf(renalProjectInfoDO.getJsonValue().split("%")[0]);
                                if (thisKtv >= 1.2) {
                                    map.put("ktvTarget", "是");
                                } else {
                                    map.put("ktvTarget", "否");
                                }
                            }
                        }
                    });
                }

                //上次血红蛋白	lastHemoglobin
                //本次血红蛋白	thisHemoglobin
                //本次血红蛋白是否达标	hemoglobinTarget
                //上次血小板	lastPlatelet
                //本次血小板	thisPlatelet
                //本次血小板是否达标	thisPlateletTarget
                List<RenalProjectInfoDO> bloodResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "728");

                if (!CollectionUtils.isEmpty(bloodResultList)) {
                    //上次时间	lastDate
                    RenalProjectInfoDO renalProjectInfoDO1 = bloodResultList.get(bloodResultList.size() - 1);
                    Date checkTime = renalProjectInfoDO1.getCheckTime();

                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String lastDate = simpleDateFormat.format(checkTime);
                    map.put("lastDate", lastDate);
                    bloodResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(2).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血红蛋白	lastHemoglobin
                            map.put("lastHemoglobin", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(4).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血小板	lastPlatelet
                            map.put("lastPlatelet", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }

                List<RenalProjectInfoDO> bloodThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "728");

                if (!CollectionUtils.isEmpty(bloodThisResultList)) {
                    //本次时间	检查时间 thisDate
                    RenalProjectInfoDO renalProjectInfoDO1 = bloodThisResultList.get(bloodThisResultList.size() - 1);
                    Date checkTime = renalProjectInfoDO1.getCheckTime();

                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String thisDate = simpleDateFormat.format(checkTime);
                    map.put("thisDate", thisDate);

                    bloodThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(2).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血红蛋白	thisHemoglobin
                            map.put("thisHemoglobin", renalProjectInfoDO.getJsonValue());
                            //本次血红蛋白是否达标	hemoglobinTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double hemoglobinTarget = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (hemoglobinTarget >= 110 && hemoglobinTarget <= 130) {
                                    map.put("hemoglobinTarget", "是");
                                } else {
                                    map.put("hemoglobinTarget", "否");
                                }
                            }
                        }
                        if (Long.valueOf(4).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血小板	thisPlatelet
                            map.put("thisPlatelet", renalProjectInfoDO.getJsonValue());
                            //本次血小板是否达标	thisPlateletTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisPlatelet = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisPlatelet >= 100 && thisPlatelet <= 300) {
                                    map.put("thisPlateletTarget", "是");
                                } else {
                                    map.put("thisPlateletTarget", "否");
                                }
                            }
                        }
                    });

                }


                //上次血钙	lastCalcium
                //本次血钙	thisCalcium
                //本次血钙是否达标	thisCalciumTarget
                //上次血磷	lastPhosphorus
                //本次血磷	thisPhosphorus
                //本次血磷是否达标	phosphorusTarget
                //上次血钾	lastPotassium
                //本次血钾	thisPotassium
                //本次血钾是否达标	otassiumTarget
                List<RenalProjectInfoDO> elecResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "735");

                if (!CollectionUtils.isEmpty(elecResultList)) {
                    elecResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(71).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血钙	lastCalcium
                            map.put("lastCalcium", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(74).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血磷	lastPhosphorus
                            map.put("lastPhosphorus", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(67).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血钾	lastPotassium
                            map.put("lastPotassium", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }

                List<RenalProjectInfoDO> elecThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "735");

                if (!CollectionUtils.isEmpty(elecThisResultList)) {
                    elecThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(71).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血钙	thisCalcium
                            map.put("thisCalcium", renalProjectInfoDO.getJsonValue());

                            //本次血钙是否达标	thisCalciumTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisCalcium = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisCalcium >= 2.1 && thisCalcium <= 2.5) {
                                    map.put("thisCalciumTarget", "是");
                                } else {
                                    map.put("thisCalciumTarget", "否");
                                }
                            }
                        }
                        if (Long.valueOf(74).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血磷	thisPhosphorus
                            map.put("thisPhosphorus", renalProjectInfoDO.getJsonValue());

                            //本次血磷是否达标	phosphorusTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisPhosphorus = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisPhosphorus >= 1.13 && thisPhosphorus <= 1.78) {
                                    map.put("phosphorusTarget", "是");
                                } else {
                                    map.put("phosphorusTarget", "否");
                                }
                            }
                        }
                        if (Long.valueOf(67).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血钾	thisPotassium
                            map.put("thisPotassium", renalProjectInfoDO.getJsonValue());
                            //本次血钾是否达标	otassiumTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisPotassium = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisPotassium >= 3.5 && thisPotassium <= 5.5) {
                                    map.put("otassiumTarget", "是");
                                } else {
                                    map.put("otassiumTarget", "否");
                                }
                            }
                        }
                    });

                }


                //上次PTH	lastPTH
                //本次PTH	thisPTH
                //本次PTH是否达标	pthTarget
                List<RenalProjectInfoDO> pthLastResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "736");

                if (!CollectionUtils.isEmpty(pthLastResultList)) {
                    pthLastResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(84).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次PTH	lastPTH
                            map.put("lastPTH", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }

                List<RenalProjectInfoDO> pthThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "736");

                if (!CollectionUtils.isEmpty(pthThisResultList)) {
                    pthThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(84).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次PTH	thisPTH
                            map.put("thisPTH", renalProjectInfoDO.getJsonValue());

                            //本次PTH是否达标	pthTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisPTH = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisPTH >= 150 && thisPTH <= 300) {
                                    map.put("pthTarget", "是");
                                } else {
                                    map.put("pthTarget", "否");
                                }
                            }

                        }
                    });
                }

                //上次血清白蛋白	lastSA
                //本次血清白蛋白	thisSA
                //血清白蛋白是否达标	saTarget
                //上次碱性磷酸酶	lastALP
                //本次碱性磷酸酶	thisALP
                //碱性磷酸酶是否达标	alpTarget
                List<RenalProjectInfoDO> ganResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "732");

                if (!CollectionUtils.isEmpty(ganResultList)) {
                    ganResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(32).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次血清白蛋白	lastSA
                            map.put("lastSA", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(38).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次碱性磷酸酶	lastALP
                            map.put("lastALP", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }

                List<RenalProjectInfoDO> ganThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "732");

                if (!CollectionUtils.isEmpty(ganThisResultList)) {
                    ganThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(32).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次血清白蛋白	thisSA
                            map.put("thisSA", renalProjectInfoDO.getJsonValue());

                            //血清白蛋白是否达标	saTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {

                                double thisSA = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisSA >= 35) {
                                    map.put("saTarget", "是");
                                } else {
                                    map.put("saTarget", "否");
                                }
                            }
                        }
                        if (Long.valueOf(38).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次碱性磷酸酶	thisALP
                            map.put("thisALP", renalProjectInfoDO.getJsonValue());

                            //碱性磷酸酶是否达标	alpTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                double thisALP = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisALP >= 40 && thisALP <= 150) {
                                    map.put("alpTarget", "是");
                                } else {
                                    map.put("alpTarget", "否");
                                }
                            }

                        }
                    });
                }

                //上次铁蛋白	lastFerritin
                //本次铁蛋白	thisFerritin
                //本次铁蛋白是否达标	ferritinTarget
                //上次转铁蛋白饱和度	lastTsat
                //本次转铁蛋白饱和度	thisTsat
                //本次转铁蛋白饱和度是否达标	tsatTarget
                List<RenalProjectInfoDO> feResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "729");

                if (!CollectionUtils.isEmpty(feResultList)) {
                    feResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(15).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次铁蛋白	lastFerritin
                            map.put("lastFerritin", renalProjectInfoDO.getJsonValue());
                        }
                        if (Long.valueOf(17).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次转铁蛋白饱和度	lastTsat
                            map.put("lastTsat", renalProjectInfoDO.getJsonValue());
                        }
                    });
                }

                List<RenalProjectInfoDO> feThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "729");

                if (!CollectionUtils.isEmpty(feThisResultList)) {
                    feThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(15).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次铁蛋白	thisFerritin
                            map.put("thisFerritin", renalProjectInfoDO.getJsonValue());

                            //本次铁蛋白是否达标	ferritinTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisFerritin = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisFerritin >= 200 && thisFerritin <= 500) {
                                    map.put("ferritinTarget", "是");
                                } else {
                                    map.put("ferritinTarget", "否");
                                }
                            }else {
                                map.put("ferritinTarget", "否");
                            }

                        }
                        if (Long.valueOf(17).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次转铁蛋白饱和度	thisTsat
                            map.put("thisTsat", renalProjectInfoDO.getJsonValue());

                            //本次转铁蛋白饱和度是否达标	tsatTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisTsat = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisTsat > 20) {
                                    map.put("tsatTarget", "是");
                                } else {
                                    map.put("tsatTarget", "否");
                                }
                            }

                        }
                    });
                }


                //上次C反应蛋白	lastCRP
                //本次C反应蛋白	thisCRP
                //本次C反应蛋白是否达标	crpTarget
                List<RenalProjectInfoDO> cLastResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "731");

                if (!CollectionUtils.isEmpty(cLastResultList)) {
                    cLastResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(29).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次C反应蛋白	lastCRP
                            map.put("lastCRP", renalProjectInfoDO.getJsonValue());
                        }
                    /*if (Long.valueOf(28).equals(renalProjectInfoDO.getJsonKey())) {
                        //上次C反应蛋白	lastCRP
                        map.put("lastCRP",renalProjectInfoDO.getJsonValue());
                    }*/
                    });
                }

                List<RenalProjectInfoDO> cThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "731");

                if (!CollectionUtils.isEmpty(cThisResultList)) {
                    cThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(29).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次C反应蛋白	thisCRP
                            map.put("thisCRP", renalProjectInfoDO.getJsonValue());

                            //本次C反应蛋白是否达标	crpTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                if(renalProjectInfoDO.getJsonValue().contains("<") || renalProjectInfoDO.getJsonValue().contains("＜")) {
                                    map.put("crpTarget","是");
                                }else  {
                                    double thisCRP = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                    if (thisCRP >= 0 && thisCRP <= 8) {
                                        map.put("crpTarget", "是");
                                    } else {
                                        map.put("crpTarget", "否");
                                    }
                                }

                            }
                        }
                    });
                }


                //上次β2微球蛋白	lastMG
                //本次β2微球蛋白	thisMG
                //本次微球蛋白是否达标	mgTarget
                List<RenalProjectInfoDO> wqdbLastResultList = getExamResult(createReqVO.getPatientId(), lastStartTime, lastEndTime, "734");

                if (!CollectionUtils.isEmpty(wqdbLastResultList)) {
                    wqdbLastResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(61).equals(renalProjectInfoDO.getJsonKey())) {
                            //上次β2微球蛋白	lastMG
                            map.put("lastMG", renalProjectInfoDO.getJsonValue());
                        }
                    /*if (Long.valueOf(28).equals(renalProjectInfoDO.getJsonKey())) {
                        //上次C反应蛋白	lastCRP
                        map.put("lastCRP",renalProjectInfoDO.getJsonValue());
                    }*/
                    });
                }

                List<RenalProjectInfoDO> wqdbThisResultList = getExamResult(createReqVO.getPatientId(), startTime, endTime, "734");

                if (!CollectionUtils.isEmpty(wqdbThisResultList)) {
                    wqdbThisResultList.forEach(renalProjectInfoDO -> {
                        if (Long.valueOf(61).equals(renalProjectInfoDO.getJsonKey())) {
                            //本次β2微球蛋白	thisMG
                            map.put("thisMG", renalProjectInfoDO.getJsonValue());

                            //本次微球蛋白是否达标	mgTarget
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<") && !renalProjectInfoDO.getJsonValue().contains(">")) {
                                double thisMG = Double.valueOf(renalProjectInfoDO.getJsonValue());
                                if (thisMG >= 0 && thisMG <= 20) {
                                    map.put("mgTarget", "是");
                                } else {
                                    map.put("mgTarget", "否");
                                }
                            }
                        }
                    });
                }


                //原医嘱1	lastAdvice1
                //原医嘱2	lastAdvice2
                //原医嘱3	lastAdvice3
                //原医嘱4	lastAdvice4
                //原医嘱5	lastAdvice5
                //原医嘱6	lastAdvice6
                //原医嘱7	lastAdvice7
                //原医嘱8	lastAdvice8
                //原医嘱9	lastAdvice9
                //原医嘱10	lastAdvice10
                //原医嘱11	lastAdvice11
                //原医嘱12	lastAdvice12
                //原医嘱13	lastAdvice13
                //原医嘱14	lastAdvice14
                //原医嘱15	lastAdvice15
                //原医嘱16	lastAdvice16
                //原医嘱17	lastAdvice17
                //原医嘱18	lastAdvice18


                //现医嘱1	thisAdvice1
                //现医嘱2	thisAdvice2
                //现医嘱3	thisAdvice3
                //现医嘱4	thisAdvice4
                //现医嘱5	thisAdvice5
                //现医嘱6	thisAdvice6
                //现医嘱7	thisAdvice7
                //现医嘱8	thisAdvice8
                //现医嘱9	thisAdvice9
                //现医嘱10	thisAdvice10
                //现医嘱11	thisAdvice11
                //现医嘱12	thisAdvice12
                //现医嘱13	thisAdvice13
                //现医嘱14	thisAdvice14
                //现医嘱15	thisAdvice15
                //现医嘱16	thisAdvice16
                //现医嘱17	thisAdvice17
                //现医嘱18	thisAdvice18
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPatientId, createReqVO.getPatientId())
                        .eq(DialysisAdviceDO::getType, "1")
                        .eq(DialysisAdviceDO::getDeleted, 0)
                        .eq(DialysisAdviceDO::getStopStatus, "0"));

                if (!CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                    List<DictDataRespDTO> frequencyList = dictDataApi.getDictListData("frequency");

                    for (int i = 0; i < dialysisAdviceDOS.size(); i++) {
                        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceDOS.get(i);
                        if (!dialysisAdviceDO.getAdviceName().contains("氯化钠") && dialysisAdviceDO.getAdviceId() > 1000000) {
                            List<DictDataRespDTO> collect = frequencyList.stream().filter(dictDataRespDTO -> dictDataRespDTO.getValue().equals(dialysisAdviceDO.getFrequency())).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(collect)) {
                                String name = dialysisAdviceDO.getAdviceName() + "(" + dialysisAdviceDO.getSpecification() + ") " + dialysisAdviceDO.getOneNo() + dialysisAdviceDO.getFpreparaUnit();
                                map.put("thisAdvice" + (i + 1), name);
                            }else {
                                String name = dialysisAdviceDO.getAdviceName() + "(" + dialysisAdviceDO.getSpecification() + ") " + dialysisAdviceDO.getOneNo() + dialysisAdviceDO.getFpreparaUnit() + " " + collect.get(0).getLabel();
                                map.put("thisAdvice" + (i + 1), name);
                            }
                        }
                    }
                }
            }

            // 设置数据
            Context context = new Context();
            context.setVariables(map);
            String process = springTemplateEngine.process(fileName, context);

            // 新增季度小结
            CourseRecordDO courseRecordDO = new CourseRecordDO();
            courseRecordDO.setPatientId(Math.toIntExact(patientDO.getId()));
            courseRecordDO.setPatientName(patientDO.getNickName());
            courseRecordDO.setPatientNickName(patientDO.getSpellName());
            courseRecordDO.setDialyzeNo(patientDO.getDialyzeNo());
            courseRecordDO.setStartTime(new Date());
            long userId = StpUtil.getLoginIdAsLong();
            courseRecordDO.setUserId(userId);
            courseRecordDO.setName("13");
            courseRecordDO.setContent(process);
            courseRecordDO.setCreator(String.valueOf(userId));
            courseRecordDO.setCreateTime(new Date());
            courseRecordDO.setUpdater(String.valueOf(userId));
            courseRecordDO.setUpdateTime(new Date());
            courseRecordDO.setDeleted(false);
            courseRecordDO.setDeptId(patientDO.getDeptId());
            courseRecordDO.setRecordName("季度小结");

            courseRecordMapper.insert(courseRecordDO);
        //}


        // 下载word
/*        try {
            byte[] bytes = process.getBytes(StandardCharsets.UTF_8);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/msword");
            response.setHeader("Access-Control-Expose-Headers","Content-disposition");
            response.setHeader("Content-disposition","attachment; filename=" +
                    URLEncoder.encode(fileName.concat(".doc"), "UTF-8"));
            ServletOutputStream out = response.getOutputStream();

            out.write(bytes);
            out.flush();
            out.close();
        }catch(Exception e){
            e.printStackTrace();
        }*/

    }

    @Override
    public void getResult() {
        // 转铁蛋白饱和度
        List<PatientDO> patientDOS = patientMapper.selectList(new LambdaQueryWrapperX<PatientDO>()
               .eq(PatientDO::getPatientTypeSource, "00"));
                //.eq(PatientDO::getDeptId,109l));
        if (!CollectionUtils.isEmpty(patientDOS)) {
            Date startTime = DateUtils.buildTime(2025, 6, 25);
            Date endTime = DateUtils.buildTime(2025, 7, 04);
            for (PatientDO patientDO : patientDOS) {
                List<RenalProjectInfoDO> twxThisResultList = getExamResultLast(patientDO.getId(), startTime, endTime, "729");
                if (!CollectionUtils.isEmpty(twxThisResultList)) {
                    String xqt = null;
                    String ztjhl = null;
                    String ztdbbhd = null;
                    RenalProjectInfoDO renalProjectInfoDO1 = twxThisResultList.get(0);
                    RenalProjectInfoDO renalProjectInfoDO2 = null;
                    //本次微球蛋白是否达标	mgTarget
                    for (RenalProjectInfoDO renalProjectInfoDO : twxThisResultList) {
                        if (Long.valueOf(14).equals(renalProjectInfoDO.getJsonKey())) {
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                xqt = renalProjectInfoDO.getJsonValue();
                            }
                        }
                        if (Long.valueOf(16).equals(renalProjectInfoDO.getJsonKey())) {
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                ztjhl = renalProjectInfoDO.getJsonValue();
                            }
                        }
                        if (Long.valueOf(17).equals(renalProjectInfoDO.getJsonKey())) {
                            renalProjectInfoDO2 = renalProjectInfoDO;
                            if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                ztdbbhd = renalProjectInfoDO.getJsonValue();
                            }
                        }
                    }

                    if (StringUtils.isNotEmpty(xqt) && StringUtils.isNotEmpty(ztjhl) && StringUtils.isEmpty(ztdbbhd)) {
                        double iron1 = Double.parseDouble(xqt);
                        double iron3 = Double.parseDouble(ztjhl);
                        double result = iron1 * 100 / iron3;
                        String formattedNumber = String.format("%.2f", result);
                        System.out.println("twx patientId:" + patientDO.getId() + ":" + patientDO.getName());
                        if (org.springframework.util.StringUtils.isEmpty(renalProjectInfoDO2)) {
                            renalProjectInfoDO2 = new RenalProjectInfoDO();
                            BeanUtils.copyProperties(renalProjectInfoDO1, renalProjectInfoDO2);
                            renalProjectInfoDO2.setId(null);
                            renalProjectInfoDO2.setJsonValue(formattedNumber);
                            renalProjectInfoDO2.setJsonKey(17l);
                        } else {
                            renalProjectInfoDO2.setJsonValue(formattedNumber);

                        }
                        renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO2);
                    }
                }


            }
            try {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                Date parse = simpleDateFormat.parse("2025-06-14 00:00:00");
                for (PatientDO patientDO : patientDOS) {
                    List<RenalProjectInfoDO> sgnThisResultList = getExamUrr(patientDO.getId(), startTime, endTime, "734");
                    if (!CollectionUtils.isEmpty(sgnThisResultList)) {
                        RenalProjectInfoDO renalProjectInfoDO1 = sgnThisResultList.get(sgnThisResultList.size() - 1);

                        List<RenalProjectInfoDO> sgnResultList = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                                .eq(RenalProjectInfoDO::getProjectId, renalProjectInfoDO1.getProjectId())
                                .eq(RenalProjectInfoDO::getProjectType, renalProjectInfoDO1.getProjectType()));
                        if (!CollectionUtils.isEmpty(sgnResultList)) {
                            RenalProjectDO sgnProject = getRenalProject(patientDO.getId(), startTime, endTime, "734");
                            // 透析前
                            String before = null;
                            String after = null;
                            for (RenalProjectInfoDO renalProjectInfoDO : sgnResultList) {
                                if (Long.valueOf(57).equals(renalProjectInfoDO.getJsonKey())) {
                                    if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                        before = renalProjectInfoDO.getJsonValue();
                                    }
                                }
                                if (Long.valueOf(58).equals(renalProjectInfoDO.getJsonKey())) {
                                    if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                        after = renalProjectInfoDO.getJsonValue();
                                    }
                                }
                            }
                            if (StringUtils.isNotEmpty(before) && StringUtils.isNotEmpty(after) && !org.springframework.util.StringUtils.isEmpty(sgnProject) && !after.contains("<")) {
                                // 查询ktv
                                RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                                        .eq(RenalProjectDO::getPatientId, patientDO.getId())
                                        .eq(RenalProjectDO::getDeptId, patientDO.getDeptId())
                                        //.eq(RenalProjectDO::getLastCheckTime, sgnProject.getLastCheckTime())
                                        .eq(RenalProjectDO::getDictId, 738L));

                                //
                                before = before.replaceAll("\\s+", "");
                                after = after.replaceAll("\\s+", "");
                                double iron1 = Double.parseDouble(before);
                                double iron3 = Double.parseDouble(after);
                                double result = (iron1 - iron3) * 100 / iron1;
                                String formattedNumber = String.format("%.2f", result) + "%";

                                if (org.springframework.util.StringUtils.isEmpty(renalProjectDO)) {
                                    // 创建ktv
                                    RenalProjectDO renalProjectDO1 = new RenalProjectDO();
                                    renalProjectDO1.setDictId(738L);
                                    renalProjectDO1.setPatientId(patientDO.getId());
                                    renalProjectDO1.setNumber(1);
                                    renalProjectDO1.setLastCheckTime(sgnProject.getLastCheckTime());
                                    renalProjectDO1.setProjectType("0");
                                    renalProjectDO1.setCreator("1");
                                    renalProjectDO1.setCreateTime(new Date());
                                    renalProjectDO1.setUpdater("1");
                                    renalProjectDO1.setUpdateTime(new Date());
                                    renalProjectDO1.setDeleted(false);
                                    renalProjectDO1.setDeptId(patientDO.getDeptId());
                                    renalProjectMapper.insert(renalProjectDO1);
                                    RenalProjectInfoDO renalProjectInfoDO = new RenalProjectInfoDO();
                                    renalProjectInfoDO.setProjectId(renalProjectDO1.getId());
                                    renalProjectInfoDO.setProjectType(1);
                                    renalProjectInfoDO.setCheckTime(sgnProject.getLastCheckTime());
                                    renalProjectInfoDO.setJsonKey(97L);
                                    renalProjectInfoDO.setDeleted(false);
                                    renalProjectInfoDO.setDeptId(patientDO.getDeptId());
                                    renalProjectInfoDO.setCreateTime(new Date());
                                    renalProjectInfoDO.setCreator("1");
                                    renalProjectInfoDO.setUpdater("1");
                                    renalProjectInfoDO.setUpdateTime(new Date());
                                    renalProjectInfoDO.setJsonValue(formattedNumber);

                                    renalProjectInfoMapper.insert(renalProjectInfoDO);

                                } else {
                                    if (!org.springframework.util.StringUtils.isEmpty(renalProjectDO.getLastCheckTime()) && renalProjectDO.getLastCheckTime().equals(sgnProject.getLastCheckTime())) {

                                        List<RenalProjectInfoDO> renalProjectInfoDOList = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                                                .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                                                .eq(RenalProjectInfoDO::getCheckTime, renalProjectDO.getLastCheckTime()));

                                        if (!CollectionUtils.isEmpty(renalProjectInfoDOList)) {
                                            RenalProjectInfoDO renalProjectInfoDO2 = new RenalProjectInfoDO();
                                            for (RenalProjectInfoDO renalProjectInfoDO : renalProjectInfoDOList) {
                                                if (Long.valueOf(97).equals(renalProjectInfoDO.getJsonKey())) {
                                                    if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                                        renalProjectInfoDO2 = renalProjectInfoDO;
                                                        renalProjectInfoDO2.setJsonValue(formattedNumber);
                                                        renalProjectInfoDO2.setProjectType(renalProjectDO.getNumber() + 101);
                                                        renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO2);
                                                    }
                                                }
                                            }
                                            if (org.springframework.util.StringUtils.isEmpty(renalProjectInfoDO2)) {
                                                renalProjectInfoDO2.setJsonValue(formattedNumber);
                                                renalProjectInfoDO2.setProjectType(renalProjectDO.getNumber() + 101);
                                                renalProjectInfoDO2.setJsonKey(97L);
                                                renalProjectInfoDO2.setUpdater("1");
                                                renalProjectInfoDO2.setUpdateTime(new Date());
                                                renalProjectInfoDO2.setCreator("1");
                                                renalProjectInfoDO2.setCheckTime(renalProjectDO.getLastCheckTime());
                                                renalProjectInfoDO2.setCreateTime(new Date());
                                                renalProjectInfoDO2.setProjectId(renalProjectDO.getId());
                                                renalProjectInfoDO2.setDeleted(false);
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO2);
                                            }
                                        } else {
                                            RenalProjectInfoDO renalProjectInfoDO2 = new RenalProjectInfoDO();
                                            renalProjectInfoDO2.setJsonValue(formattedNumber);
                                            renalProjectInfoDO2.setProjectType(renalProjectDO.getNumber() + 101);
                                            renalProjectInfoDO2.setJsonKey(97L);
                                            renalProjectInfoDO2.setUpdater("1");
                                            renalProjectInfoDO2.setUpdateTime(new Date());
                                            renalProjectInfoDO2.setCreator("1");
                                            renalProjectInfoDO2.setCheckTime(renalProjectDO.getLastCheckTime());
                                            renalProjectInfoDO2.setCreateTime(new Date());
                                            renalProjectInfoDO2.setProjectId(renalProjectDO.getId());
                                            renalProjectInfoDO2.setDeleted(false);
                                            renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO2);
                                            renalProjectDO.setLastCheckTime(sgnProject.getLastCheckTime());
                                            renalProjectDO.setNumber(renalProjectDO.getNumber() + 1);
                                            renalProjectMapper.saveOrUpdate(renalProjectDO);
                                        }

                                    } else {
                                        RenalProjectInfoDO renalProjectInfoDO2 = new RenalProjectInfoDO();
                                        renalProjectInfoDO2.setJsonValue(formattedNumber);
                                        renalProjectInfoDO2.setProjectType(renalProjectDO.getNumber() + 101);
                                        renalProjectInfoDO2.setJsonKey(97L);
                                        renalProjectInfoDO2.setUpdater("1");
                                        renalProjectInfoDO2.setUpdateTime(new Date());
                                        renalProjectInfoDO2.setCreator("1");
                                        renalProjectInfoDO2.setCheckTime(sgnProject.getLastCheckTime());
                                        renalProjectInfoDO2.setCreateTime(new Date());
                                        renalProjectInfoDO2.setProjectId(renalProjectDO.getId());
                                        renalProjectInfoDO2.setDeleted(false);
                                        renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO2);
                                        renalProjectDO.setNumber(renalProjectDO.getNumber() + 1);
                                        renalProjectDO.setLastCheckTime(sgnProject.getLastCheckTime());
                                        renalProjectMapper.saveOrUpdate(renalProjectDO);
                                    }
                                }

                            }

                        }

                    }
                }

                for (PatientDO patientDO : patientDOS) {
                    List<RenalProjectInfoDO> sgnThisResultList = getExamUrr(patientDO.getId(), startTime, endTime, "734");
                    if (!CollectionUtils.isEmpty(sgnThisResultList)) {
                        RenalProjectInfoDO renalProjectInfoDO1 = sgnThisResultList.get(sgnThisResultList.size() - 1);

                        List<RenalProjectInfoDO> sgnResultList = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                                .eq(RenalProjectInfoDO::getProjectId, renalProjectInfoDO1.getProjectId())
                                .eq(RenalProjectInfoDO::getProjectType, renalProjectInfoDO1.getProjectType()));
                        if (!CollectionUtils.isEmpty(sgnResultList)) {
                            RenalProjectDO sgnProject = getRenalProject(patientDO.getId(), startTime, endTime, "734");
                            // 透析前
                            double before = 0;
                            double after = 0;
                            HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(new LambdaQueryWrapperX<HemodialysisManagerDO>()
                                    .eq(HemodialysisManagerDO::getPatientId, patientDO.getId())
                                    .eq(HemodialysisManagerDO::getHemodialysisTime, sgnProject.getLastCheckTime())
                                    .eq(HemodialysisManagerDO::getDeptId, patientDO.getDeptId())
                                    .eq(HemodialysisManagerDO::getDeleted, false)
                                    .last("limit 1"));

                            if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO)) {
                                //double dryWeight = Double.parseDouble(hemodialysisManagerDO.getDryWeight());
                                String treatmentHour = hemodialysisManagerDO.getTreatmentHour();
                                String treatmentMin = hemodialysisManagerDO.getTreatmentMin();
                                if (StringUtils.isEmpty(treatmentMin)) {
                                    treatmentMin = "0";
                                }
                                for (RenalProjectInfoDO renalProjectInfoDO : sgnResultList) {
                                    if (Long.valueOf(57).equals(renalProjectInfoDO.getJsonKey()) && !renalProjectInfoDO.getJsonValue().contains("<")) {
                                        if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue())) {
                                            before = Double.parseDouble(renalProjectInfoDO.getJsonValue().replaceAll("\\s+", ""));
                                        }
                                    }
                                    if (Long.valueOf(58).equals(renalProjectInfoDO.getJsonKey())) {
                                        if (StringUtils.isNotEmpty(renalProjectInfoDO.getJsonValue()) && !renalProjectInfoDO.getJsonValue().contains("<")) {
                                            after = Double.parseDouble(renalProjectInfoDO.getJsonValue().replaceAll("\\s+", ""));
                                        }
                                    }
                                }
                                if (before > 0 && after > 0) {
                                    try {
                                        Double result = Double.parseDouble(treatmentHour) + Double.parseDouble(treatmentMin) / 60;
                                        Double resultNumber = Double.valueOf(String.format("%.2f", result));
                                        Double dialyzeAfterWeight = Double.valueOf(hemodialysisManagerDO.getDialyzeAfterWeight());
                                        Boolean beforeFlag = true;
                                        Boolean afterFlag = true;
                                        Boolean timeFlag = true;
                                        Boolean afterWeightFlag = true;
                                        Boolean capacityFlag = true;
                                        Boolean ktvFlag = true;

                                        Double actualUltrafiltrationCapacity = Double.valueOf(hemodialysisManagerDO.getActualUltrafiltrationCapacity()) / 1000;

                                        double ktv = (-Math.log(after / before - 0.008 * resultNumber) + (4 - (3.5 * (after / before))) * (actualUltrafiltrationCapacity / dialyzeAfterWeight));
                                        //double ktv = (-Math.log(after / before - (actualUltrafiltrationCapacity / dialyzeAfterWeight)) + ((5 / resultNumber) * (after / before)));

                                        String format = String.format("%.2f", ktv);

// 查询ktv
                                        RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                                                .eq(RenalProjectDO::getPatientId, patientDO.getId())
                                                .eq(RenalProjectDO::getDeptId, patientDO.getDeptId())
                                                //.eq(RenalProjectDO::getLastCheckTime, sgnProject.getLastCheckTime())
                                                .eq(RenalProjectDO::getDictId, 738L));
                                        List<RenalProjectInfoDO> renalProjectInfoDOList = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                                                .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                                                .eq(RenalProjectInfoDO::getCheckTime, renalProjectDO.getLastCheckTime()));
                                        RenalProjectInfoDO urrProjectInfoDO = renalProjectInfoMapper.selectOne(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                                                .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                                                .eq(RenalProjectInfoDO::getCheckTime, renalProjectDO.getLastCheckTime())
                                                .eq(RenalProjectInfoDO::getJsonKey, 97L)
                                                .last("limit 1"));
                                        if (!CollectionUtils.isEmpty(renalProjectInfoDOList)) {
                                            for (RenalProjectInfoDO renalProjectInfoDO : renalProjectInfoDOList) {
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                if (Long.valueOf(92).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(String.format("%.2f", before));

                                                    //
                                                    beforeFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }
                                                if (Long.valueOf(93).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(String.format("%.2f", after));
                                                    //
                                                    afterFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }
                                                if (Long.valueOf(94).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(String.format("%.1f", resultNumber));
                                                    //
                                                    timeFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }
                                                if (Long.valueOf(95).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(String.format("%.1f", actualUltrafiltrationCapacity));
                                                    //
                                                    capacityFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }
                                                if (Long.valueOf(96).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(String.format("%.1f", dialyzeAfterWeight));
                                                    //
                                                    afterWeightFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }
                                                if (Long.valueOf(98).equals(renalProjectInfoDO.getJsonKey())) {
                                                    renalProjectInfoDO.setJsonValue(format);
                                                    //
                                                    ktvFlag = false;
                                                    renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                                }

                                            }


                                            if (beforeFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 92L, before);
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }
                                            if (afterFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 93L, after);
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }

                                            if (timeFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 94L, resultNumber);
                                                renalProjectInfoDO.setJsonValue(String.format("%.1f", resultNumber));
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }
                                            if (capacityFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 95L, actualUltrafiltrationCapacity);
                                                renalProjectInfoDO.setJsonValue(String.format("%.1f", actualUltrafiltrationCapacity));
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }

                                            if (afterWeightFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 96L, dialyzeAfterWeight);
                                                renalProjectInfoDO.setJsonValue(String.format("%.1f", dialyzeAfterWeight));
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }
                                            if (ktvFlag) {

                                                RenalProjectInfoDO renalProjectInfoDO = getRenalProjectInfo(renalProjectDO, 98L, ktv);
                                                renalProjectInfoDO.setProjectType(urrProjectInfoDO.getProjectType());
                                                renalProjectInfoMapper.saveOrUpdate(renalProjectInfoDO);
                                            }

                                        }
                                    } catch (Exception e) {

                                    }
                                }
                            }

                        }

                    }
                }
            }catch (Exception e) {

            }
        }



    }
    @Scheduled(cron = "0 0/1 * * * ?")
    public void updatePatientType(){
        patientMapper.updatePatientType();
    }
    //@Scheduled(cron = "0 0/1 * * * ?  ")
    @Override
    public void updateSourceCourseRecord() {
//        System.out.println("开始执行：");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse("2025-03-01");
        } catch (ParseException e) {
//            System.out.println("错误信息1：" + e.getMessage());
        }
        List<CourseRecordDO> courseRecordDOS1 = courseRecordMapper.selectSourceCourseRecord();
        if(CollectionUtils.isEmpty(courseRecordDOS1)){
            return;
        }
        List<CourseRecordDO> courseRecordDOS2 = null;
        try {
            courseRecordDOS2 = courseRecordMapper.selectList(
                    new LambdaQueryWrapper<CourseRecordDO>()
                            .in(CourseRecordDO::getPatientId,courseRecordDOS1.stream()
                                    .map(item->item.getPatientId())
                                    .collect(Collectors.toList()))
                            .ge(CourseRecordDO :: getStartTime,sdf.parse("2025-01-01"))
                            .like(CourseRecordDO :: getRecordName,"%季度小结%")
                            .eq(CourseRecordDO::getDeleted,0));
        } catch (ParseException e) {
//            System.out.println("错误信息2：" + e.getMessage());
        }
        if(CollectionUtils.isEmpty(courseRecordDOS2)){
            return;
        }
        courseRecordDOS1 = courseRecordDOS1.stream().map(item->{
            item.setIsCompleted(1);
            return item;
        }).collect(Collectors.toList());
        courseRecordMapper.updateBatch(courseRecordDOS1);
//        try {
            Map<Integer,Date> map = new HashMap<>();
            for(int i = 0;!CollectionUtils.isEmpty(courseRecordDOS1) && i < courseRecordDOS1.size();i++){
                CourseRecordDO courseRecordDO = courseRecordDOS1.get(i);
                String targetHtml = courseRecordDO.getContent();
                for(int j = 0;!CollectionUtils.isEmpty(courseRecordDOS2) && j < courseRecordDOS2.size();j++){
                    try{
                        if(courseRecordDO.getPatientId().equals(courseRecordDOS2.get(j).getPatientId())){
                            Date date1 = map.getOrDefault(courseRecordDO.getPatientId(),courseRecordDOS2.get(j).getStartTime());
                            if(courseRecordDOS2.get(j).getStartTime().compareTo(date1) >= 0 &&
                                    courseRecordDOS2.get(j).getStartTime().compareTo(courseRecordDO.getStartTime()) < 0){

                                String sourceHtml = courseRecordDOS2.get(j).getContent();

                                for(int k = HtmlTableUtils.START_ROW_INDEX;k <= HtmlTableUtils.END_ROW_INDEX;k++ ){
                                    try{
                                        String cellContent = HtmlTableUtils.extractTableCell(sourceHtml, HtmlTableUtils.TABLE_INDEX, k, HtmlTableUtils.SOURCE_COL_INDEX);
                                        if(StrUtil.isNotEmpty(cellContent)){
                                            targetHtml = HtmlTableUtils.modifyTableCell(targetHtml, HtmlTableUtils.TABLE_INDEX, k, HtmlTableUtils.TARGET_COL_INDEX, cellContent);
                                        }
                                    }catch (Exception e){
//                                        System.out.println("错误信息3：" + e.getMessage());
                                    }
                                }
                                if(targetHtml.equals(courseRecordDO.getContent())){
                                    continue;
                                }
                                map.put(courseRecordDO.getPatientId(),courseRecordDOS2.get(j).getStartTime());
                            }
//
                        }
                    }catch (Exception e){
//                        System.out.println("错误信息4：" + e.getMessage());
                    }

                }
                if(StrUtil.isNotEmpty(targetHtml)){
                    CourseRecordDO courseRecord = CourseRecordDO.builder().content(targetHtml).id(courseRecordDO.getId())
                            .isCompleted(2).build();
                    try{

                        int num = courseRecordMapper.updateRecord(courseRecord)
                                ;

                    }catch (Exception e){

                    }
                }

            }



//        } catch (Exception e) {
//
//        }
    }

    @Override
    public void updateBatch(CourseRecordBatch courseRecordBatch) {
        if(courseRecordBatch != null && !CollectionUtils.isEmpty(courseRecordBatch.getIds())){
            courseRecordMapper.batchUpdateStatusByIds("1",courseRecordBatch.getIds());
        }
    }

    private RenalProjectInfoDO getRenalProjectInfo(RenalProjectDO renalProjectDO, long jsonKey, double before) {
        RenalProjectInfoDO renalProjectInfoDO = new RenalProjectInfoDO();
        renalProjectInfoDO.setJsonValue(String.format("%.2f", before));
        renalProjectInfoDO.setProjectType(renalProjectDO.getNumber());
        renalProjectInfoDO.setJsonKey(jsonKey);
        renalProjectInfoDO.setUpdater("1");
        renalProjectInfoDO.setUpdateTime(new Date());
        renalProjectInfoDO.setCreator("1");
        renalProjectInfoDO.setCheckTime(renalProjectDO.getLastCheckTime());
        renalProjectInfoDO.setCreateTime(new Date());
        renalProjectInfoDO.setProjectId(renalProjectDO.getId());
        renalProjectInfoDO.setDeleted(false);
        return renalProjectInfoDO;
    }

    private List<RenalProjectInfoDO> getExamUrr(Long id, Date startTime, Date endTime, String s) {

        RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                .eq(RenalProjectDO::getPatientId, id)
                .eq(RenalProjectDO::getDictId, s)
                .between(RenalProjectDO::getLastCheckTime,startTime,endTime)
                .last("limit 1"));

        if (StringUtils.isNotNull(renalProjectDO)) {
            List<RenalProjectInfoDO> renalProjectInfoDOS = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                    .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                    .eq(RenalProjectInfoDO::getJsonKey,58)
                    .between(RenalProjectInfoDO::getCheckTime,startTime,endTime)
                    .isNotNull(RenalProjectInfoDO::getJsonValue)
                    .orderByAsc(RenalProjectInfoDO::getProjectType));
            return renalProjectInfoDOS;
        }
        return null;
    }

    private RenalProjectDO getRenalProject(Long id, Date startTime, Date endTime, String s) {

        RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                .eq(RenalProjectDO::getPatientId, id)
                .eq(RenalProjectDO::getDictId, s)
                .last("limit 1"));
        return renalProjectDO;
    }

    // 获取计算值
    private BigDecimal getResult(List<HemodialysisManagerDO> hemodialysisManagerDOList,int type) {
        BigDecimal result = BigDecimal.ZERO;
        switch (type) {
            case 1: // 干体重
                List<HemodialysisManagerDO> dryWeightList = hemodialysisManagerDOList.stream().filter(hemodialysisManagerDO -> StringUtils.isNotEmpty(hemodialysisManagerDO.getDryWeight())).collect(Collectors.toList());
                //干体重上次	lastWeight
                BigDecimal sumDryWeight =BigDecimal.ZERO;
                for (HemodialysisManagerDO hemodialysisManagerDO : dryWeightList) {
                    sumDryWeight = sumDryWeight.add(new BigDecimal(hemodialysisManagerDO.getDryWeight()));

                }
                result = sumDryWeight.divide(BigDecimal.valueOf(dryWeightList.size()), 1, BigDecimal.ROUND_HALF_UP);
                break;
            case 2: //体重增长
                List<HemodialysisManagerDO> lastCapacityList = hemodialysisManagerDOList.stream().filter(hemodialysisManagerDO -> StringUtils.isNotEmpty(hemodialysisManagerDO.getPrescriptionEhydratedLevel())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(lastCapacityList)) {
                    BigDecimal lastCapacity = BigDecimal.ZERO;
                    for (HemodialysisManagerDO hemodialysisManagerDO : lastCapacityList) {
                        lastCapacity= lastCapacity.add(new BigDecimal(Double.valueOf(hemodialysisManagerDO.getPrescriptionEhydratedLevel())));
                    }
                    result = lastCapacity.divide(BigDecimal.valueOf(lastCapacityList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(1000)).setScale(0,BigDecimal.ROUND_HALF_UP);
                }
                 break;
            case 3: //透前收缩压
                List<HemodialysisManagerDO> bpOneList = hemodialysisManagerDOList.stream().filter(hemodialysisManagerDO -> StringUtils.isNotEmpty(hemodialysisManagerDO.getBpNoOne()) && !hemodialysisManagerDO.getBpNoOne().contains("拒测") && !hemodialysisManagerDO.getBpNoOne().contains("测不出")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(bpOneList)) {
                    BigDecimal lastCapacity = BigDecimal.ZERO;
                    for (HemodialysisManagerDO hemodialysisManagerDO : bpOneList) {
                        lastCapacity = lastCapacity.add(new BigDecimal(hemodialysisManagerDO.getBpNoOne()));
                    }
                    result = lastCapacity.divide(BigDecimal.valueOf(bpOneList.size()), 0, BigDecimal.ROUND_HALF_UP);
                }
                break;
            case 4: //透前舒张压
                List<HemodialysisManagerDO> bpTwoList = hemodialysisManagerDOList.stream().filter(hemodialysisManagerDO -> StringUtils.isNotEmpty(hemodialysisManagerDO.getBpNoTwo()) && !hemodialysisManagerDO.getBpNoTwo().contains("拒测") && !hemodialysisManagerDO.getBpNoTwo().contains("测不出")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(bpTwoList)) {
                    BigDecimal lastCapacity = BigDecimal.ZERO;
                    for (HemodialysisManagerDO hemodialysisManagerDO : bpTwoList) {
                        lastCapacity = lastCapacity.add(new BigDecimal(hemodialysisManagerDO.getBpNoTwo()));
                    }
                    result = lastCapacity.divide(BigDecimal.valueOf(bpTwoList.size()), 0, BigDecimal.ROUND_HALF_UP);
                }
                break;
            default:
        }

        return result;
    }

    private List<RenalProjectInfoDO> getExamResult(Long patientId,Date startTime,Date endTime,String type) {
        RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                .eq(RenalProjectDO::getPatientId, patientId)
                .eq(RenalProjectDO::getDictId, type)
                .last("limit 1"));

        if (StringUtils.isNotNull(renalProjectDO)) {
            List<RenalProjectInfoDO> renalProjectInfoDOS = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                    .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                    .between(RenalProjectInfoDO::getCheckTime, startTime, endTime)
                    .orderByAsc(RenalProjectInfoDO::getProjectType));
            return renalProjectInfoDOS;
        }
        return null;
    }

    private List<RenalProjectInfoDO> getExamResultLast(Long patientId,Date startTime,Date endTime,String type) {
        RenalProjectDO renalProjectDO = renalProjectMapper.selectOne(new LambdaQueryWrapperX<RenalProjectDO>()
                .eq(RenalProjectDO::getPatientId, patientId)
                .eq(RenalProjectDO::getDictId, type)
                .between(RenalProjectDO::getLastCheckTime,startTime,endTime)
                .last("limit 1"));

        if (StringUtils.isNotNull(renalProjectDO)) {
            List<RenalProjectInfoDO> renalProjectInfoDOS = renalProjectInfoMapper.selectList(new LambdaQueryWrapperX<RenalProjectInfoDO>()
                    .eq(RenalProjectInfoDO::getProjectId, renalProjectDO.getId())
                    .eq(RenalProjectInfoDO::getProjectType, renalProjectDO.getNumber())
                    .orderByAsc(RenalProjectInfoDO::getProjectType));
            return renalProjectInfoDOS;
        }
        return null;
    }

    private void setPatientInfo(CourseRecordDO courseRecord) {
        if(StringUtils.isEmpty(courseRecord.getPatientName())){
            PatientDO patientDO = patientMapper.selectById(courseRecord.getPatientId());
            if(patientDO != null){
                courseRecord.setPatientName(patientDO.getName());
                courseRecord.setPatientNickName(patientDO.getSpellName());
            }
        }
    }

}
