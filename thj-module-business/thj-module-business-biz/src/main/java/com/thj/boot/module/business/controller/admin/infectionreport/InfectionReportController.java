package com.thj.boot.module.business.controller.admin.infectionreport;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.infectionreport.InfectionReportConvert;
import com.thj.boot.module.business.dal.datado.infectionreport.InfectionReportDO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportCreateReqVO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportPageReqVO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportRespVO;
import com.thj.boot.module.business.pojo.infectionreport.vo.InfectionReportUpdateReqVO;
import com.thj.boot.module.business.service.infectionreport.InfectionReportService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/infection-report")
@Validated
public class InfectionReportController {

    @Resource
    private InfectionReportService infectionReportService;

    @PostMapping("/create")
    public CommonResult<Long> createInfectionReport( @RequestBody InfectionReportCreateReqVO createReqVO) {
        return success(infectionReportService.createInfectionReport(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateInfectionReport( @RequestBody InfectionReportUpdateReqVO updateReqVO) {
        infectionReportService.updateInfectionReport(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteInfectionReport(@RequestParam("id") Long id) {
        infectionReportService.deleteInfectionReport(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<InfectionReportRespVO> getInfectionReport(@RequestParam("id") Long id) {
        InfectionReportDO infectionReport = infectionReportService.getInfectionReport(id);
        return success(InfectionReportConvert.INSTANCE.convert(infectionReport));
    }

    @GetMapping("/list")
    public CommonResult<List<InfectionReportRespVO>> getInfectionReportList(InfectionReportCreateReqVO createReqVO) {
        List<InfectionReportDO> list = infectionReportService.getInfectionReportList(createReqVO);
        return success(InfectionReportConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<InfectionReportRespVO>> getInfectionReportPage(@RequestBody InfectionReportPageReqVO pageVO) {
        PageResult<InfectionReportDO> pageResult = infectionReportService.getInfectionReportPage(pageVO);
        return success(InfectionReportConvert.INSTANCE.convertPage(pageResult));
    }


}
