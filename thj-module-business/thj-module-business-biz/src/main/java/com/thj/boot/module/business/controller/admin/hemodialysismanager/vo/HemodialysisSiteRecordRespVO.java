package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceRespVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/9 9:42
 * @description
 */
@Data
public class HemodialysisSiteRecordRespVO implements Serializable {


    /**
     * 日期
     */
    private Date hemodialysisTime;

    /**
     * 位点
     */
    private String site;

    /**
     * 内瘘
     */
    private String internalFistula;

    /**
     * 导管
     */
    private String conduit;



}
