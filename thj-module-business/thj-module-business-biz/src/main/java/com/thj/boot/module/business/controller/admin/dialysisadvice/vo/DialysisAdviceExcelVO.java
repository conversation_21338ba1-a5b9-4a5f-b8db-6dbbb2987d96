package com.thj.boot.module.business.controller.admin.dialysisadvice.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/21 12:04
 * @description
 */
@Data
public class DialysisAdviceExcelVO implements Serializable {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String patientName;

    /**
     * 医嘱名称
     */
    @ExcelProperty(value = "医嘱名称")
    private String adviceName;

    /**
     * 药品规格
     */
    @ExcelProperty(value = "药品规格")
    private String specification;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String fspecUnit;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private String adviceNameCount;

}
