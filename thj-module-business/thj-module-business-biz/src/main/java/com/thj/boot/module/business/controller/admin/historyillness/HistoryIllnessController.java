package com.thj.boot.module.business.controller.admin.historyillness;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.pojo.historyillness.HistoryIllnessCreateReqVO;
import com.thj.boot.module.business.pojo.historyillness.HistoryIllnessRespVO;
import com.thj.boot.module.business.service.historyillness.HistoryIllnessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/3/22 14:41
 * @description 现病史模版
 */
@RestController
@RequestMapping("/business/his-illness")
public class HistoryIllnessController {

    @Resource
    private HistoryIllnessService historyIllnessService;


    /**
     * 新增
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createHistoryIllness(@RequestBody HistoryIllnessCreateReqVO createReqVO) {
        historyIllnessService.createHistoryIllness(createReqVO);
        return success(true);
    }


    /**
     * 修改
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateHistoryIllness(@RequestBody HistoryIllnessCreateReqVO createReqVO) {
        historyIllnessService.updateHistoryIllness(createReqVO);
        return success(true);
    }


    /**
     * 不分页
     *
     * @param createReqVO
     * @return
     */
    @GetMapping("/list")
    public CommonResult<List<HistoryIllnessRespVO>> getHistoryIllnessList(HistoryIllnessCreateReqVO createReqVO) {
        return success(historyIllnessService.getHistoryIllnessList(createReqVO));
    }


    /**
     * 删除
     *
     * @param createReqVO
     * @return
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteHistoryIllnessList(Long id) {
        historyIllnessService.deleteHistoryIllnessList(id);
        return success(true);
    }

}
