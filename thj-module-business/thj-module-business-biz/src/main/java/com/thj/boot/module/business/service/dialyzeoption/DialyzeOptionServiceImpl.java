package com.thj.boot.module.business.service.dialyzeoption;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.convert.dialysisprotocol.DialysisProtocolConvert;
import com.thj.boot.module.business.convert.dialyzeoption.DialyzeOptionConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.contradict.ContradictDO;
import com.thj.boot.module.business.dal.datado.contradictadvice.ContradictAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.dal.datado.mottohard.MottoHardDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.contradict.ContradictMapper;
import com.thj.boot.module.business.dal.mapper.contradictadvice.ContradictAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysisprotocol.DialysisProtocolMapper;
import com.thj.boot.module.business.dal.mapper.dialyzeoption.DialyzeOptionMapper;
import com.thj.boot.module.business.dal.mapper.mottohard.MottoHardMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.pojo.dialysisprotocol.vo.DialysisProtocolCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionCreateReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionPageReqVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionUpdateReqVO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 透析方案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DialyzeOptionServiceImpl extends ServiceImpl<DialyzeOptionMapper, DialyzeOptionDO> implements DialyzeOptionService {

    @Resource
    private DialyzeOptionMapper dialyzeOptionMapper;

    @Resource
    private MottoHardMapper hardMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private BloodRoadMapper bloodRoadMapper;

    @Resource
    private DialysisProtocolMapper dialysisProtocolMapper;

    @Resource
    private ContradictMapper contradictMapper;

    @Resource
    private ContradictAdviceMapper contradictAdviceMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Autowired
    private AdminUserApi adminUserApi;

    @Override
    public Long createDialyzeOption(DialyzeOptionCreateReqVO createReqVO) {
        MottoHardDO mottoHardDO = hardMapper.selectOne(MottoHardDO::getDialyzeId, createReqVO.getDialyzeDictValue());
        if (mottoHardDO == null) {
            throw new ServiceException(GlobalErrorCodeConstants.NO_EMPTY_DAILIZE);
        }
        createReqVO.setContent(mottoHardDO.getContent());
        createReqVO.setAnticoagulant(mottoHardDO.getAnticoagulant());
        if (createReqVO.getPid() != null && 0 != createReqVO.getPid()) {
            DialyzeOptionDO dialyzeOptionDO = dialyzeOptionMapper.selectById(createReqVO.getPid());
            if (!org.springframework.util.StringUtils.isEmpty(dialyzeOptionDO)) {
                createReqVO.setNumberDictValue(dialyzeOptionDO.getNumberDictValue());
            }
        } else {
            //检测透析模式唯一性
            checkDialyzeValue(createReqVO);
            //默认处方名和透析模式一致
            DictDataRespDTO dialyzeWay = dictDataApi.getDictData("dialyze_way", createReqVO.getDialyzeDictValue());
            createReqVO.setPrescription(dialyzeWay == null ? null : dialyzeWay.getLabel());
        }
        DialyzeOptionDO dialyzeOption = DialyzeOptionConvert.INSTANCE.convert(createReqVO);
        // 创建透析方案
        DialysisProtocolDO dialysisProtocolDO = new DialysisProtocolDO();
        dialysisProtocolDO.setDialyzeId(Long.valueOf(dialyzeOption.getDialyzeDictValue()));
        dialysisProtocolDO.setProtocolType(1);
        dialysisProtocolDO.setPatientId(dialyzeOption.getPatientId());

        dialysisProtocolMapper.insert(dialysisProtocolDO);
        dialyzeOption.setProtocolId(dialysisProtocolDO.getId());
        dialyzeOptionMapper.insert(dialyzeOption);
        // 返回

        dialysisProtocolDO.setPatientDialyzeId(dialyzeOption.getId());
        dialysisProtocolMapper.updateById(dialysisProtocolDO);
        return dialyzeOption.getId();
    }

    private void checkDialyzeValue(DialyzeOptionCreateReqVO createReqVO) {
        Long count = dialyzeOptionMapper.selectCount(DialyzeOptionDO::getDialyzeDictValue, createReqVO.getDialyzeDictValue(), DialyzeOptionDO::getPatientId, createReqVO.getPatientId());
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.DIALYZE_NAME_EXITS);
        }
    }

    @Override
    public void updateDialyzeOption(DialyzeOptionUpdateReqVO updateReqVO) {
        updateReqVO.setDialyzeDictValue(null);
        DialyzeOptionDO updateObj = DialyzeOptionConvert.INSTANCE.convert(updateReqVO);
        dialyzeOptionMapper.updateById(updateObj);
    }

    @Override
    public void deleteDialyzeOption(Long id) {
        // 删除
        dialyzeOptionMapper.deleteById(id);
        dialyzeOptionMapper.delete(new LambdaUpdateWrapper<DialyzeOptionDO>().eq(DialyzeOptionDO::getPid, id));
    }

    @Override
    public DialyzeOptionRespVO getDialyzeOption(Long id, String child) {
        DialyzeOptionDO dialyzeOptionDO = dialyzeOptionMapper.selectById(id);
        DialyzeOptionRespVO dialyzeOptionRespVO = DialyzeOptionConvert.INSTANCE.convert(dialyzeOptionDO);
        if (dialyzeOptionRespVO != null && dialyzeOptionRespVO.getPid() != 0 || dialyzeOptionRespVO != null && StrUtil.isNotEmpty(child)) {
            dialyzeOptionRespVO.setChildFrequency(dialyzeOptionRespVO.getFrequencyDictValue() + dialyzeOptionRespVO.getNumberDictValue());
        }
        if (!org.springframework.util.StringUtils.isEmpty(dialyzeOptionRespVO) && !org.springframework.util.StringUtils.isEmpty(dialyzeOptionRespVO.getUserId())) {
            UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(dialyzeOptionRespVO.getUserId());
            if (!org.springframework.util.StringUtils.isEmpty(adminUserInfo)) {
                dialyzeOptionRespVO.setDoctorName(adminUserInfo.getNickname());
            }
        }
        return dialyzeOptionRespVO;
    }

    @Override
    public List<DialyzeOptionDO> getDialyzeOptionList(Collection<Long> ids) {
        return dialyzeOptionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DialyzeOptionRespVO> getDialyzeOptionPage(DialyzeOptionPageReqVO pageReqVO) {
        PageResult<DialyzeOptionDO> dialyzeOptionDOPageResult = dialyzeOptionMapper.selectPage(pageReqVO);
        PageResult<DialyzeOptionRespVO> dialyzeOptionRespVOPageResult = DialyzeOptionConvert.INSTANCE.convertPage(dialyzeOptionDOPageResult);
        if (CollectionUtil.isNotEmpty(dialyzeOptionRespVOPageResult.getList())) {
            List<DialyzeOptionRespVO> collect = dialyzeOptionRespVOPageResult.getList().stream().peek(dialyzeOptionRespVO -> {
                List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPid, dialyzeOptionRespVO.getId());
                dialyzeOptionRespVO.setChildren(dialyzeOptionDOS);
            }).collect(Collectors.toList());
            dialyzeOptionRespVOPageResult.setList(collect);
        }
        return dialyzeOptionRespVOPageResult;
    }

    @Override
    public List<DialyzeOptionRespVO> getDialyzeOptionList(DialyzeOptionCreateReqVO createReqVO) {
        List<DialyzeOptionDO> dialyzeOptionDOList = dialyzeOptionMapper.selectList(createReqVO);
        List<DialyzeOptionRespVO> dialyzeOptionRespVOS = DialyzeOptionConvert.INSTANCE.convertList(dialyzeOptionDOList);
        if (CollectionUtil.isNotEmpty(dialyzeOptionRespVOS)) {
            dialyzeOptionRespVOS = dialyzeOptionRespVOS.stream().filter(dialyzeOptionRespVO -> 0 == dialyzeOptionRespVO.getPid()).collect(Collectors.toList());
            dialyzeOptionRespVOS = dialyzeOptionRespVOS.stream().peek(dialyzeOptionRespVO -> {
                if (!org.springframework.util.StringUtils.isEmpty(dialyzeOptionRespVO.getUserId())) {
                    UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(dialyzeOptionRespVO.getUserId());
                    if (!org.springframework.util.StringUtils.isEmpty(adminUserInfo)) {
                        dialyzeOptionRespVO.setDoctorName(adminUserInfo.getNickname());
                    }
                }

                //按周
                if ("1".equals(dialyzeOptionRespVO.getFrequencyDictValue())) {
                    DictDataRespDTO zhouqi = dictDataApi.getDictData("zhouqi", dialyzeOptionRespVO.getCycleDictValue());
                    DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionRespVO.getNumberDictValue());
                    dialyzeOptionRespVO.setFrequencyName(zhouqi.getLabel() + cishu.getLabel());
                } else if ("2".equals(dialyzeOptionRespVO.getFrequencyDictValue())) { //按日
                    DictDataRespDTO zhouqi = dictDataApi.getDictData("cycle_day", dialyzeOptionRespVO.getCycleDictValue());
                    DictDataRespDTO cishu = dictDataApi.getDictData("cishu", dialyzeOptionRespVO.getNumberDictValue());
                    StringBuilder sb = new StringBuilder();
                    sb.append(zhouqi == null ? "" : zhouqi.getLabel());
                    sb.append(cishu == null ? "" : cishu.getLabel());
                    dialyzeOptionRespVO.setFrequencyName(sb.toString());
                }
                List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPid, dialyzeOptionRespVO.getId());
                if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
                    dialyzeOptionDOS = dialyzeOptionDOS.stream().peek(dialyzeOptionDO -> {
                        dialyzeOptionDO.setDialyzeDictValue(dialyzeOptionRespVO.getDialyzeDictValue());
                    }).collect(Collectors.toList());
                }
                dialyzeOptionRespVO.setChildren(dialyzeOptionDOS);
            }).collect(Collectors.toList());
        }
        return dialyzeOptionRespVOS;
    }

    @Override
    public DialyzeOptionRespVO getDialyzeInfo(String dialyzeDictValue, Long patientId, Date hemodialysisTime) {
        Date time = null;
        if (org.springframework.util.StringUtils.isEmpty(hemodialysisTime)) {
            // 查询今日排班
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY,0);
            calendar.set(Calendar.MINUTE,0);
            calendar.set(Calendar.SECOND,0);
            calendar.set(Calendar.MILLISECOND,0);
            time = calendar.getTime();
        }else {
            time = hemodialysisTime;
        }

        DialyzeOptionDO dialyzeOptionDO = null;
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(ArrangeClassesDO::getClassesTime, time, ArrangeClassesDO::getPatientId, patientId, ArrangeClassesDO::getTempType, 0);
        if (!org.springframework.util.StringUtils.isEmpty(arrangeClassesDO) && !org.springframework.util.StringUtils.isEmpty(arrangeClassesDO.getProtocolId())) {
            DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectById(arrangeClassesDO.getProtocolId());
            if (!org.springframework.util.StringUtils.isEmpty(dialysisProtocolDO) && !org.springframework.util.StringUtils.isEmpty(dialysisProtocolDO.getPatientDialyzeId())) {
                dialyzeOptionDO = dialyzeOptionMapper.selectById(dialysisProtocolDO.getPatientDialyzeId());
            }else {
                dialyzeOptionDO = dialyzeOptionMapper.selectOne(DialyzeOptionDO::getPatientId, patientId, DialyzeOptionDO::getDialyzeDictValue, dialyzeDictValue,DialyzeOptionDO::getPid,0);
            }
        }else {
            dialyzeOptionDO = dialyzeOptionMapper.selectOne(DialyzeOptionDO::getPatientId, patientId, DialyzeOptionDO::getDialyzeDictValue, dialyzeDictValue,DialyzeOptionDO::getPid,0);
        }

        DialyzeOptionRespVO dialyzeOptionRespVO = DialyzeOptionConvert.INSTANCE.convert(dialyzeOptionDO);
        List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(BloodRoadDO::getPatientId, patientId);
        if (dialyzeOptionRespVO != null) {
            if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                List<BloodRoadDO> bloodRoadDOList = bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(bloodRoadDOList)) {
                    BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                    dialyzeOptionRespVO.setBloodPart(bloodRoadDO.getPart());
                    dialyzeOptionRespVO.setBloodType(bloodRoadDO.getType());
                }
            }
        } else {
            //获取默认到处方血管通路
            dialyzeOptionRespVO = new DialyzeOptionRespVO();
            if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                List<BloodRoadDO> bloodRoadDOList = bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(bloodRoadDOList)) {
                    BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                    dialyzeOptionRespVO.setBloodPart(bloodRoadDO.getPart());
                    dialyzeOptionRespVO.setBloodType(bloodRoadDO.getType());
                }
            }
        }
        return dialyzeOptionRespVO;
    }

    @Override
    public DialyzeOptionRespVO getDialyzeList(DialyzeOptionCreateReqVO createReqVO) {
        DialyzeOptionDO dialyzeOptionDOS = dialyzeOptionMapper.selectOne(DialyzeOptionDO::getDialyzeDictValue, createReqVO.getDialyzeDictValue()
                , DialyzeOptionDO::getPatientId, createReqVO.getPatientId(),DialyzeOptionDO::getPid,0);
        DialyzeOptionRespVO dialyzeOptionRespVO = DialyzeOptionConvert.INSTANCE.convert(dialyzeOptionDOS);
        List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(BloodRoadDO::getPatientId, createReqVO.getPatientId());
        if (dialyzeOptionRespVO != null) {
            //子级
            List<DialyzeOptionDO> dialyzeOptionDOList = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPid, dialyzeOptionRespVO.getId());
            dialyzeOptionRespVO.setChildren(dialyzeOptionDOList);
            //获取默认到处方血管通路
            if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                List<BloodRoadDO> bloodRoadDOList = bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(bloodRoadDOList)) {
                    BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                    dialyzeOptionRespVO.setBloodPart(bloodRoadDO.getPart());
                    dialyzeOptionRespVO.setBloodType(bloodRoadDO.getType());
                }
            }
        } else {
            dialyzeOptionRespVO = new DialyzeOptionRespVO();
            //获取默认到处方血管通路
            if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                List<BloodRoadDO> bloodRoadDOList = bloodRoadDOS.stream().filter(bloodRoadDO -> "0".equals(bloodRoadDO.getRecipe())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(bloodRoadDOList)) {
                    BloodRoadDO bloodRoadDO = bloodRoadDOList.stream().findFirst().get();
                    dialyzeOptionRespVO.setBloodPart(bloodRoadDO.getPart());
                    dialyzeOptionRespVO.setBloodType(bloodRoadDO.getType());
                }
            }
        }

        return dialyzeOptionRespVO;
    }


    @Override
    public void checkDialysisProtocol(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, idList);
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            List<Long> patientIds = dialyzeOptionDOS.stream().map(DialyzeOptionDO::getPatientId).collect(Collectors.toList());
            List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getId, patientIds);
            String patientNames = patientDOS.stream().map(PatientDO::getName).collect(Collectors.joining(","));
            throw new ServiceException(GlobalErrorCodeConstants.DIALYZE_OPTION_INFO, patientNames, patientDOS.size() + "");
        }
    }

    @Override
    public MottoHardDO dialysisInfo(String dialyzeDictValue) {
        return hardMapper.selectOne(MottoHardDO::getDialyzeId, dialyzeDictValue);
    }

    @Override
    public void batchSaveDialyze(DialyzeOptionCreateReqVO createReqVO) {

        newBatchSaveDialyze(createReqVO);

      /*  List<Long> patientIds = Arrays.stream(createReqVO.getPatientIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getId, patientIds);
        if (CollectionUtil.isNotEmpty(patientDOS)) {
            for (PatientDO patientDO : patientDOS) {
                for (DialyzeOptionCreateReqVO dialyzeOptionCreateReqVO : createReqVO.getDialyzeOptionCreateReqVOS()) {
                    DialyzeOptionDO dialyzeOption = DialyzeOptionConvert.INSTANCE.convert(dialyzeOptionCreateReqVO);
                    dialyzeOption.setPatientId(patientDO.getId());
                    dialyzeOption.setDialyzeNo(patientDO.getDialyzeNo());
                    dialyzeOption.setPatientName(patientDO.getName());
                    dialyzeOption.setPatientNickName(patientDO.getNickName());
                    dialyzeOption.setFirstDialyze(patientDO.getFirstReceiveTime());
                    dialyzeOptionMapper.insert(dialyzeOption);
                    if (CollectionUtil.isNotEmpty(dialyzeOptionCreateReqVO.getChildren())) {
                        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionCreateReqVO.getChildren().stream().peek(dialyzeOptionDO -> {
                            dialyzeOptionDO.setPid(dialyzeOption.getId());
                            dialyzeOption.setDialyzeDictValue(null);
                        }).collect(Collectors.toList());
                        dialyzeOptionMapper.insertBatch(dialyzeOptionDOS);
                    }
                }
            }
        }*/
    }

    private void newBatchSaveDialyze(DialyzeOptionCreateReqVO createReqVO) {
        List<Long> patientIds = Arrays.stream(createReqVO.getPatientIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getId, patientIds);
//        判断患者是否存在
        if(StringUtils.isNotNull(patientDOS)){
            patientDOS.forEach(p ->{
                dialyzeOption(createReqVO.getDialyzeOptionCreateReqVOS(),p);
            });
        }
    }

    /**
     * 处理多个透析模式数据
     */
    private void dialyzeOption(List<DialyzeOptionCreateReqVO> vos, PatientDO patientDO){
        vos.forEach(dialyzeOptionCreateReqVO -> {
            DialyzeOptionDO dialyzeOption = DialyzeOptionConvert.INSTANCE.convert(dialyzeOptionCreateReqVO);
            dialyzeOption.setId(null);
            dialyzeOption.setPatientId(patientDO.getId());
            dialyzeOption.setDialyzeNo(patientDO.getDialyzeNo());
            dialyzeOption.setPatientName(patientDO.getName());
            dialyzeOption.setPatientNickName(patientDO.getNickName());
            dialyzeOption.setFirstDialyze(patientDO.getFirstReceiveTime());
             dialyzeOptionMapper.insert(dialyzeOption);

            DialysisProtocolCreateReqVO contents = dialyzeOptionCreateReqVO.getContents();
            if(StringUtils.isNotNull(contents)){
                getContent(contents,dialyzeOption.getId(),dialyzeOptionCreateReqVO.getDialyzeDictValue(),patientDO.getId());
            }
            //子药
            List<DialyzeOptionDO> children = dialyzeOptionCreateReqVO.getChildren();
            children.forEach(dialyzeOption01 -> {
                dialyzeOption01.setId(null);
                dialyzeOption01.setPid(dialyzeOption.getId());
                dialyzeOption01.setPatientId(patientDO.getId());
                dialyzeOption01.setDialyzeNo(patientDO.getDialyzeNo());
                dialyzeOption01.setPatientName(patientDO.getName());
                dialyzeOption01.setPatientNickName(patientDO.getNickName());
                dialyzeOption01.setFirstDialyze(patientDO.getFirstReceiveTime());
                dialyzeOptionMapper.insert(dialyzeOption01);
                DialysisProtocolCreateReqVO contents1 = dialyzeOption01.getContents();
                if(StringUtils.isNotNull(contents1)){
                    getContent(contents1,dialyzeOption01.getId(),dialyzeOptionCreateReqVO.getDialyzeDictValue(),patientDO.getId());
                }
            });
        });
    }

    /**
     * 多个透析模式
     * @param contents
     */
    private void getContent(DialysisProtocolCreateReqVO contents,Long dialyzeOptionId,String dialyzeDictValue, Long patientId){
        DialysisProtocolDO dialysisProtocolDO = DialysisProtocolConvert.INSTANCE.convert(contents);
        dialysisProtocolDO.setId(null);
        dialysisProtocolDO.setProtocolType(1);
        dialysisProtocolDO.setDialyzeId(Long.valueOf(dialyzeDictValue));
        dialysisProtocolDO.setPatientDialyzeId(Long.valueOf(dialyzeOptionId));
        dialysisProtocolDO.setPatientId(patientId);
        dialysisProtocolMapper.insert(dialysisProtocolDO);
        List<ContradictDO> contradictDOS = contents.getContradictDOS();
        contradictDOS.forEach(contradictDO -> {
            contradictDO.setId(null);
            contradictMapper.insert(contradictDO);
        });
        //抗凝剂
        List<ContradictAdviceDO> contradictAdviceDOS = contents.getContradictAdviceDOS();
        contradictAdviceDOS.forEach(contradictAdviceDO -> {
            contradictAdviceDO.setId(null);
            contradictAdviceMapper.insert(contradictAdviceDO);
        });
    }

}
