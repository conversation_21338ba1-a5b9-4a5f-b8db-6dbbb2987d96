package com.thj.boot.module.business.dal.mapper.mottosimple;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.pojo.motto.vo.MottoCreateReqVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimpleCreateReqVO;
import com.thj.boot.module.business.pojo.mottosimple.vo.MottoSimplePageReqVO;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;
import java.util.List;

/**
 * 简单个性化 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MottoSimpleMapper extends BaseMapperX<MottoSimpleDO> {

    default PageResult<MottoSimpleDO> selectPage(MottoSimplePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MottoSimpleDO>()
                .eqIfPresent(MottoSimpleDO::getPid, reqVO.getPid())
                .eqIfPresent(MottoSimpleDO::getMottoId, reqVO.getMottoId())
                .likeIfPresent(MottoSimpleDO::getPname, reqVO.getPname())
                .eqIfPresent(MottoSimpleDO::getContent, reqVO.getContent())
                .eqIfPresent(MottoSimpleDO::getSort, reqVO.getSort())
                .eqIfPresent(MottoSimpleDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(MottoSimpleDO::getRemark, reqVO.getRemark())
                .orderByDesc(MottoSimpleDO::getId));
    }

    default List<MottoSimpleDO> selectList(MottoSimpleCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                .eqIfPresent(MottoSimpleDO::getPid, reqVO.getPid())
                .eqIfPresent(MottoSimpleDO::getMottoId, reqVO.getMottoId())
                .likeIfPresent(MottoSimpleDO::getPname, reqVO.getPname())
                .eqIfPresent(MottoSimpleDO::getContent, reqVO.getContent())
                .eqIfPresent(MottoSimpleDO::getSort, reqVO.getSort())
                .eqIfPresent(MottoSimpleDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(MottoSimpleDO::getRemark, reqVO.getRemark())
                .orderByDesc(MottoSimpleDO::getId));
    }

    default List<MottoSimpleDO> selectList(MottoCreateReqVO createReqVO) {
        return selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                .eqIfPresent(MottoSimpleDO::getMottoId, createReqVO.getId())
                .eq(createReqVO.getId() != null &&10 == createReqVO.getId() ,MottoSimpleDO::getDeptId, createReqVO.getDeptId())
                .inIfPresent(MottoSimpleDO::getMottoId, createReqVO.getIdList())
                .eq(createReqVO.getId() != null && createReqVO.getType() != null && 6 == createReqVO.getId() && 2 == createReqVO.getType(), MottoSimpleDO::getDeptId, createReqVO.getDeptId())
                .and(createReqVO.getId() != null && (7 == createReqVO.getId() || 19 == createReqVO.getId())
                                && StringUtils.isNotBlank(createReqVO.getDeptIds()),
                        wrap -> {
                            // 将传入的deptIds分割成列表
                            List<String> deptIdList = Arrays.asList(createReqVO.getDeptIds().split(","));
                            // 使用OR连接每个部门id的匹配条件（FIND_IN_SET）
                            wrap.and(innerWrapper -> {
                                for (String deptId : deptIdList) {
                                    innerWrapper.or(w -> w.apply("FIND_IN_SET({0}, dept_ids)", deptId));
                                }
                            });
                        })
                .orderByAsc(MottoSimpleDO::getId));
    }

}
