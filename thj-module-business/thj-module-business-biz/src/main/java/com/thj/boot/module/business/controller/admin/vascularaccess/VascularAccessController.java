package com.thj.boot.module.business.controller.admin.vascularaccess;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.vascularaccess.VascularAccessConvert;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessCreateReqVO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessPageReqVO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessRespVO;
import com.thj.boot.module.business.pojo.vascularaccess.vo.VascularAccessUpdateReqVO;
import com.thj.boot.module.business.service.vascularaccess.PatientVascularAccessService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 新建通路
 */
@RestController
@RequestMapping("/business/vascular-access")
@Validated
public class VascularAccessController {

    @Resource
    private PatientVascularAccessService patientVascularAccessService;

    @PostMapping("/create")
    public CommonResult<Long> createVascularAccess(@Valid @RequestBody VascularAccessCreateReqVO createReqVO) {
        return success(patientVascularAccessService.createVascularAccess(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateVascularAccess(@Valid @RequestBody VascularAccessUpdateReqVO updateReqVO) {
        patientVascularAccessService.updateVascularAccess(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteVascularAccess(@RequestParam("id") Long id) {
        patientVascularAccessService.deleteVascularAccess(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<VascularAccessRespVO> getVascularAccess(@RequestParam("id") Long id) {
        VascularAccessDO vascularAccess = patientVascularAccessService.getVascularAccess(id);
        return success(VascularAccessConvert.INSTANCE.convert(vascularAccess));
    }

    @GetMapping("/list")
    public CommonResult<List<VascularAccessRespVO>> getVascularAccessList(VascularAccessCreateReqVO createReqVO) {
        return success(patientVascularAccessService.getVascularAccessList(createReqVO));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<VascularAccessRespVO>> getVascularAccessPage(@Valid VascularAccessPageReqVO pageVO) {
        PageResult<VascularAccessDO> pageResult = patientVascularAccessService.getVascularAccessPage(pageVO);
        return success(VascularAccessConvert.INSTANCE.convertPage(pageResult));
    }

}
