package com.thj.boot.module.business.controller.admin.dialysisdetection.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/24 14:20
 * @description
 */
@Data
public class BloodPressureExcelVO implements Serializable {
    /**
     * 透析号
     */
    @ExcelProperty("透析号")
    private String dialyzeNo;
    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String patientName;
    /**
     * 机号
     */
    @ExcelProperty("机号")
    private String facilityName;
    /**
     * 透析模式
     */
    @ExcelProperty("透析模式")
    private String dialysisName;
    /**
     * 时间
     */
    @ExcelProperty("时间")
    private String dayState;
    /**
     * 病区名称
     */
    @ExcelProperty("病区名称")
    private String faciitySubareaName;
    /**
     * 干体重(kg)
     */
    @ExcelProperty("干体重(kg)")
    private String dryWeight;
    /**
     * 目标脱水量(L)
     */
    @ExcelProperty("目标脱水量(L)")
    private String dehydration;
    /**
     * 超滤总量(L)
     */
    @ExcelProperty("超滤总量(L)")
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量(L)
     */
    @ExcelProperty("实际超滤量(L)")
    private String actualUltrafiltrationCapacity;
    /**
     * 透后体重(kg)
     */
    @ExcelProperty("透后体重(kg)")
    private String dialyzeAfterWeight;
    /**
     * 透前血压脉搏
     */
    @ExcelProperty("透前")
    private String beforeBlood;
    /**
     * 第1次(血压+脉搏)
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第1次"})
    private String one;
    /**
     * 第2次
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第2次"})
    private String two;
    /**
     * 第3次
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第3次"})
    private String three;
    /**
     * 第4次
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第4次"})
    private String four;
    /**
     * 第5次
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第5次"})
    private String five;
    /**
     * 第n次
     */
    @ExcelProperty({"透中血压(mmHg)/透中脉搏(次/分)", "第n次"})
    private String more;
    /**
     * 透后血压脉搏
     */
    @ExcelProperty("透后")
    private String afterBlood;

}
