package com.thj.boot.module.business.controller.admin.firstcourserecord;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.firstcourserecord.FirstCourseRecordConvert;
import com.thj.boot.module.business.dal.datado.firstcourserecord.FirstCourseRecordDO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordRespVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordUpdateReqVO;
import com.thj.boot.module.business.service.firstcourserecord.FirstCourseRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/first-course-record")
@Validated
public class FirstCourseRecordController {

    @Resource
    private FirstCourseRecordService firstCourseRecordService;

    @PostMapping("/create")
    public CommonResult<Long> createFirstCourseRecord(@RequestBody FirstCourseRecordCreateReqVO createReqVO) {
        return success(firstCourseRecordService.createFirstCourseRecord(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateFirstCourseRecord(@RequestBody FirstCourseRecordUpdateReqVO updateReqVO) {
        firstCourseRecordService.updateFirstCourseRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteFirstCourseRecord(@RequestParam("id") Long id) {
        firstCourseRecordService.deleteFirstCourseRecord(id);
        return success(true);
    }

    @PostMapping("/get")
    public CommonResult<FirstCourseRecordRespVO> getFirstCourseRecord(@RequestBody FirstCourseRecordCreateReqVO createReqVO) {
        return success(firstCourseRecordService.getFirstCourseRecord(createReqVO));
    }

    @GetMapping("/list")
    public CommonResult<List<FirstCourseRecordRespVO>> getFirstCourseRecordList(FirstCourseRecordCreateReqVO createReqVO) {
        List<FirstCourseRecordDO> list = firstCourseRecordService.getFirstCourseRecordList(createReqVO);
        return success(FirstCourseRecordConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<FirstCourseRecordRespVO>> getFirstCourseRecordPage(@RequestBody FirstCourseRecordPageReqVO pageVO) {
        PageResult<FirstCourseRecordDO> pageResult = firstCourseRecordService.getFirstCourseRecordPage(pageVO);
        return success(FirstCourseRecordConvert.INSTANCE.convertPage(pageResult));
    }


}
