package com.thj.boot.module.business.controller.admin.qualityinspection;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.qualityinspection.QualityInspectionConvert;
import com.thj.boot.module.business.dal.datado.qualityinspection.QualityInspectionDO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionCreateReqVO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionPageReqVO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionRespVO;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionUpdateReqVO;
import com.thj.boot.module.business.service.qualityinspection.QualityInspectionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 质量检测
 */
@RestController
@RequestMapping("/business/quality-inspection")
@Validated
public class QualityInspectionController {

    @Resource
    private QualityInspectionService qualityInspectionService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createQualityInspection(@RequestBody QualityInspectionCreateReqVO createReqVO) {
        return success(qualityInspectionService.createQualityInspection(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateQualityInspection(@RequestBody QualityInspectionUpdateReqVO updateReqVO) {
        qualityInspectionService.updateQualityInspection(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteQualityInspection(@RequestParam("id") Long id) {
        qualityInspectionService.deleteQualityInspection(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<QualityInspectionRespVO> getQualityInspection(@RequestParam("id") Long id) {
        QualityInspectionDO qualityInspection = qualityInspectionService.getQualityInspection(id);
        return success(QualityInspectionConvert.INSTANCE.convert(qualityInspection));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<QualityInspectionRespVO>> getQualityInspectionList(QualityInspectionCreateReqVO createReqVO) {
        List<QualityInspectionDO> list = qualityInspectionService.getQualityInspectionList(createReqVO);
        return success(QualityInspectionConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<QualityInspectionRespVO>> getQualityInspectionPage(@RequestBody QualityInspectionPageReqVO pageVO) {
        return success(qualityInspectionService.getQualityInspectionPage(pageVO));
    }

}
