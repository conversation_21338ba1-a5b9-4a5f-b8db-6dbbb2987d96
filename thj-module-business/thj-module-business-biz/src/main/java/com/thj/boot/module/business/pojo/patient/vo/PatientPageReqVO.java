package com.thj.boot.module.business.pojo.patient.vo;

import com.thj.boot.common.pojo.PageParam;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientFieldReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PatientPageReqVO extends PageParam {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 1-男，2-女
     */
    private String sex;
    /**
     * 身份证类型
     */
    private String idCardType;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 住院号
     */
    private String hospitalNo;
    /**
     * 病区
     */
    private List<String> endemicAreas;

    /**
     * 床号
     */
    private Integer bedNo;
    /**
     * 婚姻状况
     */
    private String marriage;
    /**
     * 报销方式
     */
    private String applyWay;
    /**
     * 医保号
     */
    private String baoNo;
    /**
     * 医保到期日期
     */
    private Date baoEndTime;
    /**
     * 医保到期提醒时间
     */
    private Date baoEndWarnTime;
    /**
     * 身高
     */
    private String stature;
    /**
     * 初始体重
     */
    private String initWeight;
    /**
     * 血型
     */
    private String bloodType;
    /**
     * RH
     */
    private String rh;
    /**
     * 文化程度
     */
    private String culture;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 吸烟
     */
    private String smoking;
    /**
     * 视力障碍
     */
    private String eyesight;
    /**
     * 饮酒
     */
    private String tipple;
    /**
     * 宗教信仰
     */
    private String religion;
    /**
     * 民族
     */
    private String nation;
    /**
     * 电话(本人)
     */
    private String mobile;
    /**
     * 电话(家属)
     */
    private String mobileMan;
    /**
     * 家庭住址
     */
    private String address;
    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 接收日期
     */
    private LocalDateTime receiveTime;
    /**
     * 首次透析日期
     */
    private LocalDateTime firstReceiveTime;
    /**
     * 透析年龄
     */
    private String dialyzeAge;
    /**
     * 诱导期
     */
    private LocalDateTime induceTime;
    /**
     * 初始透析次数
     */
    private String initDialyzeNo;
    /**
     * 透析总次数
     */
    private String dialyzeTotal;
    /**
     * 传染病
     */
    private String infect;
    /**
     * 角色是医生用户id
     */
    private Long medic;
    /**
     * 角色是护士用户id
     */
    private Long nurse;
    /**
     * 诊断
     */
    private String describe;
    /**
     * 备注
     */
    private String remark;
    /**
     * 过敏史
     */
    private String allergy;
    /**
     * 肿瘤史
     */
    private String tumor;
    /**
     * 红细胞压积
     */
    private String erythrocyte;
    /**
     * 认证角色
     */
    private String authRole;
    /**
     * 认证姓名
     */
    private String authName;
    /**
     * 认证身份证号
     */
    private String authIdCard;
    /**
     * 认证任联系方式
     */
    private String authMobile;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 标签管理
     */
    private String labels;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 多个搜索项
     */
    private String more;
    /**
     * 0-未排班，1-已排班
     */
    private String scheduling;
    /**
     * 0-无方案，1-有方案
     */
    private String dialysisPlan;
    /**
     * a-上午，b-下午,c-晚上
     */
    private List<String> dayState;
    /**
     * 多个患者id
     */
    private List<Long> PatientIdList;
    /**
     * 我的病人,上机护士ID
     */
    private String computerNurse;

    private String days;

    /**
     * 00-转入，01-转出
     */
    private String patientTypeSource;

    private String spellShort;

    /**
     * 字段名
     */
    private String fields;

    /**
     * 名称和字段
     */
    private List<PatientFieldReqVO> patientFieldReqVOS;


    private String queryDate;

    /**
     * cnrds同步标识 Y/N
     */
    private String cnrdsSyncFlag;

    private String sortType;
    private String[] date;
}
