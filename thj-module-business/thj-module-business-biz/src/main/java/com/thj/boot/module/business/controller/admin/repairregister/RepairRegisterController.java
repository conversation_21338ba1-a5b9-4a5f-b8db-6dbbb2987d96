package com.thj.boot.module.business.controller.admin.repairregister;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.repairregister.RepairRegisterConvert;
import com.thj.boot.module.business.dal.datado.repairregister.RepairRegisterDO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterPageReqVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterUpdateReqVO;
import com.thj.boot.module.business.service.repairregister.RepairRegisterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 维修登记-透析机-水处理机-CCDS-CDDS-其他
 */
@RestController
@RequestMapping("/business/repair-register")
@Validated
public class RepairRegisterController {

    @Resource
    private RepairRegisterService repairRegisterService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createRepairRegister(@Valid @RequestBody RepairRegisterCreateReqVO createReqVO) {
        return success(repairRegisterService.createRepairRegister(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateRepairRegister(@Valid @RequestBody RepairRegisterUpdateReqVO updateReqVO) {
        repairRegisterService.updateRepairRegister(updateReqVO);
        return success(true);
    }


    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteRepairRegister(@RequestParam("id") Long id) {
        repairRegisterService.deleteRepairRegister(id);
        return success(true);
    }

    /**
     * 详情
     */
    @PostMapping("/get")
    public CommonResult<RepairRegisterRespVO> getRepairRegister(@RequestBody RepairRegisterUpdateReqVO repairRegisterUpdateReqVO) {
        RepairRegisterDO repairRegister = repairRegisterService.getRepairRegister(repairRegisterUpdateReqVO);
        return success(RepairRegisterConvert.INSTANCE.convert(repairRegister));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<RepairRegisterRespVO>> getRepairRegisterList(RepairRegisterCreateReqVO createReqVO) {
        List<RepairRegisterDO> list = repairRegisterService.getRepairRegisterList(createReqVO);
        return success(RepairRegisterConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<RepairRegisterRespVO>> getRepairRegisterPage(@RequestBody RepairRegisterPageReqVO pageVO) {
        PageResult<RepairRegisterDO> pageResult = repairRegisterService.getRepairRegisterPage(pageVO);
        return success(RepairRegisterConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 保存完之后显示最新的一条数据
     */
    @GetMapping("/getNew")
    public CommonResult<RepairRegisterRespVO> getNewRepairRegister(RepairRegisterCreateReqVO createReqVO) {
        return success(repairRegisterService.getNewRepairRegister(createReqVO));
    }


}
