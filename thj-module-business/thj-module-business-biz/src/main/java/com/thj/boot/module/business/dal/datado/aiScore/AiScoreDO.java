package com.thj.boot.module.business.dal.datado.aiScore;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.thj.boot.common.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 病历首页 DO
 *
 * <AUTHOR>
 */
@TableName("ai_score_medical_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiScoreDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 患者id
     */
    private Long patientId;

    private String patient_name;

    private String content;

    /**
     * 专家点评
     */
    private String expertComment;

    /**
     * AI评分结果JSON
     */
    private String scoreResult;

    /**
     * 病历查验结果JSON
     */
    private String checkResult;

    /**
     * 风险等级评分JSON
     */
    private String riskResult;

    /**
     * 总分
     */
    private Integer scoreTotal;

    /**
     * 评分等级(甲级/乙级/丙级)
     */
    private String scoreLevel;

    /**
     * AI模型名称
     */
    private String aiModel;

    /**
     * AI评分使用的提示词
     */
    private String aiPrompt;

    /**
     * AI API请求JSON数据
     */
    private String aiRequestJson;

    /**
     * AI API响应JSON数据
     */
    private String aiResponseJson;

    /**
     * 患者基本信息(接口1返回数据)
     */
    private String patientBasicInfo;

    /**
     * 初始诊疗信息(接口2返回数据)
     */
    private String treatmentInfo;

    /**
     * AI评分详细推理过程
     */
    private String aiReasoning;

    /**
     * AI报告生成处理时间(秒)
     * 精确记录generateAiReports方法执行耗时
     * 支持小数位精度(如2.350秒)
     */
    private BigDecimal processTime;

}
