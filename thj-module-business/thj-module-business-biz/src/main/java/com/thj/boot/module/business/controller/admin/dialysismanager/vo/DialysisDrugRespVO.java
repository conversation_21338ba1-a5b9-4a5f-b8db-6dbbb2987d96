package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/3 14:41
 * @description
 */
@Data
public class DialysisDrugRespVO extends DialyzeArrangeBaseVO{

    /**
     * 处方状态json
     */
    private String prescriptionState;
    /**
     * 促红素(推送)
     */
    private String erythropoietinPush;
    /**
     * 促红素(医嘱)
     */
    private String erythropoietin;
    /**
     * 左卡尼汀(推送)
     */
    private String carnitinePush;
    /**
     * 左卡尼汀(医嘱)
     */
    private String carnitine;
    /**
     * 蔗糖铁 (推送)
     */
    private String ironSucrosePush;
    /**
     * 蔗糖铁 (医嘱)
     */
    private String ironSucrose;
    /**
     * 帕立骨化醇(推送)
     */
    private String paricalcitolPush;
    /**
     * 帕立骨化醇(医嘱)
     */
    private String paricalcitol;
    /**
     * 骨化三醇(推送)
     */
    private String calcitriolPush;
    /**
     * 骨化三醇(医嘱)
     */
    private String calcitriol;
    /**
     * 尿激酶 (推送)
     */
    private String urokinasePush;
    /**
     * 尿激酶 (医嘱)
     */
    private String urokinase;
    /**
     * 葡萄糖酸钙(推送)
     */
    private String calciumGluconatePush;
    /**
     * 葡萄糖酸钙(医嘱)
     */
    private String calciumGluconate;
}
