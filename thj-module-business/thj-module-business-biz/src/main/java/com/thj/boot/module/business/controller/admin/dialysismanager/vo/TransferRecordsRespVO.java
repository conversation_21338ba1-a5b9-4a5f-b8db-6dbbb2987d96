package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/28 15:27
 * @description 交班日志
 */
@Data
public class TransferRecordsRespVO implements Serializable {
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 日期
     */
    private Date recordTime;
    /**
     * 透析人数/人
     */
    private String dialyzeNumber;
    /**
     * 加透患者/人
     */
    private String penetration;
    /**
     * 转入患者/人
     */
    private String trunIn;
    /**
     * 转出患者/人
     */
    private String trunOut;
    /**
     * 住院患者/人
     */
    private String hospitalized;
    /**
     * 留治患者/人
     */
    private String hospitalization;
    /**
     * 死亡患者/人
     */
    private String deathNumber;
    /**
     * 无肝素透析/例
     */
    private String nonHeparin;
    /**
     * 自体动静脉内瘘/人次
     */
    private String artery;
    /**
     * 长期导管/人次
     */
    private String longDuct;
    /**
     * 临时导管/人次
     */
    private String temporarilyDuct;
    /**
     * 人工血管/人次
     */
    private String labour;
    /**
     * 凝血III度/人次
     */
    private String clotting;
    /**
     * 超滤总量大于5L/人次
     */
    private String totalUltrafiltration;
    /**
     * 体温高于37.5度/人
     */
    private String animal;
    /**
     * 血管通路
     */
    private List<BloodPassageComplicationRespVO> bloodPassageComplicationRespVOS;
    /**
     * 故障发生阶段
     */
    private List<RepairRegisterRespVO> repairRegisterRespVOS;
    /**
     * 透析例次
     */
    private List<Map<String, Object>> dialysisCounts;
    /**
     * 透析例次合计
     */
    private Integer sum;
    /**
     * 穿刺
     */
    private Integer dressingChangeOne;
    /**
     * 换药
     */
    private Integer dressingChangeTwo;

    /**
     * 患者病情
     */
    private List<PatientConditionVo> patientConditionVos;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 备注 医生
     */
    private String remarksDoctor;
}
