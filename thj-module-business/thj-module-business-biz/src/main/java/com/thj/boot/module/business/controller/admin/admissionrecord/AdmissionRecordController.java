package com.thj.boot.module.business.controller.admin.admissionrecord;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.service.admissionrecord.AdmissionRecordService;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/business/admissionRecord")
@Validated
public class AdmissionRecordController {
    @Autowired
    AdmissionRecordService admissionRecordService;
    @GetMapping("/getById")
    public CommonResult<AdmissionRecordVo> admissionRecordGetById(@RequestParam("id") Long patientId,@RequestParam("name") String name) {
        return success(CourseRecordFactory.get(name).getList(patientId));
    }
    @PostMapping("/saveOrUpdateById")
    public CommonResult<Boolean> saveOrUpdateById(@RequestBody AdmissionRecordVo admissionRecordVo) {
        return success(CourseRecordFactory.get(admissionRecordVo.getName()).saveOrUpdate(admissionRecordVo));
    }
}
