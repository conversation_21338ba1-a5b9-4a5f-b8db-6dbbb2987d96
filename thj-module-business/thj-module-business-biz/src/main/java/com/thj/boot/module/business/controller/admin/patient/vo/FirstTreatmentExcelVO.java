package com.thj.boot.module.business.controller.admin.patient.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者首次自疗详情 （导出）
 */
@Data
public class FirstTreatmentExcelVO  implements Serializable {

    /**
     * 序号
     */
    @ExcelProperty("序号")
    private int index;

    /**
     * 中心id
     */
    @ExcelIgnore
    private Long deptId;

    /**
     * 中心名称
     */
    @ExcelProperty("中心名称")
    private String deptName;
    /**
     * 透析号
     */
    @ExcelProperty("透析号")
    private String dialyzeNo;

    /**
     * 患者姓名
     */
    @ExcelProperty("患者姓名")
    private String name;

    /**
     * 1-男，2-女
     */
    @ExcelProperty("性别")
    private String sex;;

    /**
     * 年龄
     */
    @ExcelProperty("年龄")
    private Integer age;

    /**
     * 转入时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("转入时间")
    private Date receiveTime;

    /**
     * 最近转入时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("最近转入时间")
    private Date latestReceiveTime;

    /**
     * 在中心首次治疗时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("在中心首次治疗时间")
    private Date firstCureTime;

    /**
     * 最后一次治疗时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("最后一次治疗时间")
    private Date lastCureTime;

    /**
     * 转出时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("转出时间")
    private Date outTime;




}
