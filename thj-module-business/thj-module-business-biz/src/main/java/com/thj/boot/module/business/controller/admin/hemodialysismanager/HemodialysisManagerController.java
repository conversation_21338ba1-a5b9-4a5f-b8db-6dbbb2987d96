package com.thj.boot.module.business.controller.admin.hemodialysismanager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.thj.boot.common.annotation.OperateLog;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.hemodialysismanager.vo.*;
import com.thj.boot.module.business.controller.admin.patient.vo.FirstTreatmentExcelVO;
import com.thj.boot.module.business.convert.hemodialysis.HemodialysisConvert;
import com.thj.boot.module.business.convert.hemodialysismanager.HemodialysisManagerConvert;
import com.thj.boot.module.business.convert.sitemarkers.SiteMarkersConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.sitemarkers.SiteMarkersDO;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.mottosimple.MottoSimpleMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesLastVo;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.*;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.pojo.sitemarkers.vo.SiteMarkersRespVO;
import com.thj.boot.module.business.service.facilitymanager.FacilityManagerService;
import com.thj.boot.module.business.service.hemodialysismanager.HemodialysisManagerService;
import com.thj.boot.module.business.service.patient.PatientService;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 血液透析管理
 */
@RestController
@RequestMapping("/business/hemodialysis-manager")
@Validated
public class HemodialysisManagerController {

    @Resource
    private HemodialysisManagerService hemodialysisManagerService;

    @Autowired
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Autowired
    private MottoSimpleMapper mottoSimpleMapper;


    @Autowired
    private PatientService patientService;

    @Autowired
    private FacilityManagerService facilityManagerService;

    /**
     * 新增
     */
    @PostMapping("/create")
    @OperateLog("新增透析")
    @RepeatSubmit
    public CommonResult<Boolean> createHemodialysisManager(@Valid @RequestBody HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
        hemodialysisManagerService.createHemodialysisManager(createReqVO, request);
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateHemodialysisManager(@Valid @RequestBody HemodialysisManagerUpdateReqVO updateReqVO) {
        hemodialysisManagerService.updateHemodialysisManager(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    @OperateLog("删除透析")
    public CommonResult<Boolean> deleteHemodialysisManager(@RequestParam("id") Long id) {
        hemodialysisManagerService.deleteHemodialysisManager(id);
        return success(true);
    }

    /**
     * 详情
     */
    @PostMapping("/get")
    public CommonResult<HemodialysisManagerRespVO> getHemodialysisManager(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getHemodialysisManager(createReqVO));
    }
    @GetMapping("/getById")
    public CommonResult<HemodialysisManagerDO> getHemodialysisManagerGetById(@RequestParam("patientId") Long patientId) {
        return success(hemodialysisManagerService.getHemodialysisManagerGetById(patientId));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<HemodialysisManagerRespVO>> getHemodialysisManagerList(HemodialysisManagerCreateReqVO createReqVO) {
        List<HemodialysisManagerDO> list = hemodialysisManagerService.getHemodialysisManagerList(createReqVO);
        return success(HemodialysisManagerConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/last")
    public CommonResult<Map<String, Object>> getHemodialysisManagerLast(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getHemodialysisManagerLast(createReqVO));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<HemodialysisManagerRespVO>> getHemodialysisManagerPage(@RequestBody HemodialysisManagerPageReqVO pageVO) {
        PageResult<HemodialysisManagerDO> pageResult = hemodialysisManagerService.getHemodialysisManagerPage(pageVO);
        return success(HemodialysisManagerConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 获取机型
     */
    @PostMapping("/getFacilityManagerInfo")
    public CommonResult<FacilityManagerRespVO> getFacilityManagerInfo(@RequestBody ArrangeClassesCreateReqVO createReqVO) {
        return success(facilityManagerService.getFacilityManagerInfo(createReqVO));
    }

    /**
     * 开始透析
     */
    @PostMapping("/startDialysis")
    @OperateLog("开始透析")
    @RepeatSubmit
    public CommonResult<Long> startDialysis(@RequestBody HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
        return success(hemodialysisManagerService.startDialysis(createReqVO, request));
    }

    /**
     * 结束透析
     */
    @PostMapping("/endDialysis")
    @OperateLog("结束透析")
    public CommonResult<Long> endDialysis(@RequestBody HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request) {
//        hemodialysisManagerService.endDialysis(createReqVO);
        return success(hemodialysisManagerService.endDialysis(createReqVO, request));
    }

    /**
     * 自查-归档
     */
    @PostMapping("/pigeonholeDialysis")
    public CommonResult<Boolean> pigeonholeDialysis(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        hemodialysisManagerService.pigeonholeDialysis(createReqVO);
        return success(true);
    }

    /**
     * 透析准备
     */
    @PostMapping("/consumablePage")
    public CommonResult<List<HemodialysisManagerRespVO>> consumablePage(@RequestBody HemodialysisManagerRespVO pageVO) {
        return success(hemodialysisManagerService.consumablePage(pageVO));
    }

    /**
     * 结束透析，透析器使用列表
     */
    @GetMapping("/useInstrumentInfo")
    public CommonResult<List<HemodialysisManagerRespVO>> useInstrumentInfo(HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.useInstrumentInfo(createReqVO));
    }

    /**
     * 透析单打印详情
     */
    @PostMapping("/getPrintInfo")
    public CommonResult<List<HemodialysisManagerPrintRespVO>> getPrintInfo(@RequestBody HemodialysisManagerPrintRespVO printRespVO) {
        return success(hemodialysisManagerService.getPrintInfo(printRespVO));
    }

    /**
     * 通过透析时长计算结束透析时间
     */
    @PostMapping("/getDailysisTime")
    public CommonResult<HemodialysisManagerRespVO> getDailysisTime(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getDailysisTime(createReqVO));
    }

    /**
     * 用药推送
     */
    @PostMapping("/medicatePush")
    @OperateLog("血液透析临时医嘱用药推送")
    public CommonResult<List<DialysisAdviceRespVO>> medicatePush(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.medicatePush(createReqVO));
    }

    /**
     * 开始透析获取上次穿刺方式/型号/方向信息
     */
    @PostMapping("/getLastHealInfo")
    public CommonResult<HemodialysisManagerRespVO> getLastHealInfo(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getLastHealInfo(createReqVO));
    }

    /**
     * 根据月份回显透析记录
     *
     * @param
     * @return
     */
    @PostMapping("/getLastRecord")
    public CommonResult<List<ArrangeClassesDO>> getLastRecord(@RequestBody ArrangeClassesLastVo vo) {
        return success(hemodialysisManagerService.getLastRecord(vo));
    }

    /**
     * 透析准备
     */
    @PostMapping("/getDialysisPreparation")
    public CommonResult<PageResult<DialysisPreparationRespVO>> getDialysisPreparation(@RequestBody HemodialysisManagerPageReqVO pageReqVO) {
        return success(hemodialysisManagerService.getDialysisPreparation(pageReqVO));
    }

    /**
     * 透析准备统计
     */
    @PostMapping("/preparationStatistics")
    public CommonResult<List<DialysisPreparationRespVO>> getPreparationStatistics(@RequestBody HemodialysisManagerPageReqVO pageReqVO) {
        return success(hemodialysisManagerService.getPreparationStatistics(pageReqVO));
    }


    /**
     * 透析单打印详情
     */
    @PostMapping("/getPrintDialyze")
    public CommonResult<List<HemodialysisManagerPrintDialyzeRespVO>> getPrintDialyze(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getPrintDialyze(createReqVO));
    }
    /**
     * 透析单打印详情
     */
    @PostMapping("/getPrintDialyzeBatch")
    public CommonResult<List<HemodialysisManagerPrintDialyzeRespVO>> getPrintDialyzeBatch(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(hemodialysisManagerService.getPrintDialyzeBatch(createReqVO));
    }

    /**
     * 血液透析修改干体重信息
     */
    @PostMapping("/updateDryWeight")
    @OperateLog("血液透析修改干体重信息")
    public CommonResult<Boolean> updateDryWeight(@RequestBody HemodialysisReachReqVO createReqVO) {
        hemodialysisManagerService.updateDryWeight(createReqVO);
        return success(true);
    }


    /**
     * 汇总导出
     */
    @PostMapping("/export-excel")
    public void exportDevManExcel(@RequestBody HemodialysisManagerPageReqVO pageReqVO
            , HttpServletResponse response) throws IOException {
        PageResult<DialysisPreparationRespVO> dialysisPreparation = hemodialysisManagerService.getDialysisPreparation(pageReqVO);
        PageResult<PreparationExcelVO> excelVOPageResult = HemodialysisConvert.INSTANCE.convertExcel(dialysisPreparation);
        EasyExcelUtils.write(response, "汇总.xls", "汇总", PreparationExcelVO.class, excelVOPageResult.getList());
    }


    /**
     * 位点记录列表 (最新10条）
     */
    @GetMapping("/getSiteRecordList")
    public CommonResult<List<HemodialysisSiteRecordRespVO>> getSiteRecordList(@RequestParam("patientId") Long patientId, @RequestParam("size") Integer size) {
        return success(hemodialysisManagerService.getSiteRecordList(patientId, size));
    }

    /**
     * 患者治疗记录详情
     */
    @PostMapping("/treatRecord")
    public CommonResult<PageResult<HemodialysisManagerRespVO>> getPatientTreatRecordDetail(@RequestBody HemodialysisManagerPageReqVO pageReqVO) {
        return success(patientService.getPatientTreatRecordDetail(pageReqVO));
    }

    /**
     * 患者治疗记录详情（导出数据）
     */
    @PostMapping("/treatRecord/export")
    public void exportPatientTreatRecordDetail(@RequestBody HemodialysisManagerPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
//        // 获取导出的数据
//        PageResult<HemodialysisManagerRespVO> patientTreatRecordDetail = hemodialysisManagerService.getPatientTreatRecordDetail(pageReqVO);
//        List<HemodialysisManagerRespVO> patientTreatRecordDetailList = patientTreatRecordDetail.getList();
        List<PatientTreatRecordDetailVO> patientTreatRecordDetailVOS = BeanUtil.copyToList(patientService.getPatientTreatRecordDetail(pageReqVO).getList(), PatientTreatRecordDetailVO.class);
//
        // 数据序号
        AtomicInteger indexCounter = new AtomicInteger(1);
        // 处理数据
        patientTreatRecordDetailVOS = patientTreatRecordDetailVOS.stream().peek(item -> {
            // 设置序号
            item.setIndex(indexCounter.getAndIncrement());

        }).collect(Collectors.toList());

        EasyExcelUtils.write(response, "患者治疗记录详情.xls", "患者治疗记录详情", PatientTreatRecordDetailVO.class, patientTreatRecordDetailVOS);
    }

}
