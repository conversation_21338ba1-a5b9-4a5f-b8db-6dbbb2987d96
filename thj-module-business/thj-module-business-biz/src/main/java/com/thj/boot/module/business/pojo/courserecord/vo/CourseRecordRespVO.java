package com.thj.boot.module.business.pojo.courserecord.vo;

import com.thj.boot.module.business.pojo.infra.vo.InfraRespVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CourseRecordRespVO extends CourseRecordBaseVO {

    private List<InfraRespVO> infraRespVOList;

    private String doctorName;

}
