package com.thj.boot.module.business.controller.admin.checkdataconvert;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertBaseVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertPageReqVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertRespVO;
import com.thj.boot.module.business.pojo.checkdataconvert.vo.CheckDataConvertUpdateReqVO;
import com.thj.boot.module.business.service.checkdataconvert.CheckDataConvertService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/check-data-convert")
@Validated
public class CheckDataConvertController {

    @Resource
    private CheckDataConvertService checkDataConvertService;

    @GetMapping("/get")
    public CommonResult<CheckDataConvertRespVO> getCheckDataConvert(@RequestParam("id") Long id) {
        return success(checkDataConvertService.getCheckDataConvert(id));
    }

    @GetMapping("/list")
    public CommonResult<List<CheckDataConvertRespVO>> getCheckDataConvertList(CheckDataConvertBaseVO exportReqVO) {
        return success(checkDataConvertService.getCheckDataConvertList(exportReqVO));
    }

    @GetMapping("/page")
    public CommonResult<PageResult<CheckDataConvertRespVO>> getCheckDataConvertPage(CheckDataConvertPageReqVO pageVO) {
        return success( checkDataConvertService.getCheckDataConvertPage(pageVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateCheckDataConvert(@RequestBody CheckDataConvertUpdateReqVO updateReqVO) {
        return success(checkDataConvertService.updateCheckDataConvert(updateReqVO));
    }
}
