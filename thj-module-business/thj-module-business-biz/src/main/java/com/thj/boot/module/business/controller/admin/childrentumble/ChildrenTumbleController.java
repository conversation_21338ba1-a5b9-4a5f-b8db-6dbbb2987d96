package com.thj.boot.module.business.controller.admin.childrentumble;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.childrentumble.ChildrenTumbleConvert;
import com.thj.boot.module.business.dal.datado.childrentumble.ChildrenTumbleDO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleCreateReqVO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumblePageReqVO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleRespVO;
import com.thj.boot.module.business.pojo.childrentumble.vo.ChildrenTumbleUpdateReqVO;
import com.thj.boot.module.business.service.childrentumble.ChildrenTumbleService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/children-tumble")
@Validated
public class ChildrenTumbleController {

    @Resource
    private ChildrenTumbleService childrenTumbleService;

    @PostMapping("/create")
    public CommonResult<Long> createChildrenTumble(@RequestBody ChildrenTumbleCreateReqVO createReqVO) {
        return success(childrenTumbleService.createChildrenTumble(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateChildrenTumble(@RequestBody ChildrenTumbleUpdateReqVO updateReqVO) {
        childrenTumbleService.updateChildrenTumble(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteChildrenTumble(@RequestParam("id") Long id) {
        childrenTumbleService.deleteChildrenTumble(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<ChildrenTumbleRespVO> getChildrenTumble(@RequestParam("id") Long id) {
        ChildrenTumbleDO childrenTumble = childrenTumbleService.getChildrenTumble(id);
        return success(ChildrenTumbleConvert.INSTANCE.convert(childrenTumble));
    }

    @GetMapping("/list")
    public CommonResult<List<ChildrenTumbleRespVO>> getChildrenTumbleList(ChildrenTumbleCreateReqVO createReqVO) {
        List<ChildrenTumbleDO> list = childrenTumbleService.getChildrenTumbleList(createReqVO);
        return success(ChildrenTumbleConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<ChildrenTumbleRespVO>> getChildrenTumblePage(@RequestBody ChildrenTumblePageReqVO pageVO) {
        PageResult<ChildrenTumbleDO> pageResult = childrenTumbleService.getChildrenTumblePage(pageVO);
        return success(ChildrenTumbleConvert.INSTANCE.convertPage(pageResult));
    }


}
