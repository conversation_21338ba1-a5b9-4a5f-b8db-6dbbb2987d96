package com.thj.boot.module.business.controller.admin.dialysisadvice.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/5 15:19
 * @description
 */
@Data
public class AdviceTempsCreateVO implements Serializable {

    /**
     * 透析日期
     */
    private Date dateWeek;
    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 患者昵称
     */
    private String patientNickName;
    /**
     * 来源
     */
    private String patientSource;

    /**
     * 医嘱模版
     */
    private List<AdviceTempsVO> adviceTempsVO;

    /**
     * 血液透析左侧患者信息
     */
    private String tableContentInfo;
    /**
     * 多个医嘱id
     */
    private String adviceIds;
    /**
     * 单个医嘱id
     */
    private Long adviceId;
    /**
     * 门店id
     */
    private Long deptId;

    /**
     * 多个患者
     */
    private String patientIds;

    private String startTime;

    @Data
    public static class AdviceTempsVO implements Serializable {
        private List<Long> children;
    }
}


