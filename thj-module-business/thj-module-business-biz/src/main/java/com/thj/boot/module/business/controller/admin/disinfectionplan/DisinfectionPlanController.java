package com.thj.boot.module.business.controller.admin.disinfectionplan;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.disinfectionplan.DisinfectionPlanConvert;
import com.thj.boot.module.business.dal.datado.disinfectionplan.DisinfectionPlanDO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanPageReqVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanRespVO;
import com.thj.boot.module.business.pojo.disinfectionplan.vo.DisinfectionPlanUpdateReqVO;
import com.thj.boot.module.business.service.disinfectionplan.DisinfectionPlanService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 消毒计划
 */
@RestController
@RequestMapping("/business/disinfection-plan")
@Validated
public class DisinfectionPlanController {

    @Resource
    private DisinfectionPlanService disinfectionPlanService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createDisinfectionPlan(@RequestBody DisinfectionPlanCreateReqVO createReqVO) {
        disinfectionPlanService.createDisinfectionPlan(createReqVO);
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDisinfectionPlan(@RequestBody DisinfectionPlanUpdateReqVO updateReqVO) {
        disinfectionPlanService.updateDisinfectionPlan(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDisinfectionPlan(@RequestParam("id") Long id) {
        disinfectionPlanService.deleteDisinfectionPlan(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DisinfectionPlanRespVO> getDisinfectionPlan(@RequestParam("id") Long id) {
        DisinfectionPlanDO disinfectionPlan = disinfectionPlanService.getDisinfectionPlan(id);
        return success(DisinfectionPlanConvert.INSTANCE.convert(disinfectionPlan));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DisinfectionPlanRespVO>> getDisinfectionPlanList(DisinfectionPlanCreateReqVO createReqVO) {
        List<DisinfectionPlanDO> list = disinfectionPlanService.getDisinfectionPlanList(createReqVO);
        return success(DisinfectionPlanConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DisinfectionPlanRespVO>> getDisinfectionPlanPage(@RequestBody DisinfectionPlanPageReqVO pageVO) {
        PageResult<DisinfectionPlanDO> pageResult = disinfectionPlanService.getDisinfectionPlanPage(pageVO);
        return success(DisinfectionPlanConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     *根据机号获取消毒计划
     */
    @PostMapping("/getFacilityInfo")
    public CommonResult<DisinfectionPlanRespVO> getFacilityInfo(@RequestBody DisinfectionPlanCreateReqVO createReqVO) {
        return success(disinfectionPlanService.getFacilityInfo(createReqVO));
    }



}
