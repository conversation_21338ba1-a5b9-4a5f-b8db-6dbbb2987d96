package com.thj.boot.module.business.controller.admin.disinfectionunit;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.convert.disinfectionunit.DisinfectionUnitConvert;
import com.thj.boot.module.business.dal.datado.disinfectionunit.DisinfectionUnitDO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitCreateReqVO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitRespVO;
import com.thj.boot.module.business.pojo.disinfectionunit.vo.DisinfectionUnitUpdateReqVO;
import com.thj.boot.module.business.service.disinfectionunit.DisinfectionUnitService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/disinfection-unit")
@Validated
public class DisinfectionUnitController {

    @Resource
    private DisinfectionUnitService disinfectionUnitService;

    @PostMapping("/create")
    public CommonResult<Long> createDisinfectionUnit( @RequestBody DisinfectionUnitCreateReqVO createReqVO) {
        return success(disinfectionUnitService.createDisinfectionUnit(createReqVO));
    }

    @PutMapping("/update")
    public CommonResult<Boolean> updateDisinfectionUnit( @RequestBody DisinfectionUnitUpdateReqVO updateReqVO) {
        disinfectionUnitService.updateDisinfectionUnit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    public CommonResult<Boolean> deleteDisinfectionUnit(@RequestParam("id") Long id) {
        disinfectionUnitService.deleteDisinfectionUnit(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<DisinfectionUnitRespVO> getDisinfectionUnit(@RequestParam("id") Long id) {
        DisinfectionUnitDO disinfectionUnit = disinfectionUnitService.getDisinfectionUnit(id);
        return success(DisinfectionUnitConvert.INSTANCE.convert(disinfectionUnit));
    }

    @GetMapping("/list")
    public CommonResult<List<DisinfectionUnitRespVO>> getDisinfectionUnitList(@RequestParam("ids") Collection<Long> ids) {
        List<DisinfectionUnitDO> list = disinfectionUnitService.getDisinfectionUnitList(ids);
        return success(DisinfectionUnitConvert.INSTANCE.convertList(list));
    }


}
