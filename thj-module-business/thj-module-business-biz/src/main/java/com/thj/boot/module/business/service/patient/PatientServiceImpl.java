package com.thj.boot.module.business.service.patient;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.thj.boot.common.annotation.RepeatSubmit;
import com.thj.boot.module.business.dal.datado.jkimplementation.JkImplementationDO;
import com.thj.boot.module.business.dal.mapper.jkimplementation.JkImplementationMapper;
import com.thj.boot.module.business.pojo.jkimplementation.vo.JkImplementationCreateReqVO;
import com.thj.boot.module.business.service.jkimplementation.JkImplementationService;
import org.springframework.util.StopWatch;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import com.thj.boot.common.exception.ErrorCode;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.controller.admin.gk.vo.CollectiveVo;
import com.thj.boot.module.business.controller.admin.gk.vo.MaintainDialysisVo;
import com.thj.boot.module.business.controller.admin.gk.vo.PatientImplementationVo;
import com.thj.boot.module.business.controller.admin.patient.vo.DiseaseHistoryRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientFieldReqVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientInspectionRespVO;
import com.thj.boot.module.business.controller.admin.patient.vo.PatientLabelReqVO;
import com.thj.boot.module.business.convert.arrangeclasses.ArrangeClassesConvert;
import com.thj.boot.module.business.convert.dialyzeoption.DialyzeOptionConvert;
import com.thj.boot.module.business.convert.hemodialysismanager.HemodialysisManagerConvert;
import com.thj.boot.module.business.convert.patient.PatientConvert;
import com.thj.boot.module.business.convert.renalproject.RenalProjectConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysismanager.DialysisManagerDO;
import com.thj.boot.module.business.dal.datado.dialysisprotocol.DialysisProtocolDO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import com.thj.boot.module.business.dal.datado.diseasereason.DiseaseReasonDO;
import com.thj.boot.module.business.dal.datado.exam.DialysisAdviceTransferDO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyDO;
import com.thj.boot.module.business.dal.datado.exam.ExamApplyVo;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.facilitymanager.FacilityManagerDO;
import com.thj.boot.module.business.dal.datado.facilitysubarea.FacilitySubareaDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.inspectsettings.InspectSettingsDO;
import com.thj.boot.module.business.dal.datado.jkdivision.JkDivisionDO;
import com.thj.boot.module.business.dal.datado.mottosimple.MottoSimpleDO;
import com.thj.boot.module.business.dal.datado.outcomerecord.OutcomeRecordDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.reachweigh.ReachWeighDO;
import com.thj.boot.module.business.dal.datado.renalproject.RenalProjectDO;
import com.thj.boot.module.business.dal.datado.teampatient.TeamPatientDO;
import com.thj.boot.module.business.dal.datado.useregister.UseRegisterDO;
import com.thj.boot.module.business.dal.datado.vascularaccess.VascularAccessDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.bloodroad.BloodRoadMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.dialysismanager.DialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.dialysisprotocol.DialysisProtocolMapper;
import com.thj.boot.module.business.dal.mapper.dialysisrecord.DialysisRecordMapper;
import com.thj.boot.module.business.dal.mapper.dialyzeoption.DialyzeOptionMapper;
import com.thj.boot.module.business.dal.mapper.diseasereason.DiseaseReasonMapper;
import com.thj.boot.module.business.dal.mapper.exam.ExamApplyMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.facilitymanager.FacilityManagerMapper;
import com.thj.boot.module.business.dal.mapper.facilitysubarea.FacilitySubareaMapper;
import com.thj.boot.module.business.dal.mapper.hemodialysismanager.HemodialysisManagerMapper;
import com.thj.boot.module.business.dal.mapper.inspectsettings.InspectSettingsMapper;
import com.thj.boot.module.business.dal.mapper.jkdivision.JkDivisionMapper;
import com.thj.boot.module.business.dal.mapper.mottosimple.MottoSimpleMapper;
import com.thj.boot.module.business.dal.mapper.outcomerecord.OutcomeRecordMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.reachweigh.ReachWeighMapper;
import com.thj.boot.module.business.dal.mapper.renalproject.RenalProjectMapper;
import com.thj.boot.module.business.dal.mapper.teampatient.TeamPatientMapper;
import com.thj.boot.module.business.dal.mapper.useregister.UseRegisterMapper;
import com.thj.boot.module.business.dal.mapper.vascularaccess.VascularAccessMapper;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.dialyzeoption.vo.DialyzeOptionRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.JkDivisionRespVO;
import com.thj.boot.module.business.pojo.jkdivision.vo.PatientDivisionVo;
import com.thj.boot.module.business.pojo.patient.vo.*;
import com.thj.boot.module.business.pojo.patinfovo.PatInfoVo;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectRespVO;
import com.thj.boot.module.business.service.arrangeclasses.ArrangeClassesServiceImpl2;
import com.thj.boot.module.business.service.hisconsumables.HisConsumablesService;
import com.thj.boot.module.business.service.motto.MottoService;
import com.thj.boot.module.business.service.renalproject.RenalProjectService;
import com.thj.boot.module.business.utils.PinYinUtil;
import com.thj.boot.module.business.wxpublicno.WxPublicNoHandle;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import com.thj.boot.module.system.api.dict.DictDataApi;
import com.thj.boot.module.system.api.dict.dto.DictDataRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.boot.module.system.enums.dict.DictEnum;
import com.thj.boot.module.system.pojo.dept.vo.DeptCreateReqVO;
import com.thj.boot.module.system.pojo.dept.vo.DeptRespVO;
import com.thj.boot.module.system.service.dept.DeptService;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import net.bytebuddy.asm.Advice;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 患者管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PatientServiceImpl extends ServiceImpl<PatientMapper, PatientDO> implements PatientService {

    @Value("${file.staticPath}")
    private String staticPath;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private Environment environment;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private WxPublicNoHandle wxPublicNoHandle;

    @Resource
    private TeamPatientMapper teamPatientMapper;

    @Resource
    private FacilitySubareaMapper facilitySubareaMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private FacilityManagerMapper facilityManagerMapper;

    @Resource
    private DialysisManagerMapper dialysisManagerMapper;

    @Resource
    private MottoSimpleMapper mottoSimpleMapper;

    @Resource
    private DialyzeOptionMapper dialyzeOptionMapper;

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private DialysisRecordMapper dialysisRecordMapper;

    @Resource
    private InspectSettingsMapper inspectSettingsMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private RenalProjectMapper renalProjectMapper;

    @Resource
    private DiseaseReasonMapper diseaseReasonMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private OutcomeRecordMapper outcomeRecordMapper;

    @Resource
    private HemodialysisManagerMapper hemodialysisManagerMapper;

    @Resource
    private BloodRoadMapper bloodRoadMapper;

    @Resource
    private UseRegisterMapper useRegisterMapper;

    @Resource
    private VascularAccessMapper vascularAccessMapper;

    @Autowired
    private ReachWeighMapper reachWeighMapper;

    @Autowired
    private DeptApi deptApi;

    @Value("${his.patient}")
    private String patientUrl;

    @Autowired
    private ExamApplyMapper examApplyMapper;

    @Autowired
    private HisConsumablesService hisConsumablesService;

    @Resource
    private DialysisProtocolMapper dialysisProtocolMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private DeptService deptService;

    private final MottoService mottoService = SpringUtil.getBean(MottoService.class);

    @Resource
    private AdminUserApi usersApi;

    @Resource
    private JkDivisionMapper jkDivisionMapper;

    @Autowired
    private RenalProjectService renalProjectService;

    @Autowired
    private ArrangeClassesServiceImpl2 arrangeClassesService;

    @Autowired
    JkImplementationMapper jkImplementationMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit
    public Long createPatient(PatientCreateReqVO createReqVO) {
        //校验患者唯一性
        checkName(createReqVO.getIdCard());
        //计算透析年龄
        //Long day = null;
        //if (createReqVO.getInduceTime() != null) {
        //    //计算开始透析日期和截止日期差值
        //    day = DateUtil.between(createReqVO.getFirstReceiveTime(), createReqVO.getInduceTime(), DateUnit.DAY);
        //} else {
        //    //计算开始透析时间和当前时间的差值
        //    day = DateUtil.between(createReqVO.getFirstReceiveTime(), DateUtil.date(), DateUnit.DAY);
        //}
        //createReqVO.setDialyzeAge(day + "");
        // 插入
        PatientDO patient = PatientConvert.INSTANCE.convert(createReqVO);
        patient.setSpellName(PinYinUtil.getFullSpell(patient.getName()));
        if (StrUtil.isNotEmpty(patient.getAvatar())) {
            String avatar = StrUtil.subAfter(patient.getAvatar(), staticPath, false);
            patient.setAvatar(avatar);
        }
        if (StrUtil.isNotEmpty(patient.getSignature())) {
            String sign = StrUtil.subAfter(patient.getSignature(), staticPath, false);
            patient.setSignature(sign);
        }
        patientMapper.insert(patient);
        // 新增患者检验检查
        renalProjectService.addRenalProject(patient.getId());

        //新增转入记录
        OutcomeRecordDO outcomeRecordDO = new OutcomeRecordDO();
        outcomeRecordDO.setPrognosisTime(DateUtil.date());
        outcomeRecordDO.setType("16");
        Map<String, Long> mottoSimpleMapByReturn = mottoSimpleMapper.selectList(MottoSimpleDO::getMottoId, 12, MottoSimpleDO::getPid, 16).stream().collect(Collectors.toMap(MottoSimpleDO::getPname, MottoSimpleDO::getId));
        if ("1".equals(createReqVO.getPatientSource())) { // 1-门诊 2-住院
            Long blood = mottoSimpleMapByReturn.getOrDefault("血透科", 0L);
            if (blood != null && blood > 0) {
                outcomeRecordDO.setClassify(String.valueOf(blood));
            }
        } else if ("2".equals(createReqVO.getPatientSource())) {
            Long blood = mottoSimpleMapByReturn.getOrDefault("住院", 0L);
            if (blood != null && blood > 0) {
                outcomeRecordDO.setClassify(String.valueOf(blood));
            }
        }
        outcomeRecordDO.setPatientId(patient.getId());
        outcomeRecordDO.setDialyzeNo(patient.getDialyzeNo());
        outcomeRecordDO.setPatientName(patient.getName());
        outcomeRecordDO.setPatientNickName(patient.getNickName());
        outcomeRecordMapper.insert(outcomeRecordDO);
        JkImplementationDO jkImplementationDO = JkImplementationDO.builder()
                .patientId(patient.getId())
                .patientName(patient.getName())
                .dialyzeNo(patient.getDialyzeNo())
                .pinyin(PinYinUtil.getFullSpell(patient.getName()))
                .patientType("0")
                .dialyzeTotal(0).cunt(0).state("0").binding("0").build();
        jkImplementationMapper.insert(jkImplementationDO);
        String synPatDept = redisTemplate.opsForValue().get("synPatDept");
        if (StringUtils.isNotEmpty(synPatDept) && synPatDept.contains(createReqVO.getDeptId().toString())) {

            PatientRespVO patientRespVO = new PatientRespVO();
            patientRespVO.setIdCard(createReqVO.getIdCard());
            patientRespVO.setName(createReqVO.getName());
            patientRespVO.setMobile(createReqVO.getMobile());
            patientRespVO.setInfect(org.springframework.util.StringUtils.isEmpty(createReqVO.getInfect()) ? "无":createReqVO.getInfect());
            patientRespVO.setBirthday(createReqVO.getBirthday());
            patientRespVO.setSex(createReqVO.getSex());
            patientRespVO.setFirstReceiveTime(createReqVO.getFirstReceiveTime());
            patientRespVO.setApplyWay(createReqVO.getApplyWay());
            patientRespVO.setDialyzeTotal(createReqVO.getDialyzeTotal());
            patientRespVO.setDeptId(createReqVO.getDeptId());
            patientRespVO.setDiseaseReasonNames(createReqVO.getDiseaseReasonNames());
            if (org.springframework.util.StringUtils.isEmpty(createReqVO.getDialyzeTotal())) {
                patientRespVO.setDialyzeTotal("0");
            }
            patientRespVO.setDiagnosis(createReqVO.getDescribes());
            patientRespVO.setAddress(createReqVO.getAddress());
            patientRespVO.setEnabledMark(1);

            Boolean result = synPatInfoToHis(patientRespVO);
        }
        // 返回
        return patient.getId();
    }

    private void checkName(String idCard) {
        Long count = patientMapper.selectCount(PatientDO::getIdCard, idCard);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.CHECK_NAME_EXITS);
        }
    }

    @Override
    public void updatePatient(PatientUpdateReqVO updateReqVO) {
//        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
//                .eqIfPresent(ArrangeClassesDO::getPatientId, updateReqVO.getId())
//                .eqIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()))
//                .eqIfPresent(ArrangeClassesDO::getTempType, 0));
//        if (StrUtil.isNotEmpty(updateReqVO.getEnterWay())) {
//            if (arrangeClassesDO == null) {
//                throw new ServiceException(GlobalErrorCodeConstants.ENTER_WAY_SAVE);
//            } else if (arrangeClassesDO.getState() < 2) {
//                throw new ServiceException(GlobalErrorCodeConstants.ENTER_WAY_SAVE);
//            }
//        }
        // 更新
        PatientDO updateObj = PatientConvert.INSTANCE.convert(updateReqVO);
        if (StrUtil.isNotEmpty(updateObj.getAvatar())) {
            String avatar = StrUtil.subAfter(updateObj.getAvatar(), staticPath, false);
            updateObj.setAvatar(avatar);
        }
        if (StrUtil.isNotEmpty(updateObj.getSignature())) {
            String sign = StrUtil.subAfter(updateObj.getSignature(), staticPath, false);
            updateObj.setSignature(sign);
        }
        if (StrUtil.isNotEmpty(updateObj.getName())) {
            try {
                updateObj.setSpellName(PinYinUtil.getFullSpell(updateObj.getName()));
            }catch (Exception e) {

            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(updateReqVO.getUpdateFlag()) && 1 == updateReqVO.getUpdateFlag()){
            updateObj.setUpdateTime(new Date());
        }
        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.getTodayRecord(updateObj.getId());
        if(StrUtil.isNotEmpty(updateObj.getHemodialysis()) || StrUtil.isNotEmpty(updateObj.getHemofiltration()) || StrUtil.isNotEmpty(updateObj.getPerfusion())) {
            // 更新透析记录的透析器
            //
            Boolean updateFlag = false;

            if (StrUtil.isNotEmpty(updateObj.getHemodialysis()) && hemodialysisManagerDO != null && StrUtil.isNotEmpty(hemodialysisManagerDO.getBloodFilter())) {
                hemodialysisManagerDO.setBloodFilter(updateObj.getHemodialysis());
                updateFlag= true;
            }
            if (StrUtil.isNotEmpty(updateObj.getHemofiltration()) &&hemodialysisManagerDO != null && StrUtil.isNotEmpty(hemodialysisManagerDO.getHemodialysisDevice())){
                hemodialysisManagerDO.setHemodialysisDevice(updateObj.getHemofiltration());
                updateFlag = true;
            }
            if (StrUtil.isNotEmpty(updateObj.getPerfusion())&&hemodialysisManagerDO != null && StrUtil.isNotEmpty(hemodialysisManagerDO.getPerfumer())){
                hemodialysisManagerDO.setPerfumer(updateObj.getPerfusion());
                updateFlag = true;
            }
            if (updateFlag) {
                hemodialysisManagerMapper.updateById(hemodialysisManagerDO);
            }
        }

        patientMapper.updateById(updateObj);
        if (!org.springframework.util.StringUtils.isEmpty(updateReqVO.getUpdateFlag()) &&1 == updateReqVO.getUpdateFlag()){
            FacilityDO facilityDO = facilityMapper.selectOne(FacilityDO::getId, updateReqVO.getAdjustNumber());
            if (!org.springframework.util.StringUtils.isEmpty(facilityDO)) {
                FacilityManagerDO facilityManagerDO = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityDO.getId());
                if (!org.springframework.util.StringUtils.isEmpty(facilityManagerDO)) {

                    String localDateTime = LocalDate.now().atStartOfDay().toString();
                    localDateTime = localDateTime.replace("T"," ");

                    // 更新排班机号
                    ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getPatientId, updateReqVO.getId())
                            .eqIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()))
                            .eqIfPresent(ArrangeClassesDO::getTempType, 0));

                    // 机号有患者
                    ArrangeClassesDO arrangeClassesDO1 = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(DateUtil.date()))
                            .eq(ArrangeClassesDO::getFacilityId, updateReqVO.getAdjustNumber())
                            .eq(ArrangeClassesDO::getDayState,arrangeClassesDO.getDayState())
                            .eqIfPresent(ArrangeClassesDO::getTempType, 0));
                    long loginIdAsLong = StpUtil.getLoginIdAsLong();
                    UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(loginIdAsLong);

                    if (!org.springframework.util.StringUtils.isEmpty(arrangeClassesDO) && !org.springframework.util.StringUtils.isEmpty(arrangeClassesDO1)){
                        HemodialysisManagerDO todayRecord = hemodialysisManagerMapper.getTodayRecord(arrangeClassesDO1.getPatientId());
                        // 交换机号
                        if (arrangeClassesDO1.getState() >1 && !org.springframework.util.StringUtils.isEmpty(todayRecord) && !org.springframework.util.StringUtils.isEmpty(todayRecord.getComputerNurse())
                            && !org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO) && !org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO.getComputerNurse()) && todayRecord.getComputerNurse().equals(hemodialysisManagerDO.getComputerNurse())) {

                        }else if (arrangeClassesDO1.getState() > 1 && arrangeClassesDO.getState() > 1) {
                            //throw new ServiceException(new ErrorCode(10010, "当前机号已存在透析中患者,且上机护士不同!"));
                        }
                        Long facilityId = arrangeClassesDO.getFacilityId();
                        Long facilitySubareaId = arrangeClassesDO.getFacilitySubareaId();
                        String facilityName = arrangeClassesDO.getFacilityName();
                        List<ArrangeClassesDO> arrangeClassesDOList = new ArrayList<>();
                        arrangeClassesDOList.add(arrangeClassesDO);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,1,0,0,adminUserInfo,null,1);
                        arrangeClassesDOList.clear();
                        arrangeClassesDO.setFacilityId(Long.valueOf(updateReqVO.getAdjustNumber()));
                        arrangeClassesDO.setFacilitySubareaId(facilityDO.getSubareaId());
                        arrangeClassesDO.setFacilityName(facilityDO.getCode());
                        // 先删除
                        arrangeClassesMapper.updateById(arrangeClassesDO);
                        arrangeClassesDOList.add(arrangeClassesDO);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,0,0,0,adminUserInfo,arrangeClassesDO1, 1);
                        arrangeClassesDOList.clear();

                        arrangeClassesDOList.add(arrangeClassesDO1);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,1,0,0,adminUserInfo,arrangeClassesDO, 1);

                        arrangeClassesDOList.clear();
                        arrangeClassesDO1.setFacilityId(facilityId);
                        arrangeClassesDO1.setFacilitySubareaId(facilitySubareaId);
                        arrangeClassesDO1.setFacilityName(facilityName);
                        // 查询被替换的患者透析记录机号

                        if (!org.springframework.util.StringUtils.isEmpty(todayRecord)) {
                            FacilityManagerDO facilityManager = facilityManagerMapper.selectOne(FacilityManagerDO::getFacilityId, facilityId);

                            hemodialysisManagerMapper.updateFacility(arrangeClassesDO1.getPatientId(), String.valueOf(facilityId),facilitySubareaId,facilityManager.getFacilityTypeId(),localDateTime);
                        }

                        arrangeClassesMapper.updateById(arrangeClassesDO1);
                        arrangeClassesDOList.add(arrangeClassesDO1);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,0,0,0,adminUserInfo,null, 1);

                    }else if(!org.springframework.util.StringUtils.isEmpty(arrangeClassesDO) && org.springframework.util.StringUtils.isEmpty(arrangeClassesDO1)) {
                        List<ArrangeClassesDO> arrangeClassesDOList = new ArrayList<>();
                        arrangeClassesDOList.add(arrangeClassesDO);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,1,0,0,adminUserInfo,null, 1);
                        arrangeClassesDOList.clear();
                        arrangeClassesDO.setFacilityId(Long.valueOf(updateReqVO.getAdjustNumber()));
                        arrangeClassesDO.setFacilitySubareaId(facilityDO.getSubareaId());
                        arrangeClassesDO.setFacilityName(facilityDO.getCode());
                        arrangeClassesMapper.updateById(arrangeClassesDO);
                        arrangeClassesDOList.add(arrangeClassesDO);
                        arrangeClassesService.saveArrangeRecord(arrangeClassesDOList,0,0,0,adminUserInfo,null, 1);
                    }else {
                        throw new ServiceException(new ErrorCode(10010, "当前机号已存在开始透析患者!"));
                    }

                    if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO)) {
                        hemodialysisManagerMapper.updateFacility(updateReqVO.getId(),updateReqVO.getAdjustNumber(),facilityDO.getSubareaId(),facilityManagerDO.getFacilityTypeId(),localDateTime);
                    }
                }
            }

        }

        String synPatDept = redisTemplate.opsForValue().get("synPatDept");
        if (StringUtils.isNotEmpty(synPatDept) && !org.springframework.util.StringUtils.isEmpty(updateReqVO.getDeptId())&& synPatDept.contains(updateReqVO.getDeptId().toString())) {
            PatientRespVO patientRespVO = new PatientRespVO();
            patientRespVO.setIdCard(updateReqVO.getIdCard());
            patientRespVO.setName(updateReqVO.getName());
            patientRespVO.setMobile(updateReqVO.getMobile());
            patientRespVO.setInfect(org.springframework.util.StringUtils.isEmpty(updateReqVO.getInfect()) ? "无":updateReqVO.getInfect());
            patientRespVO.setBirthday(updateReqVO.getBirthday());
            patientRespVO.setSex(updateReqVO.getSex());
            patientRespVO.setFirstReceiveTime(updateReqVO.getFirstReceiveTime());
            patientRespVO.setApplyWay(updateReqVO.getApplyWay());
            patientRespVO.setDialyzeTotal(updateReqVO.getDialyzeTotal());
            patientRespVO.setDeptId(updateReqVO.getDeptId());
            patientRespVO.setDiseaseReasonNames(updateReqVO.getDiseaseReasonNames());
            if (org.springframework.util.StringUtils.isEmpty(updateReqVO.getDialyzeTotal())) {
                patientRespVO.setDialyzeTotal("0");
            }
            patientRespVO.setDiagnosis(updateReqVO.getDescribes());
            patientRespVO.setAddress(updateReqVO.getAddress());
            patientRespVO.setEnabledMark(1);

            Boolean result = synPatInfoToHis(patientRespVO);
        }

    }

    @Override
    public void deletePatient(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        patientMapper.deleteBatchIds(idList);
    }


    @Override
    public PatientRespVO getPatient(Long id) {
        PatientDO patientDO = patientMapper.selectById(id);
        PatientRespVO patientRespVO = PatientConvert.INSTANCE.convert(patientDO);
        if (!org.springframework.util.StringUtils.isEmpty(patientDO)) {
            patientRespVO.setHemodialysis(null);
            patientRespVO.setHemofiltration(null);
            patientRespVO.setPerfusion(null);
        }
        if (patientRespVO != null && StrUtil.isNotEmpty(patientRespVO.getAvatar())) {
            patientRespVO.setAvatar(staticPath + patientRespVO.getAvatar());
        }
        if (patientRespVO != null && StrUtil.isNotEmpty(patientRespVO.getSignature())) {
            patientRespVO.setSignature(staticPath + patientRespVO.getSignature());
        }
        //诊断
        List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, id, DiseaseReasonDO::getSync, 1);
        if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
            String diseaseReasonNames = diseaseReasonDOS.stream().sorted(Comparator.comparing(DiseaseReasonDO::getSorted,Comparator.nullsLast(Integer::compareTo))).map(diseaseReasonDO -> {
                //如果是自定义有值取自定义，三级和二级有值取三级，三级有值取三级，二级有值取二级
                String str = "";
                if (StrUtil.isNotEmpty(diseaseReasonDO.getCustomName())) {
                    str = diseaseReasonDO.getCustomName();
                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentThreeName())) {
                    str = diseaseReasonDO.getParentThreeName();
                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentTwoName())) {
                    str = diseaseReasonDO.getParentTwoName();
                }
                return str;
            }).collect(Collectors.toList()).stream().collect(Collectors.joining(","));
            if (patientRespVO != null) patientRespVO.setDiseaseReasonNames(diseaseReasonNames);
        }
        //透析记录
        List<DialysisRecordDO> dialysisRecordDOS = dialysisRecordMapper.selectList(DialysisRecordDO::getPatientId, id);
        if (CollectionUtil.isNotEmpty(dialysisRecordDOS)) {
            if (patientDO != null && StrUtil.isNotBlank(patientDO.getInitDialyzeNo())) {
                Integer dialyzeTotal = Integer.parseInt(patientDO.getInitDialyzeNo()) + dialysisRecordDOS.size();
                patientRespVO.setDialyzeTotal(dialyzeTotal + "");
            }
        }
        HemodialysisManagerDO h = hemodialysisManagerMapper.getPatient(String.valueOf(id));

        //透析方案
        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, id);
        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
            if (patientRespVO != null) patientRespVO.setDialysisPlan("1");
        }
        if (com.thj.boot.common.utils.StringUtils.isNotNull(h)) {
            if (com.thj.boot.common.utils.StringUtils.isNotNull(h.getStartDialyzeTime())) {
                if (patientRespVO != null) patientRespVO.setStartDialyzeTime(h.getStartDialyzeTime());
            }
            if (com.thj.boot.common.utils.StringUtils.isNotNull(h.getEndDialyzeTime())) {
                if (patientRespVO != null) patientRespVO.setEndDialyzeTime(h.getEndDialyzeTime());
            }
            if (!org.springframework.util.StringUtils.isEmpty(h.getFacilityId())) {
                // 查询机号
                FacilityDO facilityDO = facilityMapper.selectById(h.getFacilityId());
                if (!org.springframework.util.StringUtils.isEmpty(facilityDO)) {
                    patientRespVO.setFacilityName(facilityDO.getCode());
                }
            }

        }
        if (!org.springframework.util.StringUtils.isEmpty(patientRespVO) && StringUtils.isEmpty(patientRespVO.getFacilityName())) {
            // 查询排班
            DateTime dateTime = DateUtil.beginOfDay(new Date());

            ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                    .eq(ArrangeClassesDO::getClassesTime, dateTime)
                    .eq(ArrangeClassesDO::getPatientId, id)
                    .eq(ArrangeClassesDO::getDeleted, 0)
                    .last("limit 1"));
            if (!org.springframework.util.StringUtils.isEmpty(arrangeClassesDO)) {
                patientRespVO.setFacilityName(arrangeClassesDO.getFacilityName());
            }
        }
        //合并症并发症
        List<DiseaseReasonDO> d =
                diseaseReasonMapper.selectList(new LambdaQueryWrapperX<DiseaseReasonDO>().eq(DiseaseReasonDO::getPatientId, id
                ).eq(DiseaseReasonDO::getSync, 1).in(DiseaseReasonDO::getDiagnosticType, 2, 3));
        if (CollectionUtil.isNotEmpty(d)) {
            String name = d.stream().map(diseaseReasonDO -> {
                return StrUtil.isNotEmpty(diseaseReasonDO.getOpinion()) ? diseaseReasonDO.getOpinion() : "";
            }).collect(Collectors.joining(","));
            if (patientRespVO != null) patientRespVO.setDiagnosis(name);
        }
        // 查询患者透前血压和体重
        ReachWeighDO reachWeighDO =  reachWeighMapper.selectOne(new LambdaQueryWrapperX<ReachWeighDO>()
                .eqIfPresent(ReachWeighDO::getPatientId, id)
                .gtIfPresent(ReachWeighDO::getRegisterTime,LocalDate.now().atTime(LocalTime.MIDNIGHT)));

        if (!org.springframework.util.StringUtils.isEmpty(reachWeighDO)) {
            if (patientRespVO != null) patientRespVO.setWeightPre(reachWeighDO.getDialyzeBeforeWeight());
            if (patientRespVO != null) patientRespVO.setPressurePre(reachWeighDO.getBpNoOne() + "/" + reachWeighDO.getBpNoTwo());
        }

        return patientRespVO;
    }


    @Override
    public List<PatientDO> getPatientList(Collection<Long> ids) {
        return patientMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PatientRespVO> getPatientPage(PatientPageReqVO pageReqVO) {
        if(!StringUtils.isEmpty(pageReqVO.getMore())) {
            String more = pageReqVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            pageReqVO.setSpellShort(join);

        }
        PageResult<PatientDO> patientDOPageResult = patientMapper.selectPage(pageReqVO);

        PageResult<PatientRespVO> patientRespVOPageResult = PatientConvert.INSTANCE.convertPage(patientDOPageResult);
        if (!org.springframework.util.StringUtils.isEmpty(pageReqVO.getStartTime()) && !org.springframework.util.StringUtils.isEmpty(pageReqVO.getEndTime())) {
            Date endTime = pageReqVO.getEndTime();
            endTime.setTime(endTime.getTime() + 24*60*60*1000 -1);
            pageReqVO.setEndTime(endTime);
        }
        if (CollectionUtil.isNotEmpty(patientRespVOPageResult.getList())) {
            List<PatientDO> list = patientDOPageResult.getList();
            if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
                for (PatientDO patientDO : list) {
                    // 计算年龄
                    if (!StringUtils.isEmpty(patientDO.getBirthday())) {
                        try {
                            int age = getTempAge(patientDO.getBirthday());
                            patientDO.setAge(age);

                        }catch (Exception e) {

                        }
                    }
                    // 计算透析龄
                    if (!org.springframework.util.StringUtils.isEmpty(patientDO.getFirstReceiveTime())) {
                        String dialysisTime = getDialysisTime(patientDO.getFirstReceiveTime());
                        patientDO.setDialyzeAge(dialysisTime);
                    }
                    if (!StringUtils.isEmpty(patientDO.getBirthday()) || !org.springframework.util.StringUtils.isEmpty(patientDO.getFirstReceiveTime())) {
                        patientMapper.updateAgeById(patientDO);
                    }
                    //dialysisAdviceMapper.selectList(new QueryWrapper<DialysisAdviceDO>(DialysisAdviceDO::))

                }
            }

            List<PatientRespVO> collect = patientRespVOPageResult.getList().stream().peek(patientRespVO -> {
                //透析总次数
//                Long count = dialysisRecordMapper.selectCount(DialysisRecordDO::getPatientId, patientRespVO.getId());
//                if (!org.springframework.util.StringUtils.isEmpty(pageReqVO.getStartTime()) && !org.springframework.util.StringUtils.isEmpty(pageReqVO.getEndTime())) {
//                    pageReqVO.setId(patientRespVO.getId());
//                    count = dialysisRecordMapper.selectCountByTime(pageReqVO);
//                }
                List<DialysisRecordDO> dialysisRecordDOS = dialysisRecordMapper.selectList(new LambdaQueryWrapperX<DialysisRecordDO>()
                        .eq(DialysisRecordDO :: getPatientId,patientRespVO.getId())
                        .betweenIfPresent(DialysisRecordDO::getCreateTime, pageReqVO.getStartTime(), pageReqVO.getEndTime())
                        .orderByDesc(DialysisRecordDO :: getEndDialyzeTime)
                );
                Long count = CollectionUtils.isNotEmpty(dialysisRecordDOS) ? Long.valueOf(dialysisRecordDOS.size()) : 0L;
                // 诊断
                List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, patientRespVO.getId(), DiseaseReasonDO::getSync, 1);
                if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
                    String diseaseReasonNames = diseaseReasonDOS.stream().map(diseaseReasonDO -> {
                        //如果是自定义有值取自定义，三级和二级有值取三级，三级有值取三级，二级有值取二级
                        String str = "";
                        if (StrUtil.isNotEmpty(diseaseReasonDO.getCustomName())) {
                            str = diseaseReasonDO.getCustomName();
                        } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentThreeName())) {
                            str = diseaseReasonDO.getParentThreeName();
                        } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentTwoName())) {
                            str = diseaseReasonDO.getParentTwoName();
                        }
                        return str;
                    }).collect(Collectors.toSet()).stream().collect(Collectors.joining(","));
                    patientRespVO.setDiseaseReasonNames(diseaseReasonNames);
                }

                patientRespVO.setDialyzeTotal(count + "");
                //头像
                if (StrUtil.isNotEmpty(patientRespVO.getAvatar())) {
                    patientRespVO.setAvatar(staticPath + patientRespVO.getAvatar());
                }
                if (StrUtil.isNotEmpty(patientRespVO.getSignature())) {
                    patientRespVO.setSignature(staticPath + patientRespVO.getSignature());
                }
                //标签管理
                if (StrUtil.isNotEmpty(patientRespVO.getLabels())) {
                    List<Long> idList = Arrays.stream(patientRespVO.getLabels().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getId, idList);
                    if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                        patientRespVO.setLabels(mottoSimpleDOS.stream().map(MottoSimpleDO::getPname).collect(Collectors.joining(",")));
                    }
                }
                //排班 0-未排班，1-已排班
                /*List<TeamPatientDO> teamPatientDOS = teamPatientMapper.selectList(TeamPatientDO::getPatientId, patientRespVO.getId(), TeamPatientDO::getTempType, 0);
                if (CollectionUtil.isNotEmpty(teamPatientDOS)) {
                    patientRespVO.setScheduling("1");
                } else {
                    patientRespVO.setScheduling("0");
                }*/
                //0-无方案，1-有方案
                List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, patientRespVO.getId());
                if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
                    patientRespVO.setDialysisPlan("1");
                } else {
                    patientRespVO.setDialysisPlan("0");
                }

                //在中心首次治疗时间 最后一次治疗时间
//                List<HemodialysisManagerDO> hemodialysisManagerDOS = hemodialysisManagerMapper.selectList(new LambdaQueryWrapperX<HemodialysisManagerDO>().eq(HemodialysisManagerDO::getPatientId, patientRespVO.getId()).orderByDesc(HemodialysisManagerDO::getHemodialysisTime));
                if (!org.springframework.util.CollectionUtils.isEmpty(dialysisRecordDOS)) {
                    patientRespVO.setLastCureTime(dialysisRecordDOS.get(0).getEndDialyzeTime());
                    patientRespVO.setFirstCureTime(dialysisRecordDOS.get(dialysisRecordDOS.size() -1).getEndDialyzeTime());
                }
                //转出时间
                if ("01".equals(patientRespVO.getPatientTypeSource())) {
                    List<OutcomeRecordDO> outcomeRecordDOS = outcomeRecordMapper.selectList(new LambdaQueryWrapperX<OutcomeRecordDO>().eq(OutcomeRecordDO::getPatientId, patientRespVO.getId()).orderByDesc(OutcomeRecordDO::getPrognosisTime));

                    if (!org.springframework.util.CollectionUtils.isEmpty(outcomeRecordDOS)) {
                        patientRespVO.setOutTime(outcomeRecordDOS.get(0).getPrognosisTime());
                    }
                }

                // 最近一次转入时间
                Map<String, String> convertMap = mottoService.getMottoSimpleMap();
                List<OutcomeRecordDO> outcomeRecordDOS = outcomeRecordMapper.selectList(
                        new LambdaQueryWrapperX<OutcomeRecordDO>()
                                .eq(OutcomeRecordDO::getPatientId, patientRespVO.getId())
                                .eq(OutcomeRecordDO::getType, convertMap.get("转入"))
                                .orderByDesc(OutcomeRecordDO::getPrognosisTime)
                );
                if (!org.springframework.util.CollectionUtils.isEmpty(outcomeRecordDOS)) {
                    patientRespVO.setLatestReceiveTime(outcomeRecordDOS.get(0).getPrognosisTime());
                }



            }).filter(patientRespVO -> {
                if (pageReqVO.getDeptId() != null) {
                    return patientRespVO.getDeptId().equals(pageReqVO.getDeptId());
                }
                return true;
            }).collect(Collectors.toList());
            if (StrUtil.isNotEmpty(pageReqVO.getScheduling()) && StrUtil.isNotEmpty(pageReqVO.getDialysisPlan())) {
                collect = collect.stream().filter(patientRespVO -> pageReqVO.getScheduling().equals(patientRespVO.getScheduling()) && pageReqVO.getDialysisPlan().equals(patientRespVO.getDialysisPlan())).collect(Collectors.toList());
                patientRespVOPageResult.setList(collect);
            } else if (StrUtil.isNotEmpty(pageReqVO.getScheduling())) {
                collect = collect.stream().filter(patientRespVO -> pageReqVO.getScheduling().equals(patientRespVO.getScheduling())).collect(Collectors.toList());
                patientRespVOPageResult.setList(collect);
            } else if (StrUtil.isNotEmpty(pageReqVO.getDialysisPlan())) {
                collect = collect.stream().filter(patientRespVO -> pageReqVO.getDialysisPlan().equals(patientRespVO.getDialysisPlan())).collect(Collectors.toList());
                patientRespVOPageResult.setList(collect);
            } else {
                patientRespVOPageResult.setList(collect);
            }
        }
        return patientRespVOPageResult;
    }

    private String getDialysisTime(Date firstReceiveTime) {
        LocalDate now = LocalDate.now();
        LocalDate localDate = firstReceiveTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Period between = Period.between(localDate, now);
        String time = between.getYears() + "年零" + between.getMonths() + "月";
        return time;

    }

    private int getTempAge(String birthday) {
        String[] split = birthday.split("-");
        int year = Integer.parseInt(split[0]);
        int month = Integer.parseInt(split[1]);
        int day = Integer.parseInt(split[2]);
        LocalDate of = LocalDate.of(year, month, day);
        LocalDate now = LocalDate.now();
        int years = Period.between(of, now).getYears();
        return years;
    }

    @Override
    public List<PatientRespVO> getPatientList(PatientCreateReqVO createReqVO,Long systemDeptId) {
        if(!org.springframework.util.StringUtils.isEmpty(createReqVO.getMore())) {
            String more = createReqVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            createReqVO.setMore(join);
        }
        List<PatientDO> patientDOS = patientMapper.selectList(createReqVO);

        List<PatientRespVO> patientRespVOS = PatientConvert.INSTANCE.convertList(patientDOS);
        if (!org.springframework.util.CollectionUtils.isEmpty(patientDOS)) {
            // 获取患者责任护士
            List<UserRespDTO> nurse = usersApi.getUserByRole("nurse", systemDeptId.toString());
            nurse.forEach(UserRespDTO ->{
                JkDivisionRespVO vo = new JkDivisionRespVO();
                vo.setName(UserRespDTO.getNickname());
                vo.setId(UserRespDTO.getId());
                List<PatientDivisionVo> divisionRespVOS = jkDivisionMapper.queryPatient(vo.getId());
                if (!org.springframework.util.CollectionUtils.isEmpty(divisionRespVOS)) {
                    patientRespVOS.forEach(patientRespVO -> {
                        divisionRespVOS.forEach(patientDivisionVo -> {
                            if (patientRespVO.getId().equals(patientDivisionVo.getPatientId())) {
                                patientRespVO.setNurseName(UserRespDTO.getNickname());
                            }
                        });
                    });
                }
            });
        }
        return patientRespVOS;
    }

    @Override
    public String exportPatientPDF(PatientCreateReqVO createReqVO, HttpServletResponse response) {
        //根据患者id获取详情信息
        PatientDO patientDO = patientMapper.selectById(createReqVO.getId());
        if (patientDO != null) {
            OutputStream outputStream = null;
            try {
                outputStream = response.getOutputStream();
                //获得浏览器代理信息
                //final String userAgent = request.getHeader("USER-AGENT");
                //判断浏览器代理并分别设置响应给浏览器的编码格式
                //String finalFileName = null;
                //if (StringUtils.contains(userAgent, "MSIE") || StringUtils.contains(userAgent, "Trident")) {
                //    //IE浏览器
                //    finalFileName = URLEncoder.encode(fileName, "UTF8");
                //} else if (StringUtils.contains(userAgent, "Mozilla")) {
                //    //google,火狐浏览器
                //    finalFileName = new String(fileName.getBytes(), "ISO8859-1");
                //} else {
                //    //其他浏览器
                //    finalFileName = URLEncoder.encode(fileName, "UTF8");
                //}
                //response.setContentType("application/x-download");
                //response.setHeader("Content-Disposition", "attachment; filename=" + new String("patient.pdf".getBytes(), "ISO-8859-1"));
                outputStream = new FileOutputStream(new File(environment.getProperty("patient.profile") + "patientManager.pdf"));
                //读取磁盘的模版文件
                InputStream inputStream = new FileInputStream(new File(environment.getProperty("patient.patientPdf")));
                //读取pdf模板
                PdfReader reader = new PdfReader(inputStream);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                PdfStamper stamper = new PdfStamper(reader, bos);

                // 获得pdf模板中的文字域
                AcroFields form = stamper.getAcroFields();

                // 设置文字样式
                BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                ArrayList<BaseFont> fonts = new ArrayList<>();
                fonts.add(baseFont);
                form.setSubstitutionFonts(fonts);
                //获取患者类型字典数据
                DictDataRespDTO patientType = dictDataApi.getDictData(DictEnum.DICT_TYPE.getType(), patientDO.getPatientType());
                //患者来源
                DictDataRespDTO patientSource = dictDataApi.getDictData(DictEnum.PATIENT_SOURCE.getType(), patientDO.getPatientType());

                // 设置填充的文字内容
                Map<String, Object> map = new HashMap<>();
                map.put("time", DateUtil.now());


                map.put("patientType", patientType == null ? null : patientType.getLabel());
                map.put("dialyzeNo", patientDO.getDialyzeNo());
                map.put("hospitalNo", patientDO.getHospitalNo());
                map.put("patientSource", patientSource == null ? null : patientSource.getLabel());
                map.put("endemicArea", "46465465");
                map.put("bedNo", "46465465");
                map.put("name", "46465465");
                map.put("sex", "46465465");
                map.put("marriage", "46465465");
                map.put("idCard", "46465465");
                map.put("birthday", "46465465");
                map.put("age", "46465465");
                map.put("applyWay", "46465465");
                map.put("baoNo", "46465465");
                map.put("baoEndTime", "46465465");
                map.put("baoEndWarnTime", "46465465");
                map.put("stature", "46465465");
                map.put("initWeight", "46465465");
                map.put("bloodType", "46465465");
                map.put("rh", "46465465");
                map.put("culture", "46465465");
                map.put("occupation", "46465465");
                map.put("smoking", "46465465");
                map.put("eyesight", "46465465");
                map.put("tipple", "46465465");
                map.put("religion", "46465465");
                map.put("nation", "46465465");
                map.put("allergy", "46465465");
                map.put("labels", "46465465");
                map.put("mobile", "46465465");
                map.put("mobileMan", "46465465");
                map.put("address", "46465465");
                map.put("workUnit", "46465465");
                map.put("receiveTime", "46465465");
                map.put("firstReceiveTime", "46465465");
                map.put("dialyzeAge", "46465465");
                map.put("induceTime", "46465465");
                map.put("initDialyzeNo", "46465465");
                map.put("dialyzeTotal", "46465465");
                map.put("infect", "46465465");
                map.put("medic", "46465465");
                map.put("nurse", "46465465");
                map.put("describes", "46465465");
                // 填充pdf模板中的文本域
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    form.setField(entry.getKey(), entry.getValue() == null ? null : entry.getValue() + "");
                }
                // 如果为false，生成的PDF文件可以编辑，如果为true，生成的PDF文件不可以编辑
                stamper.setFormFlattening(true);
                stamper.close();
                //创建空的文档，并将生产的pdf复制到文档中
                Document doc = new Document();
                PdfCopy copy = new PdfCopy(doc, outputStream);
                doc.open();
                PdfImportedPage importPage = null;
                for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                    importPage = copy.getImportedPage(new PdfReader(bos.toByteArray()), i);
                    copy.addPage(importPage);
                }
                // 关闭文档
                doc.close();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (DocumentException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (outputStream != null) {
                        outputStream.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return environment.getProperty("patient.staticPatternPath") + "/" + "patientManager.pdf";
        }
        return null;
    }

    @Override
    public String getPcQrCode(HttpServletResponse response, Long patientId) {
        return wxPublicNoHandle.wxLoginPage(response, patientId);
    }

    @Override
    public List<ArrangeClassesRespVO> getTeamPatientList(Long patientId) {
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(ArrangeClassesDO::getPatientId, patientId);
        List<ArrangeClassesRespVO> arrangeClassesRespVOList = ArrangeClassesConvert.INSTANCE.convertList(arrangeClassesDOS);
        if (CollectionUtil.isNotEmpty(arrangeClassesRespVOList)) {
            arrangeClassesRespVOList = arrangeClassesRespVOList.stream().peek(arrangeClassesRespVO -> {
                //日期=当前第几周+日期+周几
                Week week = DateUtil.dayOfWeekEnum(arrangeClassesRespVO.getClassesTime());
                arrangeClassesRespVO.setDateStr(DateUtil.weekOfYear(arrangeClassesRespVO.getClassesTime()) + "周"
                        + "(" + DateUtil.format(arrangeClassesRespVO.getClassesTime(), DatePattern.NORM_DATE_PATTERN) + ")" +
                        week.toChinese("周"));
                //班次
                arrangeClassesRespVO.setWeekDay("a".equals(arrangeClassesRespVO.getDayState()) ? "上午" : "b".equals(arrangeClassesRespVO.getDayState()) ? "下午" : "晚上");
                //病区
                FacilitySubareaDO facilitySubareaDO = facilitySubareaMapper.selectById(arrangeClassesRespVO.getFacilitySubareaId());
                arrangeClassesRespVO.setFacilitySubareaName(facilitySubareaDO == null ? null : facilitySubareaDO.getName());
                //设备
                FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesRespVO.getFacilityId());
                arrangeClassesRespVO.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
            }).collect(Collectors.toList());

        }
        return arrangeClassesRespVOList;
    }


    @Override
    public List<PatientRespVO> getPatientListApp(PatientCreateReqVO createReqVO) {
        //查询已排班患者
        if (CollectionUtil.isNotEmpty(createReqVO.getDayState())) {
            List<ArrangeClassesDO> arrangeClassesDOS = null;
            if (StringUtils.isNotNull(createReqVO.getComputerNurse())) {
                PatientPageReqVO pageVO = new PatientPageReqVO();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                if(StringUtils.isEmpty(createReqVO.getQueryDate())){
                    pageVO.setDays(createReqVO.getQueryDate());
                }else{
                    String format = sdf.format(new Date());
                    pageVO.setDays(format);
                }

                pageVO.setComputerNurse(createReqVO.getComputerNurse());
                pageVO.setDayState(createReqVO.getDayState());

                arrangeClassesDOS = hemodialysisManagerMapper.getMyPatient(pageVO);
            } else{
                LambdaQueryWrapperX<ArrangeClassesDO> queryWrapperX = new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .inIfPresent(ArrangeClassesDO::getDayState, createReqVO.getDayState())
                        .inIfPresent(ArrangeClassesDO::getFacilitySubareaId, createReqVO.getEndemicAreas());
                if(StringUtils.isEmpty(createReqVO.getQueryDate())){
                    queryWrapperX.betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()));
                }else{
                    DateTime parse = DateUtil.parse(createReqVO.getQueryDate(), "yyyy-MM-dd");
                    queryWrapperX.eq(ArrangeClassesDO::getClassesTime, parse);
                }

                arrangeClassesDOS = arrangeClassesMapper.selectList(queryWrapperX);
            }
            if (CollectionUtil.isEmpty(arrangeClassesDOS)) {
                return null;
            }
            Set<Long> patientIdList = arrangeClassesDOS.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toSet());
            createReqVO.setPatientIds(patientIdList);
        }

        createReqVO.setDayState(null);
        createReqVO.setEndemicAreas(null);
        List<PatientDO> patientDOS = patientMapper.selectList(createReqVO);
        List<PatientRespVO> patientRespVOS = PatientConvert.INSTANCE.convertList(patientDOS);
        if (CollectionUtil.isNotEmpty(patientRespVOS)) {
            patientRespVOS = patientRespVOS.stream().filter(patientRespVO -> "00".equals(patientRespVO.getPatientTypeSource())).peek(patientRespVO -> {
                //头像
                if (StrUtil.isNotEmpty(patientRespVO.getAvatar())) {
                    patientRespVO.setAvatar(staticPath + patientRespVO.getAvatar());
                }
                if (StrUtil.isNotEmpty(patientRespVO.getSignature())) {
                    patientRespVO.setSignature(staticPath + patientRespVO.getSignature());
                }
                patientRespVO.setAvatar(null);
                //未执行医嘱头像加标签
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eqIfPresent(DialysisAdviceDO::getPatientId, patientRespVO.getId())
                        .eqIfPresent(DialysisAdviceDO::getType, "0")
                        .eqIfPresent(DialysisAdviceDO::getDateWeek, DateUtil.beginOfDay(new Date()))
                        .select(DialysisAdviceDO::getState));
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                    List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> "0".equals(dialysisAdviceDO.getState())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(collect1)) {
                        patientRespVO.setCarryState(0);
                    } else {
                        patientRespVO.setCarryState(1);
                    }
                }
                //如果透析处方确认就显示透析信息否则显示排班信息
                HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(HemodialysisManagerDO::getPatientId, patientRespVO.getId(), HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(DateUtil.date()));
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                        .eqIfPresent(ArrangeClassesDO::getPatientId, patientRespVO.getId())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0).last("limit 1"));
                if (hemodialysisManagerDO != null && arrangeClassesDO != null) {
                    patientRespVO.setClassesState(arrangeClassesDO.getState());
                    patientRespVO.setPrescriptionState(1);
                    //机号
                    FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
                    patientRespVO.setFacilityId(facilityDO == null ? null:facilityDO.getId());
                    patientRespVO.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
                    if (StrUtil.isNotEmpty(hemodialysisManagerDO.getDialyzeWayValue())) {
                        DictDataRespDTO dictData = dictDataApi.getDictData("dialyze_way", hemodialysisManagerDO.getDialyzeWayValue());
                        patientRespVO.setDialyzeName(dictData == null ? null : dictData.getLabel());
                    }
                    //透析模式
                } else {
                    patientRespVO.setPrescriptionState(0);
                    if (arrangeClassesDO != null) {
                        //机号
                        FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
                        patientRespVO.setFacilityId(facilityDO == null ? null:facilityDO.getId());
                        patientRespVO.setFacilityName(facilityDO == null ? null : facilityDO.getCode());
                        //状态 0-未签到，1-已签到，2-透析中，3-透析后,4自查，5-归档，6已排班，7未排班
                        patientRespVO.setClassesState(arrangeClassesDO.getState());
                        //透析模式
                        patientRespVO.setDialyzeName(arrangeClassesDO.getDialysisName());
                    } else {
                        patientRespVO.setClassesState(7);
                        patientRespVO.setFacilityId(0L);
                    }
                }

            }).sorted((a, b) -> a.getClassesState() - b.getClassesState()).sorted(Comparator.comparing(PatientRespVO::getFacilityId)).collect(Collectors.toList());
        }
        return patientRespVOS;
    }

    @Override
    public PageResult<PatientRespVO> getPatientPageApp(PatientPageReqVO pageVO) {
        //查询已排班患者
        List<ArrangeClassesDO> arrangeClassesDOS = null;
        if (CollectionUtil.isNotEmpty(pageVO.getDayState())) {

            if (StringUtils.isNotNull(pageVO.getComputerNurse())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String format = sdf.format(new Date());
                pageVO.setDays(format);

                arrangeClassesDOS = hemodialysisManagerMapper.getMyPatient(pageVO);
            } else {
                arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .inIfPresent(ArrangeClassesDO::getDayState, pageVO.getDayState())
                        .inIfPresent(ArrangeClassesDO::getFacilitySubareaId, pageVO.getEndemicAreas()));
            }

            if (CollectionUtil.isEmpty(arrangeClassesDOS)) {
                return null;
            }
            if (!org.springframework.util.StringUtils.isEmpty(pageVO.getSortType())) {
                if (pageVO.getSortType().equals("2")){
                    arrangeClassesDOS = arrangeClassesDOS.stream().map(arrangeClassesDO -> {
                        if (org.springframework.util.StringUtils.isEmpty(arrangeClassesDO.getRegisterTime())) {
                            arrangeClassesDO.setRegisterTime(DateUtil.endOfDay(new Date()));
                        }
                        return arrangeClassesDO;
                    }).sorted(Comparator.comparing(ArrangeClassesDO::getRegisterTime)).collect(Collectors.toList());
                }

            }
            List<Long> patientIdList = arrangeClassesDOS.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toList());
            pageVO.setPatientIdList(patientIdList);

        }
        pageVO.setDayState(null);
        pageVO.setEndemicAreas(null);
        //查询患者
        if (!org.springframework.util.StringUtils.isEmpty(pageVO.getMore())) {
            String more = pageVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            pageVO.setMore(join);
        }

        PageResult<PatientDO> patientDOPageResult = patientMapper.selectPage(pageVO);
        PageResult<PatientRespVO> patientRespVOPageResult = PatientConvert.INSTANCE.convertPage(patientDOPageResult);
        if (CollectionUtil.isNotEmpty(patientRespVOPageResult.getList())) {
            List<PatientFlags> patientFlags = patientMapper.selectPatientFlags(patientRespVOPageResult.getList());
            Map<Long,PatientFlags> patientFlagsMap = patientFlags.stream().collect(Collectors.toMap(PatientFlags::getPatientId,Function.identity()));
            Stream<PatientRespVO> patList = patientRespVOPageResult.getList().stream()
                    .filter(patientRespVO -> "00".equals(patientRespVO.getPatientTypeSource())).peek(patientRespVO -> {
                        //头像
                        if (StrUtil.isNotEmpty(patientRespVO.getAvatar())) {
                            patientRespVO.setAvatar(staticPath + patientRespVO.getAvatar());
                        }
                        if (StrUtil.isNotEmpty(patientRespVO.getSignature())) {
                            patientRespVO.setSignature(staticPath + patientRespVO.getSignature());
                        }
                        patientRespVO.setAvatar(null);
                        //未执行医嘱头像加标签
                        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                                .eqIfPresent(DialysisAdviceDO::getPatientId, patientRespVO.getId())
                                .eqIfPresent(DialysisAdviceDO::getType, "0")
                                .eqIfPresent(DialysisAdviceDO::getDateWeek, DateUtil.beginOfDay(new Date()))
                                .select(DialysisAdviceDO::getState));
                        if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                            List<DialysisAdviceDO> collect1 = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> "0".equals(dialysisAdviceDO.getState())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(collect1)) {
                                patientRespVO.setCarryState(0);
                            } else {
                                patientRespVO.setCarryState(1);
                            }
                        }
                        //调整机号
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);
//                        List<DiseaseReasonDO> diseaseReasonDOs =
//                                diseaseReasonMapper.selectList(new LambdaQueryWrapper<DiseaseReasonDO>()
//                                .eq(DiseaseReasonDO::getDialyzeNo,patientRespVO.getDialyzeNo())
//                                .orderByDesc(DiseaseReasonDO::getReasonTime)
//                                );
//                        if(patientRespVO != null)
                        patientRespVO.setTunnelCuffedFlag(patientFlagsMap.get(patientRespVO.getId()).getTunnelCuffedFlag());
                        patientRespVO.setDiabetesFlag(patientFlagsMap.get(patientRespVO.getId()).getDiabetesFlag());
                        patientRespVO.setNoCuffFlag(patientFlagsMap.get(patientRespVO.getId()).getNoCuffFlag());
                        patientRespVO.setNoHeparinFlag(patientFlagsMap.get(patientRespVO.getId()).getNoHeparinFlag());
//                        patientRespVO.setParentThreeId(diseaseReasonDOs != null && diseaseReasonDOs.size() > 0 ? (diseaseReasonDOs.get(0) != null && diseaseReasonDOs.get(0).getParentThreeId() != null ? Long.valueOf(diseaseReasonDOs.get(0).getParentThreeId()) : -1L) : -1L);
                        //如果透析处方确认就显示透析信息否则显示排班信息
                        HemodialysisManagerDO hemodialysisManagerDO = hemodialysisManagerMapper.selectOne(HemodialysisManagerDO::getPatientId, patientRespVO.getId(), HemodialysisManagerDO::getHemodialysisTime, DateUtil.beginOfDay(DateUtil.date()));
                        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                                .eqIfPresent(ArrangeClassesDO::getPatientId, patientRespVO.getId())
                                .eqIfPresent(ArrangeClassesDO::getTempType, 0).last("limit 1"));
                        // 获取的时间就是当天的零点时间
                        if (org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO)) {
                            Date midnight = calendar.getTime();
                            if (StrUtil.isNotEmpty(patientRespVO.getAdjustNumber()) && patientRespVO.getUpdateTime().after(midnight)) {
                                FacilityDO facilityDO1 = facilityMapper.selectById(patientRespVO.getAdjustNumber());
                                patientRespVO.setAdjustNumber(facilityDO1 != null ? facilityDO1.getCode() : null);
                            } else {
                                patientRespVO.setAdjustNumber(null);
                            }
                        }

                        patientRespVO.setSort(0);
                        if (hemodialysisManagerDO != null && arrangeClassesDO != null) {
                            patientRespVO.setClassesTime(arrangeClassesDO.getClassesTime());
                            patientRespVO.setClassesState(arrangeClassesDO.getState());
                            patientRespVO.setPrescriptionState(hemodialysisManagerDO.getPrescriptionState());
                            patientRespVO.setWeekState(arrangeClassesDO.getWeekState());
                            patientRespVO.setDayState(arrangeClassesDO.getDayState());
                            //机号
                            FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
                            if (facilityDO != null) {
                                patientRespVO.setFacilityName(facilityDO.getCode());
                                patientRespVO.setFacilityId(facilityDO.getId());
                                patientRespVO.setSort(facilityDO.getSort());
                            }
                            if (!org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO) && !org.springframework.util.StringUtils.isEmpty(hemodialysisManagerDO.getFacilityId())) {
                                patientRespVO.setFacilityId(hemodialysisManagerDO.getFacilityId());
                                FacilityDO facilityDO2 = facilityMapper.selectById(hemodialysisManagerDO.getFacilityId());
                                if (facilityDO2 != null) {
                                    patientRespVO.setFacilityName(facilityDO2.getCode());
                                    patientRespVO.setSort(facilityDO2.getSort());
                                }
                            }
                            //透析模式
                            if (StrUtil.isNotEmpty(hemodialysisManagerDO.getDialyzeWayValue())) {
                                DictDataRespDTO dictData = dictDataApi.getDictData("dialyze_way", hemodialysisManagerDO.getDialyzeWayValue());
                                if (dictData != null) {
                                    patientRespVO.setDialyzeName(dictData.getLabel());
                                    patientRespVO.setDialyzeValue(dictData.getValue());
                                }
                            }
                            UseRegisterDO useRegisterDO = useRegisterMapper.selectOne(new LambdaQueryWrapperX<UseRegisterDO>()
                                    .eqIfPresent(UseRegisterDO::getPatientId, patientRespVO.getId())
                                    .eqIfPresent(UseRegisterDO::getUseTime, patientRespVO.getClassesTime()));
                            patientRespVO.setUseRegisterId(useRegisterDO != null ? useRegisterDO.getId() : null);
                            if (StringUtils.isNotNull(useRegisterDO) && StringUtils.isNotNull(useRegisterDO.getStartTime())) {
                                SimpleDateFormat sf1 = new SimpleDateFormat("HH:mm");
                                patientRespVO.setDisinfectTime(sf1.format(useRegisterDO.getStartTime()));
                            }

                            //设置透析器耗材型号
                            String consumableConsumSpec = getConsumableConsumSpec(hemodialysisManagerDO.getHemodialysisDevice(),
                                    hemodialysisManagerDO.getBloodFilter(), hemodialysisManagerDO.getPerfumer());
                            if (StringUtils.isNotEmpty(consumableConsumSpec)) {
                                patientRespVO.setModelConsumableSpec(consumableConsumSpec);
                            } else {
                                //如果是未确认处方就开始透析
                                String patientModelConsumableSpec = getPatientModelConsumableSpec(arrangeClassesDO);
                                patientRespVO.setModelConsumableSpec(patientModelConsumableSpec);
                            }

                            //计算透析结束时间
                            if (hemodialysisManagerDO.getStartDialyzeTime() != null) {
                                double durationMin = 0;
                                if (StringUtils.isNotEmpty(hemodialysisManagerDO.getDuration())) {
                                    durationMin += Double.valueOf(hemodialysisManagerDO.getDuration()) * 60;
                                }
                                if (StringUtils.isNotEmpty(hemodialysisManagerDO.getDurationMin())) {
                                    durationMin += Double.valueOf(hemodialysisManagerDO.getDurationMin());
                                }
                                DateTime endDateTime = DateUtil.offsetMinute(hemodialysisManagerDO.getStartDialyzeTime(), (int) durationMin);
                                patientRespVO.setFinishDialyzeTime(endDateTime);
                            }

                        } else {
                            patientRespVO.setPrescriptionState(0);
                            if (arrangeClassesDO != null) {
                                //设置透析器型号
                                String patientModelConsumableSpec = getPatientModelConsumableSpec(arrangeClassesDO);
                                patientRespVO.setModelConsumableSpec(patientModelConsumableSpec);

                                patientRespVO.setClassesTime(arrangeClassesDO.getClassesTime());
                                patientRespVO.setWeekState(arrangeClassesDO.getWeekState());
                                patientRespVO.setDayState(arrangeClassesDO.getDayState());
                                patientRespVO.setSort(null);
                                //机号
                                FacilityDO facilityDO = facilityMapper.selectById(arrangeClassesDO.getFacilityId());
                                if (facilityDO != null) {
                                    patientRespVO.setFacilityName(facilityDO.getCode());
                                    patientRespVO.setFacilityId(facilityDO.getId());
                                    patientRespVO.setSort(facilityDO.getSort());
                                }
                                //状态 0-未签到，1-已签到，2-透析中，3-透析后,4自查，5-归档，6已排班，7未排班
                                patientRespVO.setClassesState(arrangeClassesDO.getState());
                                //透析模式
                                patientRespVO.setDialyzeName(arrangeClassesDO.getDialysisName());
                                patientRespVO.setDialyzeValue(arrangeClassesDO.getDialysisValue());
                            } else {
                                patientRespVO.setClassesState(7);
                                patientRespVO.setFacilityId(0L);
                            }
                        }

                    });
            List<PatientRespVO> collect;

            if (!org.springframework.util.StringUtils.isEmpty(pageVO.getSortType()) && !org.springframework.util.CollectionUtils.isEmpty(arrangeClassesDOS)) {
                if (pageVO.getSortType().equals("2")){
                    List<PatientRespVO> collect1 = patList.collect(Collectors.toList());
                    List<PatientRespVO> tempList = new ArrayList<>();
                    arrangeClassesDOS.stream().forEach(arrangeClassesDO -> {
                        collect1.stream().forEach(patientRespVO -> {
                            if (patientRespVO.getId().equals(arrangeClassesDO.getPatientId())) {
                                tempList.add(patientRespVO);
                            }
                        });
                    });

                    collect = tempList;
                }else {
                    collect = patList.sorted(Comparator.comparing(PatientRespVO::getSort,Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                }

            }else {
                collect = patList.sorted(Comparator.comparing(PatientRespVO::getSort,Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
            }


            patientRespVOPageResult.setList(collect);
        }
        return patientRespVOPageResult;
    }

    private String getPatientModelConsumableSpec(ArrangeClassesDO arrangeClassesDO) {
        //通过透析模式字典值和患者id查询透析方案id
        QueryWrapper<DialyzeOptionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DialyzeOptionDO::getPatientId, arrangeClassesDO.getPatientId())
                .eq(DialyzeOptionDO::getDialyzeDictValue, arrangeClassesDO.getDialysisValue())
                .last("limit 1");
        DialyzeOptionDO dialyzeOptionDO = dialyzeOptionMapper.selectOne(queryWrapper);
        if(dialyzeOptionDO != null){
            //根据透析方案id和患者id查询透析方案详情
            DialysisProtocolDO dialysisProtocolDO = dialysisProtocolMapper.selectOne(new LambdaQueryWrapperX<DialysisProtocolDO>()
                    .eqIfPresent(DialysisProtocolDO::getPatientDialyzeId, dialyzeOptionDO.getId())
                    .eqIfPresent(DialysisProtocolDO::getPatientId, dialyzeOptionDO.getPatientId())
                    .eqIfPresent(DialysisProtocolDO::getProtocolType, 1));
            if(dialysisProtocolDO != null){
                String consumableConsumSpec = getConsumableConsumSpec(dialysisProtocolDO.getHemodialysisDevice(),
                        dialysisProtocolDO.getBloodFilter(), dialysisProtocolDO.getPerfume());
                return consumableConsumSpec;
            }
        }
        return ";";
    }

    /**
     * 获取耗材名称
     * @param hemodialysisDevice
     * @param bloodFilter
     * @param perfumer
     * @return
     */
    private String getConsumableConsumSpec(String hemodialysisDevice, String bloodFilter, String perfumer){
        List<String> specList = new ArrayList<>();
        if(StringUtils.isNotEmpty(hemodialysisDevice)){
            HisConsumablesDO hisConsumables = hisConsumablesService.getHisConsumables(Long.valueOf(hemodialysisDevice));
            if(hisConsumables != null){
                specList.add(hisConsumables.getConsumSpec());
            }
        }
        if(StringUtils.isNotEmpty(bloodFilter)){
            HisConsumablesDO hisConsumables = hisConsumablesService.getHisConsumables(Long.valueOf(bloodFilter));
            if(hisConsumables != null){
                specList.add(hisConsumables.getConsumSpec());
            }
        }
        if(StringUtils.isNotEmpty(perfumer)){
            HisConsumablesDO hisConsumables = hisConsumablesService.getHisConsumables(Long.valueOf(perfumer));
            if(hisConsumables != null){
                specList.add(hisConsumables.getConsumSpec());
            }
        }

        if(CollectionUtils.isNotEmpty(specList)){
            return specList.stream().collect(Collectors.joining("、"));
        }
        return "";
    }

    @Override
    public PatientRespVO getPatientApp(Long id) {
        PatientDO patientDO = patientMapper.selectById(id);
        PatientRespVO patientRespVO = PatientConvert.INSTANCE.convert(patientDO);
        if (patientRespVO != null) {
            //头像
            if (StrUtil.isNotEmpty(patientRespVO.getAvatar())) {
                patientRespVO.setAvatar(staticPath + patientRespVO.getAvatar());
            }
            if (StrUtil.isNotEmpty(patientRespVO.getSignature())) {
                patientRespVO.setSignature(staticPath + patientRespVO.getSignature());
            }
            DialysisManagerDO dialysisManagerDO = dialysisManagerMapper.selectOne(DialysisManagerDO::getPatientId, patientRespVO.getId(), DialysisManagerDO::getDateWeek, DateUtil.beginOfDay(DateUtil.date()));
            //机号
            if (dialysisManagerDO != null) {
                FacilityDO facilityDO = facilityMapper.selectById(dialysisManagerDO.getFacilityId());
                if (facilityDO != null) {
                    patientRespVO.setFacilityName(facilityDO.getCode());
                }
            } else {
                TeamPatientDO teamDO = teamPatientMapper.selectOne(TeamPatientDO::getPatientId, patientRespVO.getId(), TeamPatientDO::getDateWeek, DateUtil.beginOfDay(DateUtil.date()));
                if (teamDO != null) {
                    FacilityDO facilityDO = facilityMapper.selectById(teamDO.getFacilityId());
                    if (facilityDO != null) {
                        patientRespVO.setFacilityName(facilityDO.getCode());
                    }
                }
            }
        }
        return patientRespVO;
    }

    @Override
    public DiseaseHistoryRespVO diseaseHistory(Long patientId) {

        DiseaseHistoryRespVO diseaseHistoryRespVO = new DiseaseHistoryRespVO();
        //患者基本信息
        PatientDO patientDO = patientMapper.selectById(patientId);
        StringBuilder sb = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        if (patientDO != null) {
            diseaseHistoryRespVO.setPatientName(patientDO.getName());
            diseaseHistoryRespVO.setAgeSex(sb.append(patientDO.getAge()).append(StrUtil.isEmpty(patientDO.getSex()) ? "" : "1".equals(patientDO.getSex()) ? "(" + "男" + ")" : "(" + "女" + ")").toString());
            diseaseHistoryRespVO.setDialyzeTimeAge(sb2.append(patientDO.getFirstReceiveTime() == null ? "" : DateUtil.format(patientDO.getFirstReceiveTime(), DatePattern.NORM_DATE_PATTERN)).append(StrUtil.isEmpty(patientDO.getDialyzeAge()) ? "" : "(" + patientDO.getDialyzeAge() + ")").toString());
            //诊断
            //透析方案
            List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(DialyzeOptionDO::getPatientId, patientId, DialyzeOptionDO::getPid, 0);
            List<DialyzeOptionRespVO> dialyzeOptionRespVOS = DialyzeOptionConvert.INSTANCE.convertList(dialyzeOptionDOS);
            if (CollectionUtil.isNotEmpty(dialyzeOptionRespVOS)) {
                String cycleNumber = dialyzeOptionRespVOS.stream().map(dialyzeOptionRespVO -> {
                    //周期
                    DictDataRespDTO cycle = dictDataApi.getDictData("zhouqi", dialyzeOptionRespVO.getCycleDictValue());
                    //次数
                    DictDataRespDTO number = dictDataApi.getDictData("cishu", dialyzeOptionRespVO.getNumberDictValue());
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(cycle == null ? "" : cycle.getLabel());
                    stringBuilder.append(number == null ? "" : number.getLabel());
                    return stringBuilder.toString();
                }).collect(Collectors.joining(","));
                diseaseHistoryRespVO.setDialysisProtocol(cycleNumber);
            }
            //血管通路
            List<BloodRoadDO> bloodRoadDOS = bloodRoadMapper.selectList(BloodRoadDO::getPatientId, patientId, BloodRoadDO::getStatus, 0);
            if (CollectionUtil.isNotEmpty(bloodRoadDOS)) {
                List<Long> collect = bloodRoadDOS.stream().map(BloodRoadDO::getType).collect(Collectors.toList());
                List<VascularAccessDO> vascularAccessDOS = vascularAccessMapper.selectList(VascularAccessDO::getId, collect);
                if (CollectionUtil.isNotEmpty(vascularAccessDOS)) {
                    String collect1 = vascularAccessDOS.stream().map(VascularAccessDO::getName).collect(Collectors.joining(","));
                    diseaseHistoryRespVO.setVascularAccess(collect1);
                }

            }
            //透析记录
            List<DialysisRecordDO> dialysisRecordDOS = dialysisRecordMapper.selectList(DialysisRecordDO::getPatientId, patientId);
            diseaseHistoryRespVO.setDialysisManagerMap(dialysisRecordDOS);
            //检验检查
            List<RenalProjectDO> renalProjectDOS = renalProjectMapper.selectList(RenalProjectDO::getPatientId, patientId);
            if (CollectionUtil.isNotEmpty(renalProjectDOS)) {
                List<RenalProjectDO> renalProjectDOList = renalProjectDOS.stream().filter(renalProjectDO -> renalProjectDO.getLastCheckTime() != null).collect(Collectors.toList());
                List<RenalProjectRespVO> renalProjectRespVOS = RenalProjectConvert.INSTANCE.convertList(renalProjectDOList);
                if (CollectionUtil.isNotEmpty(renalProjectRespVOS)) {
                    renalProjectRespVOS = renalProjectRespVOS.stream().peek(renalProjectRespVO -> {
                        DictDataRespDTO dictData = dictDataApi.getDictData(renalProjectRespVO.getDictId());
                        renalProjectRespVO.setDictLabel(dictData != null ? dictData.getLabel() : null);
                    }).collect(Collectors.toList());
                }
                diseaseHistoryRespVO.setRenalProjectRespVOS(renalProjectRespVOS);
            }
            //医嘱信息
            List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPatientId, patientId);
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                List<Map<String, Object>> collect = dialysisAdviceDOS.stream().map(dialysisAdviceDO -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("startTime", DateUtil.format(dialysisAdviceDO.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
                    map.put("adviceName", dialysisAdviceDO.getAdviceName() + "(" + dialysisAdviceDO.getAdviceDesprition() + ")");
                    return map;
                }).collect(Collectors.toList());
                diseaseHistoryRespVO.setAdviceMap(collect);
            }
        }
        return diseaseHistoryRespVO;
    }


    @Override
    public List<PatientRespVO> getPatientWithDialysisList(Date startTime, Date endTime) {
        return PatientConvert.INSTANCE.convertList(patientMapper.selectList(new LambdaQueryWrapper<PatientDO>()
                .select(PatientDO::getId, PatientDO::getName, PatientDO::getAge, PatientDO::getFirstReceiveTime)
                .between(PatientDO::getCreateTime, startTime, endTime)));
    }

    @Override
    public List<MaintainDialysisVo> queryAllList(MaintainDialysisVo vo) {
        /**
         * 查询患者信息/责任护士/透析时间
         */
        MPJLambdaWrapper<PatientDO> wrapper = new MPJLambdaWrapper<>(PatientDO.class)
                .leftJoin(JkDivisionDO.class, JkDivisionDO::getPatientId, PatientDO::getId)
                .leftJoin(TeamPatientDO.class, TeamPatientDO::getPatientId, PatientDO::getId)
                .leftJoin(FacilitySubareaDO.class, FacilitySubareaDO::getId, TeamPatientDO::getFacilitySubareaId)
                .eq(StrUtil.isNotEmpty(vo.getDateWeek()), TeamPatientDO::getDateWeek, vo.getDateWeek())
                .eq(StrUtil.isNotEmpty(vo.getWeekDay()), TeamPatientDO::getWeekDay, vo.getWeekDay())
                .eq(StrUtil.isNotEmpty(vo.getWeekDay()), TeamPatientDO::getWeekDay, vo.getWeekDay())
                .eq(StrUtil.isNotEmpty(vo.getPatientState()), PatientDO::getPatientState, vo.getPatientState())//  新患者/维持性患者
                .eq(StrUtil.isNotEmpty(vo.getFacilitySubareaId()), TeamPatientDO::getFacilitySubareaId, vo.getFacilitySubareaId())
//                .eq(StrUtil.isNotEmpty(vo.getIsGroup()),PatientDO::getIsGroup,vo.getIsGroup())
                .groupBy(true, PatientDO::getId)


                .selectAs(PatientDO::getId, "id")
                .selectAs(PatientDO::getName, "name")
                .selectAs(PatientDO::getDialyzeNo, "dialyzeNo")
                .selectAs(PatientDO::getDialyzeTotal, "dialyzeTotal")
                .selectAs(JkDivisionDO::getNurseId, "nurseId")
                .selectAs(JkDivisionDO::getNurseName, "nurse")
                .selectAs(FacilitySubareaDO::getName, "facilityName")
                .selectAs(FacilitySubareaDO::getId, "facilitySubareaId")
                .and(StrUtil.isNotEmpty(vo.getKeyword()), i -> i
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getName, vo.getKeyword())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getSpellName, vo.getKeyword())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getDialyzeNo, vo.getKeyword()));
        List<MaintainDialysisVo> patientDOS = patientMapper.selectJoinList(MaintainDialysisVo.class, wrapper);
        return patientDOS;
    }

    @Override
    public List<CollectiveVo> getCollective(String keyWord) {
        List<CollectiveVo> patientDOS = patientMapper.getCollective(keyWord);
        return patientDOS;
    }

    @Override
    public List<PatientImplementationVo> getMouth(PatientImplementationVo vo) {
        /**
         * 查询患者信息/责任护士/透析时间
         */
        MPJLambdaWrapper<PatientDO> wrapper = new MPJLambdaWrapper<PatientDO>(PatientDO.class)
                .leftJoin(JkDivisionDO.class, JkDivisionDO::getPatientId, PatientDO::getId)
                .leftJoin(TeamPatientDO.class, TeamPatientDO::getPatientId, PatientDO::getId)
                .leftJoin(FacilitySubareaDO.class, FacilitySubareaDO::getId, TeamPatientDO::getFacilitySubareaId)
                .eq(StrUtil.isNotEmpty(vo.getDateWeek()), TeamPatientDO::getDateWeek, vo.getDateWeek())
                .eq(StrUtil.isNotEmpty(vo.getWeekDay()), TeamPatientDO::getWeekDay, vo.getWeekDay())
                .eq(StrUtil.isNotEmpty(vo.getWeekDay()), TeamPatientDO::getWeekDay, vo.getWeekDay())
                .eq(StrUtil.isNotEmpty(vo.getPatientState()), PatientDO::getPatientState, vo.getPatientState())//  新患者/维持性患者
                .eq(StrUtil.isNotEmpty(vo.getFacilitySubareaId()), TeamPatientDO::getFacilitySubareaId, vo.getFacilitySubareaId())

                .selectAs(PatientDO::getId, "id")
                .selectAs(PatientDO::getName, "name")
                .selectAs(PatientDO::getDialyzeNo, "dialyzeNo")
                .selectAs(PatientDO::getDialyzeTotal, "dialyzeTotal")
                .selectAs(JkDivisionDO::getNurseId, "nurseId")
                .selectAs(JkDivisionDO::getNurseName, "nurse")
                .selectAs(FacilitySubareaDO::getName, "facilityName")
                .selectAs(FacilitySubareaDO::getId, "facilitySubareaId")
                .and(StrUtil.isNotEmpty(vo.getKeyword()), i -> i
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getName, vo.getName())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getSpellName, vo.getKeyword())
                        .or()
                        .like(StrUtil.isNotEmpty(vo.getKeyword()), PatientDO::getDialyzeNo, vo.getKeyword()));
        List<PatientImplementationVo> patientImplementationVos = patientMapper.selectJoinList(PatientImplementationVo.class, wrapper);
        return patientImplementationVos;
    }

    @Override
    public List<PatientInspectionRespVO> inspectionList(PatientCreateReqVO createReqVO) {
        MPJLambdaWrapper<PatientDO> wrapper = new MPJLambdaWrapper<>(PatientDO.class);
        wrapper.leftJoin(RenalProjectDO.class, RenalProjectDO::getPatientId, PatientDO::getId)
                .selectAs(PatientDO::getId, "patientId")
                .select(PatientDO::getName, PatientDO::getDialyzeNo, PatientDO::getMedic, PatientDO::getNurse)
                .select(RenalProjectDO::getDictId, RenalProjectDO::getLastCheckTime)
                .eq(RenalProjectDO::getProjectType, 0)
                .eq(createReqVO.getMedic() != null, PatientDO::getMedic, createReqVO.getMedic())
                .and(StrUtil.isNotEmpty(createReqVO.getMore()), i -> i
                        .like(PatientDO::getName, createReqVO.getMore())
                        .or()
                        .like(PatientDO::getSpellName, createReqVO.getMore())
                        .or()
                        .like(PatientDO::getDialyzeNo, createReqVO.getMore()));
        List<PatientInspectionRespVO> patientInspectionRespVOS = patientMapper.selectJoinList(PatientInspectionRespVO.class, wrapper);
        if (CollectionUtil.isNotEmpty(patientInspectionRespVOS)) {
            Map<Long, List<PatientInspectionRespVO>> patientInspectionMap = null;
            //按患者
            if ("1".equals(createReqVO.getType())) {
                patientInspectionMap = patientInspectionRespVOS.stream().collect(Collectors.groupingBy(patientInspectionRespVO -> patientInspectionRespVO.getPatientId()));
            } else if ("2".equals(createReqVO.getType())) {
                //按项目
                patientInspectionMap = patientInspectionRespVOS.stream().collect(Collectors.groupingBy(patientInspectionRespVO -> patientInspectionRespVO.getDictId()));
            }
            List<PatientInspectionRespVO> patientInspectionRespVOList = patientInspectionMap.entrySet().stream().map(entry -> {
                PatientInspectionRespVO patientInspectionRespVO = new PatientInspectionRespVO();
                if ("1".equals(createReqVO.getType())) {
                    PatientDO patientDO = patientMapper.selectById(entry.getKey());
                    if (patientDO != null) {
                        UserRespDTO adminUser = adminUserApi.getAdminUserInfo(patientDO.getNurse());
                        UserRespDTO adminUser2 = adminUserApi.getAdminUserInfo(patientDO.getMedic());
                        patientInspectionRespVO.setPatientId(entry.getKey());
                        patientInspectionRespVO.setName(patientDO.getName());
                        patientInspectionRespVO.setDialyzeNo(patientDO.getDialyzeNo());
                        patientInspectionRespVO.setNurseName(adminUser != null ? adminUser.getNickname() : null);
                        patientInspectionRespVO.setMedicName(adminUser2 != null ? adminUser2.getNickname() : null);
                    }
                } else if ("2".equals(createReqVO.getType())) {
                    DictDataRespDTO dictData1 = dictDataApi.getDictData(entry.getKey());
                    patientInspectionRespVO.setCheckProject(dictData1.getValue());
                }
                //计算到期时间
                List<PatientInspectionRespVO> inspectionRespVOS = entry.getValue().stream().filter(patientInspectionRespVO1 -> patientInspectionRespVO1.getLastCheckTime() != null).sorted((a, b) -> a.getLastCheckTime().compareTo(b.getLastCheckTime())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(inspectionRespVOS)) {
                    List<InspectSettingsDO> inspectSettingsDOS = inspectSettingsMapper.selectList();
                    if (CollectionUtil.isNotEmpty(inspectSettingsDOS)) {
                        List<Long> dictIds = inspectSettingsDOS.stream().filter(inspectSettingsDO -> StrUtil.isNotEmpty(inspectSettingsDO.getFrequency())).map(InspectSettingsDO::getDictId).collect(Collectors.toList());
                        inspectionRespVOS = inspectionRespVOS.stream().filter(inspection -> dictIds.contains(inspection.getDictId())).collect(Collectors.toList());
                    }
                    List<PatientInspectionRespVO> inspectionRespVOList = inspectionRespVOS.stream().peek(inspectionRespVO -> {
                        InspectSettingsDO inspectSettingsDO = inspectSettingsMapper.selectOne(InspectSettingsDO::getDictId, inspectionRespVO.getDictId());
                        if (inspectSettingsDO != null) {
                            //最后一次检查时间+频率-1天
                            if (StrUtil.isEmpty(inspectSettingsDO.getBeforeWarn())) {
                                DateTime monthDateTime = DateUtil.offsetMonth(inspectionRespVO.getLastCheckTime(), Integer.valueOf(inspectSettingsDO.getFrequency()));
                                DateTime dayDateTime = DateUtil.offsetDay(monthDateTime, -1);
                                DictDataRespDTO dictData = dictDataApi.getDictData("renal_project_items", inspectionRespVO.getCheckProject());
                                inspectionRespVO.setCheckProject(dictData != null ? dictData.getLabel() : null);
                                inspectionRespVO.setLastCheckTime(dayDateTime);
                            } else {//最后一次检查时间+频率-提前天数
                                DateTime monthDateTime = DateUtil.offsetMonth(inspectionRespVO.getLastCheckTime(), Integer.valueOf(inspectSettingsDO.getFrequency()));
                                DateTime dayDateTime = DateUtil.offsetDay(monthDateTime, Integer.valueOf(inspectSettingsDO.getBeforeWarn()));
                                DictDataRespDTO dictData = dictDataApi.getDictData("renal_project_items", inspectionRespVO.getCheckProject());
                                inspectionRespVO.setCheckProject(dictData != null ? dictData.getLabel() : null);
                                inspectionRespVO.setLastCheckTime(dayDateTime);
                            }
                        }
                    }).filter(inspectionReq -> createReqVO.getStartTime() != null && createReqVO.getEndTime() != null ? inspectionReq.getLastCheckTime().after(createReqVO.getStartTime()) && inspectionReq.getLastCheckTime().before(createReqVO.getEndTime()) : 1 == 1).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(inspectionRespVOList)) {
                        List<Map<String, Object>> collect = inspectionRespVOList.stream().map(patientInspectionRespVO1 -> {
                            Map<String, Object> map = Maps.newHashMap();
                            if ("1".equals(createReqVO.getType())) {
                                DictDataRespDTO dictData = dictDataApi.getDictData(patientInspectionRespVO1.getDictId());
                                map.put("lastCheckTime", patientInspectionRespVO1.getLastCheckTime() != null ? DateUtil.format(patientInspectionRespVO1.getLastCheckTime(), DatePattern.NORM_DATE_PATTERN) : "");
                                map.put("dictData", dictData != null ? dictData.getLabel() : "");
                            } else if ("2".equals(createReqVO.getType())) {
                                PatientDO patientDO1 = patientMapper.selectById(patientInspectionRespVO1.getPatientId());
                                map.put("lastCheckTime", patientInspectionRespVO1.getLastCheckTime() != null ? DateUtil.format(patientInspectionRespVO1.getLastCheckTime(), DatePattern.NORM_DATE_PATTERN) : "");
                                map.put("dictData", patientDO1 != null ? patientDO1.getName() : "");
                            }
                            return map;
                        }).collect(Collectors.toList());
                        patientInspectionRespVO.setExpireWarn(collect);
                    }
                }
                return patientInspectionRespVO;
            }).collect(Collectors.toList());
            return patientInspectionRespVOList;
        }
        return null;
    }

    @Override
    public void batchLabels(PatientLabelReqVO patientLabelReqVO) {
        for (Long patientId : patientLabelReqVO.getPatientIds()) {
            PatientDO patientDO = patientMapper.selectOne(PatientDO::getId, patientId);
            List<PatientLabelReqVO> patientLabelReqVOS = patientLabelReqVO.getParams().stream().filter(patientLabelReqVO1 -> 1 == patientLabelReqVO1.getType()).collect(Collectors.toList());
            if (patientDO != null && StrUtil.isNotEmpty(patientDO.getLabels()) && CollectionUtil.isNotEmpty(patientLabelReqVOS)) {
                List<Long> patientIdList = Arrays.stream(patientDO.getLabels().split(",")).map(Long::valueOf).collect(Collectors.toList());
                List<Long> ids = patientLabelReqVOS.stream().map(PatientLabelReqVO::getIds).map(Long::valueOf).collect(Collectors.toList());
                List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getPid, ids);
                if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                    List<Long> mottoSimpleIds = mottoSimpleDOS.stream().map(MottoSimpleDO::getId).collect(Collectors.toList());
                    Collection subtract = CollectionUtils.subtract(patientIdList, mottoSimpleIds);
                    if (CollectionUtil.isNotEmpty(subtract)) {
                        String collect = (String) subtract.stream().map(String::valueOf).collect(Collectors.joining(","));
                        patientMapper.update(new PatientDO(), new LambdaUpdateWrapper<PatientDO>().set(PatientDO::getLabels, collect).eq(PatientDO::getId, patientId));
                    } else {
                        patientMapper.update(new PatientDO(), new LambdaUpdateWrapper<PatientDO>().set(PatientDO::getLabels, null).eq(PatientDO::getId, patientId));
                    }
                }
            }
            PatientDO patientDO1 = patientMapper.selectOne(PatientDO::getId, patientId);
            List<PatientLabelReqVO> patientLabelReqVOS1 = patientLabelReqVO.getParams().stream().filter(patientLabelReqVO1 -> 2 == patientLabelReqVO1.getType()).collect(Collectors.toList());
            if (patientDO1 != null && StrUtil.isNotEmpty(patientDO1.getLabels())) {
                //患者信息标签
                List<String> labels = Arrays.stream(patientDO1.getLabels().split(",")).collect(Collectors.toList());
                List<String> collect = labels.stream().map(label -> {
                    MottoSimpleDO mottoSimpleDO = mottoSimpleMapper.selectOne(MottoSimpleDO::getId, label);
                    for (PatientLabelReqVO labelReqVO : patientLabelReqVOS1) {
                        if (mottoSimpleDO.getPid() == Long.valueOf(labelReqVO.getIds())) {
                            //如果父id相等就把标签值赋给患者
                            label = labelReqVO.getLabels();
                        }
                    }
                    return label;
                }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    List<String> collect1 = patientLabelReqVOS1.stream().map(PatientLabelReqVO::getLabels).collect(Collectors.toList());
                    Collection<String> disjunction = CollectionUtils.disjunction(collect, collect1);
                    if (CollectionUtil.isNotEmpty(disjunction)) {
                        disjunction.addAll(collect);
                        Set<String> collect2 = disjunction.stream().collect(Collectors.toSet());
                        String idstr = collect2.stream().collect(Collectors.joining(","));
                        patientMapper.update(new PatientDO(), new LambdaUpdateWrapper<PatientDO>().set(PatientDO::getLabels, idstr).eq(PatientDO::getId, patientId));
                    } else {
                        String collect2 = collect1.stream().collect(Collectors.joining(","));
                        patientMapper.update(new PatientDO(), new LambdaUpdateWrapper<PatientDO>().set(PatientDO::getLabels, collect2).eq(PatientDO::getId, patientId));
                    }
                }
            } else {
                String idstr = patientLabelReqVOS1.stream().map(PatientLabelReqVO::getLabels).collect(Collectors.joining(","));
                patientMapper.update(new PatientDO(), new LambdaUpdateWrapper<PatientDO>().set(PatientDO::getLabels, idstr).eq(PatientDO::getId, patientId));
            }
        }
    }

    @Override
    public Map<String, Integer> getArrangeClassesNumber(PatientPageReqVO pageVO) {
        Map<String, Integer> map = Maps.newHashMap();
        List<DictDataRespDTO> dictListData = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(new Date());
        if (CollectionUtil.isNotEmpty(dictListData)) {

            for (DictDataRespDTO dictListDatum : dictListData) {
                if (StringUtils.isNotNull(pageVO.getComputerNurse())) {
                    if(StringUtils.isEmpty(pageVO.getQueryDate()) || format.equals(pageVO.getQueryDate())){
                        pageVO.setDays(format);
                    }else{
                        pageVO.setDays(pageVO.getQueryDate());
                    }
                    pageVO.setDayState(ListUtils.newArrayList(dictListDatum.getValue()));

                    List<ArrangeClassesDO> arrangeClassesDOS = hemodialysisManagerMapper.getMyPatient(pageVO);
                    map.put(dictListDatum.getValue(), arrangeClassesDOS.size());
                }else{
                    LambdaQueryWrapperX<ArrangeClassesDO> queryWrapperX = new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                            .eqIfPresent(ArrangeClassesDO::getDayState, dictListDatum.getValue());
                    if(StringUtils.isEmpty(pageVO.getQueryDate()) || format.equals(pageVO.getQueryDate())){
                        queryWrapperX.betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()));
                    }else{
                        DateTime parse = DateUtil.parse(pageVO.getQueryDate(), "yyyy-MM-dd");
                        queryWrapperX.eq(ArrangeClassesDO::getClassesTime, parse);
                    }
                    if(CollectionUtils.isNotEmpty(pageVO.getEndemicAreas())){
                        queryWrapperX.inIfPresent(ArrangeClassesDO::getFacilitySubareaId, pageVO.getEndemicAreas());
                    }
                    List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(queryWrapperX);
                    map.put(dictListDatum.getValue(), arrangeClassesDOS.size());
                }

            }
        }

        int sum = map.entrySet().stream().mapToInt(Map.Entry::getValue).sum();
        List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getPatientTypeSource, "00",PatientDO::getPatientType,"1");
        map.put("day", sum);
        map.put("all", patientDOS.size());
        return map;
    }

    @Override
    public Map<String, Integer> getArrangeClassesNumber() {
        Map<String, Integer> map = Maps.newHashMap();
        List<DictDataRespDTO> dictListData = dictDataApi.getDictListData("arrangeClassesPeriodTime");
        if (CollectionUtil.isNotEmpty(dictListData)) {
            for (DictDataRespDTO dictListDatum : dictListData) {
                List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date()))
                        .eqIfPresent(ArrangeClassesDO::getDayState, dictListDatum.getValue()));
                map.put(dictListDatum.getValue(), arrangeClassesDOS.size());
            }
        }

        int sum = map.entrySet().stream().mapToInt(Map.Entry::getValue).sum();
        Long count = patientMapper.selectCount();
        map.put("day", sum);
        map.put("all", Math.toIntExact(count));
        return map;
    }

    @Override
    public void exportPatientInfo(PatientPageReqVO patientPageReqVO, HttpServletResponse response) throws IOException {
        String to = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, patientPageReqVO.getFields()) + ",id";
        if (!org.springframework.util.StringUtils.isEmpty(patientPageReqVO.getEndTime())) {
            Date endTime = patientPageReqVO.getEndTime();
            endTime.setTime(endTime.getTime() + 24*60*60*1000 -1);
            patientPageReqVO.setEndTime(endTime);
        }

        List<String> collect = Arrays.stream(to.split(",")).collect(Collectors.toList());
        if (collect.contains("dialysis_value")) {
            collect.remove("dialysis_value");
        }
        if (collect.contains("out_time")) {
            collect.remove("out_time");
        }
        if (collect.contains("duration")) {
            collect.remove("duration");
        }
        to = collect.stream().collect(Collectors.joining(","));
        patientPageReqVO.setFields(to);
        PageResult<PatientDO> patientDOPageResult = patientMapper.selectPageExport(patientPageReqVO);
        PageResult<PatientExportVO> patientExportVOPageResult = PatientConvert.INSTANCE.convertPage2(patientDOPageResult);
        if (CollectionUtil.isNotEmpty(patientExportVOPageResult.getList())) {
            // 设置动态头
            List<List<String>> headList = new ArrayList<>();
            // 获取动态数据
            List<List<Object>> dataList = new ArrayList<>();
            for (PatientFieldReqVO patientFieldReqVO : patientPageReqVO.getPatientFieldReqVOS()) {
                List<String> head = new ArrayList<>();
                //head.add(patientFieldReqVO.getField());
                head.add(patientFieldReqVO.getLabel());
                headList.add(head);

            }

            for (PatientExportVO patientExportVO : patientExportVOPageResult.getList()) {
                List<Object> objectList = new ArrayList<>();
                Map map = JSONUtil.toBean(JSONUtil.toJsonStr(patientExportVO), Map.class);
                Integer id = (Integer) map.get("id");
                for (PatientFieldReqVO patientFieldReqVO : patientPageReqVO.getPatientFieldReqVOS()) {
                    if ("sex".equals(patientFieldReqVO.getField())) {
                        //性别
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("sex", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("patientSource".equals(patientFieldReqVO.getField())) {//患者来源
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("patient_source", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("applyWay".equals(patientFieldReqVO.getField())) {//报销方式
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("applyWay", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("describes".equals(patientFieldReqVO.getField())) {//诊断
                        //诊断
                        List<DiseaseReasonDO> diseaseReasonDOS = diseaseReasonMapper.selectList(DiseaseReasonDO::getPatientId, id, DiseaseReasonDO::getSync, 1);
                        if (CollectionUtil.isNotEmpty(diseaseReasonDOS)) {
                            String diseaseReasonNames = diseaseReasonDOS.stream().map(diseaseReasonDO -> {
                                //如果是自定义有值取自定义，三级和二级有值取三级，三级有值取三级，二级有值取二级
                                String str = "";
                                if (StrUtil.isNotEmpty(diseaseReasonDO.getCustomName())) {
                                    str = diseaseReasonDO.getCustomName();
                                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentThreeName())) {
                                    str = diseaseReasonDO.getParentThreeName();
                                } else if (StrUtil.isNotEmpty(diseaseReasonDO.getParentTwoName())) {
                                    str = diseaseReasonDO.getParentTwoName();
                                }
                                return str;
                            }).collect(Collectors.joining(","));
                            objectList.add(diseaseReasonNames);
                        }
                    } else if ("bloodType".equals(patientFieldReqVO.getField())) {//血型
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("bloodType", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("rh".equals(patientFieldReqVO.getField())) {//rh
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("bloodTypeRh", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("baoEndWarnTime".equals(patientFieldReqVO.getField())) {//医保到期时间
                        Object o = map.get(patientFieldReqVO.getField());
                        objectList.add(o != null ? DateUtil.format(DateTime.of((Long) o), DatePattern.NORM_DATE_PATTERN) : "");
                    } else if ("culture".equals(patientFieldReqVO.getField())) {//文化程度
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("culture", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("marriage".equals(patientFieldReqVO.getField())) {//婚姻状况
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("marriage", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("occupation".equals(patientFieldReqVO.getField())) {//职业
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("occupation", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("firstReceiveTime".equals(patientFieldReqVO.getField())) {//首次透析日
                        Object o = map.get(patientFieldReqVO.getField());
                        objectList.add(o != null ? DateUtil.format(DateTime.of((Long) o), DatePattern.NORM_DATE_PATTERN) : "");
                    } else if ("infect".equals(patientFieldReqVO.getField())) {//传染病
                        Object o = map.get(patientFieldReqVO.getField());
                        if (o != null && !"".equals(o)) {
                            List<String> collect1 = Arrays.stream(String.valueOf(o).split(",")).collect(Collectors.toList());
                            List<DictDataRespDTO> infect = dictDataApi.getDictDataListByBatchValue(collect1, "infect");
                            if (CollectionUtil.isNotEmpty(infect)) {
                                String collect2 = infect.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                                objectList.add(collect2);
                            }
                        } else {
                            objectList.add("");
                        }
                    } else if ("nation".equals(patientFieldReqVO.getField())) {//民族
                        Object o = map.get(patientFieldReqVO.getField());
                        DictDataRespDTO dataApiDictData = dictDataApi.getDictData("nation", o + "");
                        objectList.add(dataApiDictData != null ? dataApiDictData.getLabel() : "");
                    } else if ("labels".equals(patientFieldReqVO.getField())) {//标签
                        Object o = map.get(patientFieldReqVO.getField());
                        if (o != null && !"".equals(o)) {
                            List<Long> collect1 = Arrays.stream(String.valueOf(o).split(",")).map(Long::valueOf).collect(Collectors.toList());
                            List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(new LambdaQueryWrapperX<MottoSimpleDO>()
                                    .in(MottoSimpleDO::getId, collect1)
                                    .select(MottoSimpleDO::getPname));
                            if (CollectionUtil.isNotEmpty(mottoSimpleDOS)) {
                                String collect2 = mottoSimpleDOS.stream().map(MottoSimpleDO::getPname).collect(Collectors.joining(","));
                                objectList.add(collect2);
                            }
                        } else {
                            objectList.add("");
                        }
                    } else if ("receiveTime".equals(patientFieldReqVO.getField())) {//接收日期
                        Object o = map.get(patientFieldReqVO.getField());
                        objectList.add(o != null ? DateUtil.format(DateTime.of((Long) o), DatePattern.NORM_DATE_PATTERN) : "");
                    } else if ("medic".equals(patientFieldReqVO.getField())) {//主管医生
                        Object o = map.get(patientFieldReqVO.getField());
                        if (o != null && !"".equals(o)) {
                            AdminUserRespDTO user = adminUserApi.getUser(Long.valueOf(o + ""));
                            if (user != null) {
                                objectList.add(user.getNickname());
                            }
                        } else {
                            objectList.add("");
                        }

                    } else if ("nurse".equals(patientFieldReqVO.getField())) {//主管护士
                        Object o = map.get(patientFieldReqVO.getField());
                        if (o != null && !"".equals(o)) {
                            AdminUserRespDTO user = adminUserApi.getUser(Long.valueOf(o + ""));
                            if (user != null) {
                                objectList.add(user.getNickname());
                            }
                        } else {
                            objectList.add("");
                        }
                    } else if ("dialysisValue".equals(patientFieldReqVO.getField())) {//透析模式
                        List<DialyzeOptionDO> dialyzeOptionDOS = dialyzeOptionMapper.selectList(new LambdaQueryWrapperX<DialyzeOptionDO>()
                                .eq(DialyzeOptionDO::getPatientId, id)
                                .select(DialyzeOptionDO::getDialyzeDictValue));
                        if (CollectionUtil.isNotEmpty(dialyzeOptionDOS)) {
                            List<String> dialyzeValues = dialyzeOptionDOS.stream().filter(dialyzeOptionDO -> StrUtil.isNotEmpty(dialyzeOptionDO.getDialyzeDictValue())).map(DialyzeOptionDO::getDialyzeDictValue).collect(Collectors.toList());
                            List<DictDataRespDTO> dialyzeWay = dictDataApi.getDictDataListByBatchValue(dialyzeValues, "dialyze_way");
                            if (CollectionUtil.isNotEmpty(dialyzeWay)) {
                                String collect1 = dialyzeWay.stream().map(DictDataRespDTO::getLabel).collect(Collectors.joining(","));
                                objectList.add(collect1);
                            }
                        } else {
                            objectList.add("");
                        }
                    }else if("dialyzeTotal".equals(patientFieldReqVO.getField())) {
                        Long count = dialysisRecordMapper.selectCount(DialysisRecordDO::getPatientId, patientExportVO.getId());
                        if (!org.springframework.util.StringUtils.isEmpty(patientPageReqVO.getStartTime()) && !org.springframework.util.StringUtils.isEmpty(patientPageReqVO.getEndTime())) {
                            patientPageReqVO.setId(patientExportVO.getId());
                            count = dialysisRecordMapper.selectCountByTime(patientPageReqVO);
                        }

                        objectList.add(count + "");
                    }else {
                        objectList.add(map.get(patientFieldReqVO.getField()) == null ? "" : map.get(patientFieldReqVO.getField()));
                    }
                }
                dataList.add(objectList);
            }

            //EasyExcelUtils.write(response, "患者信息.xlsx", "患者信息数据", PatientExportVO.class, patientExportVOPageResult.getList());
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("患者信息.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream()).head(headList).sheet("患者信息数据").doWrite(dataList);
        }
    }

    @Override
    public Boolean synPatInfoToHis(PatientRespVO patientRespVO) {
        PatInfoVo patInfoVo = new PatInfoVo();
        patInfoVo.setPatientIdcard(patientRespVO.getIdCard());
        patInfoVo.setPatientSex(patientRespVO.getSex());
        patInfoVo.setPatientTel(patientRespVO.getMobile());
        Date firstReceiveTime = patientRespVO.getFirstReceiveTime();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (!org.springframework.util.StringUtils.isEmpty(firstReceiveTime)) {
            patInfoVo.setDialysisFirstTime(simpleDateFormat.format(firstReceiveTime));
        }
        String birthday = patientRespVO.getBirthday();
        if (StringUtils.isNotEmpty(birthday)) {
            birthday = birthday + " 00:00:00";
            patInfoVo.setPatientBirthday(birthday);
        }

        String dialyzeTotalStr = patientRespVO.getDialyzeTotal();

        Integer dialysisTimes = null;

        if (dialyzeTotalStr != null && dialyzeTotalStr.matches("\\d+")) {
            dialysisTimes = Integer.valueOf(dialyzeTotalStr);
        } else {
            dialysisTimes = 0;
        }
        patInfoVo.setDialysisTimes(dialysisTimes);
        patInfoVo.setPatientName(patientRespVO.getName());
        patInfoVo.setPatientAddress(patientRespVO.getAddress());
        patInfoVo.setDiagnosis(patientRespVO.getDiseaseReasonNames());
        String infectString = patientRespVO.getInfect();
        if (StringUtils.isNotEmpty(infectString)) {
            String[] split = infectString.split(",");
            List<DictDataRespDTO> infectList = dictDataApi.getDictListData("infect");
            List<String> value= new ArrayList<>();
            for (String s : split) {
                List<DictDataRespDTO> collect = infectList.stream().filter(infect -> infect.getValue().equals(s)).collect(Collectors.toList());
                if (collect.size() > 0) {
                    value.add(collect.get(0).getLabel());
                }
            }
            String collect = value.stream().collect(Collectors.joining(","));
            patInfoVo.setInfectedHistory(collect);
        }

        // 查询报销方式
        List<MottoSimpleDO> mottoSimpleDOS = mottoSimpleMapper.selectList(MottoSimpleDO::getPid, 1);
        if (!org.springframework.util.CollectionUtils.isEmpty(mottoSimpleDOS)){
            if (StringUtils.isNotEmpty(patientRespVO.getApplyWay())) {
                Long applyId = Long.valueOf(patientRespVO.getApplyWay());
                List<MottoSimpleDO> collect = mottoSimpleDOS.stream().filter(mottoSimpleDO -> mottoSimpleDO.getId().equals(applyId)).collect(Collectors.toList());
                if (collect.size() > 0) {
                    MottoSimpleDO mottoSimpleDO = collect.get(0);
                    String pname = mottoSimpleDO.getPname();
                    if (StringUtils.isNotEmpty(pname)) {
                        if (pname.contains("居民")){
                            patInfoVo.setReimWay("390");
                        }else if (pname.contains("职工")) {
                            patInfoVo.setReimWay("310");
                        }else if (pname.contains("离休")){
                            patInfoVo.setReimWay("340");
                        }else if(pname.contains("自费")){
                            patInfoVo.setReimWay("自费");
                        }
                    }
                }
            }
        }
        // 查询患者中心
        Long deptId = patientRespVO.getDeptId();
        DeptRespDTO dept = deptApi.getDept(deptId);
        if (!org.springframework.util.StringUtils.isEmpty(dept) && !org.springframework.util.StringUtils.isEmpty(dept.getHospitalId())) {
            patInfoVo.setHosipitalId(dept.getHospitalId());
            patInfoVo.setEnabledMark(patientRespVO.getEnabledMark());

            // 同步信息到his
            postUrl(patientUrl,patInfoVo);
        }

        return true;
    }

    @Override
    public List<PatientRespVO> getPatientListByDate(ExamApplyVo date) {
        List<PatientRespVO> patientRespVOList = examApplyMapper.getPatientListByDate(date);
        /*if (CollectionUtil.isEmpty(patientRespVOList)) {
            patientRespVOList = examApplyMapper.getPatientListByDateBody(date);
        }*/
        return patientRespVOList;
    }

    @Override
    public List<ExamApplyDO> getExamList(ExamApplyVo examApplyVo) {
        if (StringUtils.isEmpty(examApplyVo.getOrderDate())){
            return null;
        }
        List<ExamApplyDO> result = examApplyMapper.getExamList(examApplyVo);
        if (!org.springframework.util.CollectionUtils.isEmpty(result) && StringUtils.isNotEmpty(examApplyVo.getDialysisNoList())){
            result = result.stream().filter(examApplyDO -> examApplyVo.getDialysisNoList().contains(examApplyDO.getPatId())).collect(Collectors.toList());
        }else {
            return null;
        }

        return result;
    }

    @Override
    public Boolean updateExamList(List<ExamApplyDO> examApplyDOList) {
        if (org.springframework.util.CollectionUtils.isEmpty(examApplyDOList)) {
            return false;
        }
        examApplyDOList.stream().forEach(examApplyDO -> {
            examApplyDO.setPrintStatus(1);
            if (org.springframework.util.StringUtils.isEmpty(examApplyDO.getPrintNum())){
                examApplyDO.setPrintNum(0);
            }
            examApplyDO.setPrintNum(examApplyDO.getPrintNum() +1);
        });
        examApplyMapper.updateBatch(examApplyDOList);
        return true;
    }

    @Override
    public PageResult<HemodialysisManagerRespVO> getPatientTreatRecordDetail(HemodialysisManagerPageReqVO pageReqVO) {
        if (pageReqVO == null || pageReqVO.getDeptId() == null) return PageResult.empty();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // 定义map 用于存在满足搜索条件的数据
        Map<Long, PatientDO> patientMap = new ConcurrentHashMap<>();
        if (StrUtil.isNotBlank(pageReqVO.getMore())) {
            // 分割搜索条件
            String more = pageReqVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            // 封装搜索患者数据
            PatientPageReqVO patientPageReqVO = new PatientPageReqVO();
            patientPageReqVO.setMore(more);
            patientPageReqVO.setSpellShort(join);

            patientMap = patientMapper.selectPage(patientPageReqVO)
                    .getList()
                    .stream()
                    .collect(Collectors.toMap(PatientDO::getId, Function.identity()));
        }
        Map<Long, PatientDO> finalPatientMap = patientMap;

        stopWatch.stop();
        System.out.println("方法1耗时："  +stopWatch.getTotalTimeSeconds());
        stopWatch.start();
        // 分页查询患者透析记录
        PageResult<HemodialysisManagerDO> hemodialysisManagerDOPageResult = hemodialysisManagerMapper.selectPage(pageReqVO,
                new LambdaQueryWrapperX<HemodialysisManagerDO>()
                        .eqIfPresent(HemodialysisManagerDO::getDeptId, pageReqVO.getDeptId())
                        .betweenIfPresent(HemodialysisManagerDO::getHemodialysisTime, pageReqVO.getStartDate(), pageReqVO.getEndDate())
                        .orderByDesc(HemodialysisManagerDO::getId)
        );
        PageResult<HemodialysisManagerRespVO> hemodialysisManagerRespVOPageResult = HemodialysisManagerConvert.INSTANCE.convertPage(hemodialysisManagerDOPageResult);

        // 查询所有中心信息
        List<DeptRespVO> deptList = deptService.getDeptList(new DeptCreateReqVO());
        Map<Long, String> deptMap = deptList.stream().collect(Collectors.toMap(DeptRespVO::getId, DeptRespVO::getName));

        // 定义查询过的用户信息
        ConcurrentMap<Long, PatientDO> userMap = new ConcurrentHashMap<>();
        // 获取所有记录数据 进行处理
        List<HemodialysisManagerRespVO> managerRespVOPageResultList = hemodialysisManagerRespVOPageResult.getList();
        stopWatch.stop();
        System.out.println("方法2耗时：" + stopWatch.getTotalTimeSeconds());
        stopWatch.start();
        List<Long> idList  = managerRespVOPageResultList.stream().map(item->item.getPatientId()).collect(Collectors.toList());
        List<PatientDO> patientDOs = patientMapper.selectList(new LambdaQueryWrapper<PatientDO>().in(PatientDO::getId,idList));
        if(CollectionUtil.isNotEmpty(patientDOs)){
            patientDOs.stream().map(item->{
                userMap.put(item.getId(),item);
                return item;
            });
        }

        managerRespVOPageResultList = managerRespVOPageResultList.stream().peek(item -> {
            // 设置患者姓名
            if (StrUtil.isBlank(pageReqVO.getMore()) && item.getPatientId() != null) {
                // 从 userMap 中查询用户信息，不存在再从数据库查询
                PatientDO patient = userMap.get(item.getPatientId());
                if (patient != null) {
                    item.setPatientName(patient.getName());
                }
            }
            // 设置门店名称
            item.setDeptName(deptMap.get(item.getDeptId()));
        }).filter(item -> {
            if (StrUtil.isNotBlank(pageReqVO.getMore())) {
                if (MapUtil.isEmpty(finalPatientMap)) return false;
                item.setPatientName(
                        Optional.ofNullable(finalPatientMap.get(item.getPatientId()))
                                .map(PatientDO::getName)
                                .orElse(null)
                );
                return finalPatientMap.containsKey(item.getPatientId());
            }
            // 没有搜索条件，保留所有数据
            return true;
        }).collect(Collectors.toList());
        hemodialysisManagerRespVOPageResult.setList(managerRespVOPageResultList);
        stopWatch.stop();
        System.out.println("方法3耗时：" + stopWatch.getTotalTimeSeconds());
        return hemodialysisManagerRespVOPageResult;
    }

    @Override
    public Boolean generateBarcodeByDate(ExamApplyVo examApplyVo, HttpServletRequest request) {
        String systemDeptId = request.getHeader("SystemDeptId");
        examApplyVo.setDeptId(Long.valueOf(systemDeptId));
        List<DialysisAdviceTransferDO> dialysisAdviceTransferDOS = examApplyMapper.findExamListByDate(examApplyVo);

        if (CollectionUtils.isNotEmpty(dialysisAdviceTransferDOS)) {
            generateBarcode(dialysisAdviceTransferDOS);
        }


        return null;
    }

    private void generateBarcode(List<DialysisAdviceTransferDO> examTodayApplyList) {

        if (!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) && examTodayApplyList.get(0).getDeptId() == 103) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream()
                        .filter(wlDialysisAdvice -> Objects.equals(wlDialysisAdvice.getDialyzeNo(), no))
                        .collect(Collectors.toList());
                //Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode"));
                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> purpelDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> wlDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().equals("凝血四项") || wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA")){
                        ExamApplyDO examApply = new ExamApplyDO();
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:103"));
                        examApply.setBarcode(String.valueOf((barcode +1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setCreateTime(new Date());
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setPrintNum(0);
                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);;
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    }else if (wlDialysisAdvice.getAdviceName().contains("血常规") || wlDialysisAdvice.getAdviceName().equals("糖化血红蛋白")) {
                        purpelDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }else if (wlDialysisAdvice.getAdviceName().contains("透后") || ((!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark())) &&(wlDialysisAdvice.getRemark().contains("下机")|| wlDialysisAdvice.getRemark().contains("透后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(purpelDialysisAdviceList)) {
                    saveBarcodeElse(purpelDialysisAdviceList,3);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    saveBarcodeElse(yellowDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcodeElse(postDialysisAdviceList,2);
                }
            });
        }else if (!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) && (examTodayApplyList.get(0).getDeptId().equals(111))) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());

                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> redDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("血常规")) {
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        ExamApplyDO examApply = new ExamApplyDO();
                        examApply.setBarcode(String.valueOf((barcode + 1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setCreateTime(new Date());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setPrintNum(0);
                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    } else if ((wlDialysisAdvice.getAdviceName().contains("传染病") || wlDialysisAdvice.getAdviceName().contains("乙肝") || wlDialysisAdvice.getAdviceName().contains("甲苯胺红梅毒")
                            ||wlDialysisAdvice.getAdviceName().contains("肝炎") || wlDialysisAdvice.getAdviceName().contains("人免疫缺陷") || wlDialysisAdvice.getAdviceName().contains("梅毒螺旋体")) && !wlDialysisAdvice.getAdviceName().contains("透后")) {
                        redDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else if (wlDialysisAdvice.getAdviceName().contains("透后")) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                    else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(redDialysisAdviceList)) {
                    saveBarcode(redDialysisAdviceList,1);

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    if (yellowDialysisAdviceList.size() >3) {
                        List<DialysisAdviceTransferDO> collect1 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺")).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect1)) {
                            saveBarcode(collect1,1);
                        }
                        List<DialysisAdviceTransferDO> collect2 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> !(wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺"))).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect2)) {
                            saveBarcode(collect2,1);
                        }
                    }else {
                        saveBarcode(yellowDialysisAdviceList,1);
                    }

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }else if (!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) && (examTodayApplyList.get(0).getDeptId().equals(106) || examTodayApplyList.get(0).getDeptId().equals(109)  || examTodayApplyList.get(0).getDeptId().equals(127) || examTodayApplyList.get(0).getDeptId().equals(114)|| examTodayApplyList.get(0).getDeptId().equals(102) || examTodayApplyList.get(0).getDeptId().equals(101)|| examTodayApplyList.get(0).getDeptId().equals(126) || examTodayApplyList.get(0).getDeptId().equals(129)|| examTodayApplyList.get(0).getDeptId().equals(130))) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());

                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> redDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("血常规") || wlDialysisAdvice.getAdviceName().contains("全血细胞计数")|| wlDialysisAdvice.getAdviceName().contains("凝血四项")) {
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        ExamApplyDO examApply = new ExamApplyDO();
                        examApply.setBarcode(String.valueOf((barcode + 1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setCreateTime(new Date());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setPrintNum(0);
                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    } else if (wlDialysisAdvice.getAdviceName().contains("传染病") || wlDialysisAdvice.getAdviceName().contains("乙肝") || wlDialysisAdvice.getAdviceName().contains("甲苯胺红梅毒")
                            || wlDialysisAdvice.getAdviceName().contains("肝炎") || wlDialysisAdvice.getAdviceName().contains("人免疫缺陷") || wlDialysisAdvice.getAdviceName().contains("梅毒螺旋体")) {
                        redDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }else if (wlDialysisAdvice.getAdviceName().contains("透后") || (!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark()) && (wlDialysisAdvice.getRemark().contains("透后") ||wlDialysisAdvice.getRemark().contains("透析后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(redDialysisAdviceList)) {
                    saveBarcode(redDialysisAdviceList,1);

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    saveBarcode(yellowDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }else if ( !org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) && ((examTodayApplyList.get(0).getDeptId().equals(100) || examTodayApplyList.get(0).getDeptId().equals(105)))) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());

                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> redDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("血常规")) {
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        ExamApplyDO examApply = new ExamApplyDO();
                        examApply.setBarcode(String.valueOf((barcode + 1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setCreateTime(new Date());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setPrintNum(0);
                        examApply.setRemark(wlDialysisAdvice.getRemark());

                        examApplyMapper.insert(examApply);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    } else if ((wlDialysisAdvice.getAdviceName().contains("传染病") || wlDialysisAdvice.getAdviceName().contains("乙肝") || wlDialysisAdvice.getAdviceName().contains("甲苯胺红梅毒")
                            ||wlDialysisAdvice.getAdviceName().contains("肝炎") || wlDialysisAdvice.getAdviceName().contains("人免疫缺陷") || wlDialysisAdvice.getAdviceName().contains("梅毒螺旋体")) && !wlDialysisAdvice.getAdviceName().contains("透后")) {
                        redDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else if (wlDialysisAdvice.getAdviceName().contains("透后") || (!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark()) &&(wlDialysisAdvice.getRemark().contains("透后") || wlDialysisAdvice.getRemark().contains("透析后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                    else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(redDialysisAdviceList)) {
                    saveBarcode(redDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    if (yellowDialysisAdviceList.size() > 3) {
                        List<DialysisAdviceTransferDO> collect1 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺")).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect1)) {
                            saveBarcode(collect1,1);
                        }
                        List<DialysisAdviceTransferDO> collect2 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> !(wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺"))).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect2)) {
                            saveBarcode(collect2,1);
                        }
                    }else {
                        saveBarcode(yellowDialysisAdviceList,1);
                    }

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }else if (!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) &&( (examTodayApplyList.get(0).getDeptId().equals(107))))
        {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());

                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> redDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("血常规") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺")) {
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        ExamApplyDO examApply = new ExamApplyDO();
                        examApply.setBarcode(String.valueOf((barcode + 1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setCreateTime(new Date());
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setPrintNum(0);
                        examApply.setRemark(wlDialysisAdvice.getRemark());

                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    } else if ((wlDialysisAdvice.getAdviceName().contains("传染病") || wlDialysisAdvice.getAdviceName().contains("乙肝") || wlDialysisAdvice.getAdviceName().contains("甲苯胺红梅毒")
                            ||wlDialysisAdvice.getAdviceName().contains("肝炎") || wlDialysisAdvice.getAdviceName().contains("人免疫缺陷") || wlDialysisAdvice.getAdviceName().contains("梅毒螺旋体")) && !wlDialysisAdvice.getAdviceName().contains("透后")) {
                        redDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else if (wlDialysisAdvice.getAdviceName().contains("透后") || (!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark()) && (wlDialysisAdvice.getRemark().contains("透后") || wlDialysisAdvice.getRemark().contains("透析后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                    else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(redDialysisAdviceList)) {
                    saveBarcode(redDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    if (yellowDialysisAdviceList.size() > 3) {
                        List<DialysisAdviceTransferDO> collect1 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") ).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect1)) {
                            saveBarcode(collect1,1);
                        }
                        List<DialysisAdviceTransferDO> collect2 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> !(wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷"))).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect2)) {
                            saveBarcode(collect2,1);
                        }
                    }else {
                        saveBarcode(yellowDialysisAdviceList,1);
                    }

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }else if (!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) &&((examTodayApplyList.get(0).getDeptId().equals(124)))) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());

                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> redDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("血常规")) {
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        ExamApplyDO examApply = new ExamApplyDO();
                        examApply.setBarcode(String.valueOf((barcode + 1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setCreateTime(new Date());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setPrintNum(0);
                        examApply.setRemark(wlDialysisAdvice.getRemark());

                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    } else if ((wlDialysisAdvice.getAdviceName().contains("传染病") || wlDialysisAdvice.getAdviceName().contains("乙肝") || wlDialysisAdvice.getAdviceName().contains("甲苯胺红梅毒")
                            ||wlDialysisAdvice.getAdviceName().contains("肝炎") || wlDialysisAdvice.getAdviceName().contains("人免疫缺陷") || wlDialysisAdvice.getAdviceName().contains("梅毒螺旋体")) && !wlDialysisAdvice.getAdviceName().contains("透后")) {
                        redDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else if (wlDialysisAdvice.getAdviceName().contains("透后") || (!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark()) && (wlDialysisAdvice.getRemark().contains("透后") || wlDialysisAdvice.getRemark().contains("透析后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                    else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(redDialysisAdviceList)) {
                    saveBarcode(redDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    if (yellowDialysisAdviceList.size() > 3) {
                        List<DialysisAdviceTransferDO> collect1 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺")).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect1)) {
                            saveBarcode(collect1,1);
                        }
                        List<DialysisAdviceTransferDO> collect2 = yellowDialysisAdviceList.stream().filter(wlDialysisAdvice -> !(wlDialysisAdvice.getAdviceName().contains("电解质") || wlDialysisAdvice.getAdviceName().contains("无机磷") || wlDialysisAdvice.getAdviceName().contains("甲状旁腺"))).collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(collect2)) {
                            saveBarcode(collect2,1);
                        }
                    }else {
                        saveBarcode(yellowDialysisAdviceList,1);
                    }

                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }else if(!org.springframework.util.CollectionUtils.isEmpty(examTodayApplyList) && (examTodayApplyList.get(0).getDeptId() == 108 || examTodayApplyList.get(0).getDeptId().equals(113) || examTodayApplyList.get(0).getDeptId().equals(104) || examTodayApplyList.get(0).getDeptId().equals(110)
                || examTodayApplyList.get(0).getDeptId().equals(112)|| examTodayApplyList.get(0).getDeptId().equals(128) || examTodayApplyList.get(0).getDeptId().equals(125) )) {
            List<String> noList = examTodayApplyList.stream().map(DialysisAdviceTransferDO::getDialyzeNo).distinct().collect(Collectors.toList());
            noList.forEach(no -> {
                List<DialysisAdviceTransferDO> collect = examTodayApplyList.stream().filter(wlDialysisAdvice ->
                        wlDialysisAdvice.getDialyzeNo().equals(no)
                ).collect(Collectors.toList());
                //Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode"));
                //PatientInfo patientInfo = patientInfoDao.findByPatId(no);
                List<DialysisAdviceTransferDO> yellowDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> postDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> purpelDialysisAdviceList = new ArrayList<>();
                List<DialysisAdviceTransferDO> wlDialysisAdviceList = new ArrayList<>();
                for (int i = 0; i < collect.size(); i++) {
                    DialysisAdviceTransferDO wlDialysisAdvice = collect.get(i);
                    if (wlDialysisAdvice.getAdviceName().equals("凝血四项") || wlDialysisAdvice.getAdviceName().contains("血常规")|| wlDialysisAdvice.getAdviceName().contains("丙型肝炎RNA") || wlDialysisAdvice.getAdviceName().contains("全血细胞计数")){
                        ExamApplyDO examApply = new ExamApplyDO();
                        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
                        examApply.setBarcode(String.valueOf((barcode +1)));
                        examApply.setCapColor(wlDialysisAdvice.getCapColor());
                        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
                        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
                        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
                        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
                        examApply.setPatName(wlDialysisAdvice.getPatientName());
                        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
                        //examApply.setOrganizeId(patientInfo.getOrganizeId());
                        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
                        examApply.setPackageId(wlDialysisAdvice.getAdviceId());
                        examApply.setPackageName(wlDialysisAdvice.getAdviceName());
                        examApply.setOrderId(String.valueOf(wlDialysisAdvice.getId()));
                        examApply.setOrderContent(wlDialysisAdvice.getAdviceName());
                        examApply.setCreateTime(new Date());
                        examApply.setPatSex(wlDialysisAdvice.getPatSex());
                        examApply.setPatAge(wlDialysisAdvice.getPatAge());
                        examApply.setSampleType(wlDialysisAdvice.getSampleType());
                        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
                        examApply.setUpdateTime(new Date());
                        examApply.setPrintStatus(0);
                        examApply.setDayState(wlDialysisAdvice.getDayState());
                        examApply.setPrintNum(0);
                        examApplyMapper.insert(examApply);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                        barcode++;
                        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
                    }else if (wlDialysisAdvice.getAdviceName().contains("透后") || ((!org.springframework.util.StringUtils.isEmpty(wlDialysisAdvice.getRemark())) &&(wlDialysisAdvice.getRemark().contains("下机")|| wlDialysisAdvice.getRemark().contains("透后") || wlDialysisAdvice.getRemark().contains("透析后")))) {
                        postDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    } else {
                        yellowDialysisAdviceList.add(wlDialysisAdvice);
                        wlDialysisAdvice.setPrinted(1);
                        examApplyMapper.updateDialysisAdviceTransfer(wlDialysisAdvice);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(yellowDialysisAdviceList)) {
                    saveBarcode(yellowDialysisAdviceList,1);
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(postDialysisAdviceList)) {
                    saveBarcode(postDialysisAdviceList,2);
                }
            });
        }
    }

    public void saveBarcode(List<DialysisAdviceTransferDO> list,int type) {

        String orderIdList = list.stream().map(wlDialysisAdvice1 -> String.valueOf(wlDialysisAdvice1.getId())).collect(Collectors.joining(","));

        String orderNameList = list.stream().map(wlDialysisAdvice2 -> wlDialysisAdvice2.getAdviceName()).collect(Collectors.joining(","));

        String adviceIdList = list.stream().map(wlDialysisAdvice3 -> wlDialysisAdvice3.getAdviceId()).collect(Collectors.joining(","));
        ExamApplyDO examApply = new ExamApplyDO();

        DialysisAdviceTransferDO wlDialysisAdvice = list.get(0);
        Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
        examApply.setBarcode(String.valueOf((barcode + 1)));
        examApply.setCapColor(wlDialysisAdvice.getCapColor());
        examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
        examApply.setCreator(wlDialysisAdvice.getAdviceUser());
        examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
        examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
        //examApply.setOrganizeId(patientInfo.getOrganizeId());
        examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
        examApply.setPackageId(adviceIdList);
        examApply.setDayState(wlDialysisAdvice.getDayState());
        examApply.setPackageName(orderNameList);
        examApply.setOrderId(orderIdList);
        if (2==type) {
            examApply.setRemark("透后");
        }

        examApply.setOrderContent(orderNameList);
        examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
        examApply.setPatName(wlDialysisAdvice.getPatientName());
        examApply.setCreateTime(new Date());
        examApply.setPatSex(wlDialysisAdvice.getPatSex());
        examApply.setPatAge(wlDialysisAdvice.getPatAge());
        examApply.setUpdateTime(new Date());
        examApply.setSampleType(wlDialysisAdvice.getSampleType());
        examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
        examApply.setPrintStatus(0);
        examApply.setPrintNum(0);
        examApplyMapper.insert(examApply);
        barcode++;
        redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
    }

    public void saveBarcodeElse(List<DialysisAdviceTransferDO> list,int type) {
        if (type == 3) {
            String orderIdList = list.stream().map(wlDialysisAdvice1 -> String.valueOf(wlDialysisAdvice1.getId())).collect(Collectors.joining(","));

            String orderNameList = list.stream().map(wlDialysisAdvice2 -> wlDialysisAdvice2.getAdviceName()).collect(Collectors.joining(","));

            String adviceIdList = list.stream().map(wlDialysisAdvice3 -> wlDialysisAdvice3.getAdviceId()).collect(Collectors.joining(","));
            ExamApplyDO examApply = new ExamApplyDO();

            DialysisAdviceTransferDO wlDialysisAdvice = list.get(0);
            Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
            examApply.setBarcode(String.valueOf((barcode + 1)));
            examApply.setCapColor(wlDialysisAdvice.getCapColor());
            examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
            examApply.setCreator(wlDialysisAdvice.getAdviceUser());
            examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
            examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
            //examApply.setOrganizeId(patientInfo.getOrganizeId());
            examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
            examApply.setPackageId(adviceIdList);
            examApply.setPackageName(orderNameList);
            examApply.setDayState(wlDialysisAdvice.getDayState());
            examApply.setOrderId(orderIdList);
            if (2==type) {
                examApply.setRemark("透后");
            }

            examApply.setOrderContent(orderNameList);
            examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
            examApply.setPatName(wlDialysisAdvice.getPatientName());
            examApply.setCreateTime(new Date());
            examApply.setPatSex(wlDialysisAdvice.getPatSex());
            examApply.setPatAge(wlDialysisAdvice.getPatAge());
            examApply.setUpdateTime(new Date());
            examApply.setSampleType(wlDialysisAdvice.getSampleType());
            examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
            examApply.setPrintStatus(0);
            examApply.setPrintNum(0);
            examApplyMapper.insert(examApply);
            barcode++;
            redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
        }else {
            String orderIdList = list.stream().map(wlDialysisAdvice1 -> String.valueOf(wlDialysisAdvice1.getId())).collect(Collectors.joining(","));

            String orderNameList = list.stream().map(wlDialysisAdvice2 -> wlDialysisAdvice2.getAdviceName()).collect(Collectors.joining(","));

            String adviceIdList = list.stream().map(wlDialysisAdvice3 -> wlDialysisAdvice3.getAdviceId()).collect(Collectors.joining(","));
            ExamApplyDO examApply = new ExamApplyDO();

            DialysisAdviceTransferDO wlDialysisAdvice = list.get(0);
            Integer barcode = Integer.valueOf(redisTemplate.opsForValue().get("barcode:" + wlDialysisAdvice.getDeptId()));
            examApply.setBarcode(String.valueOf((barcode + 1)));
            examApply.setCapColor(wlDialysisAdvice.getCapColor());
            examApply.setTakeNum(wlDialysisAdvice.getTakeNum());
            examApply.setCreator(wlDialysisAdvice.getAdviceUser());
            examApply.setDeptId(Long.valueOf(wlDialysisAdvice.getDeptId()));
            examApply.setOrderDate(wlDialysisAdvice.getCreateTime());
            //examApply.setOrganizeId(patientInfo.getOrganizeId());
            examApply.setOrganizeName(wlDialysisAdvice.getDeptName());
            examApply.setDayState(wlDialysisAdvice.getDayState());
            examApply.setPackageId(adviceIdList);
            examApply.setPackageName(orderNameList);
            examApply.setOrderId(orderIdList);
            if (2 == type) {
                examApply.setRemark("透后");
                examApply.setOrderContent("血生化【透后】");
            } else {
                examApply.setOrderContent("血生化【透前】");
            }
            examApply.setPatId(wlDialysisAdvice.getDialyzeNo());
            examApply.setPatName(wlDialysisAdvice.getPatientName());
            examApply.setCreateTime(new Date());
            examApply.setPatSex(wlDialysisAdvice.getPatSex());
            examApply.setPatAge(wlDialysisAdvice.getPatAge());
            examApply.setUpdateTime(new Date());
            examApply.setSampleType(wlDialysisAdvice.getSampleType());
            examApply.setSamplingTime(wlDialysisAdvice.getStartTime());
            examApply.setPrintStatus(0);
            examApply.setPrintNum(0);
            examApplyMapper.insert(examApply);
            barcode++;
            redisTemplate.opsForValue().set("barcode:" + wlDialysisAdvice.getDeptId(), String.valueOf(barcode));
        }
    }

    private void postUrl(String url, PatInfoVo patInfoVo) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        Gson gson = new Gson();
        String req = gson.toJson(patInfoVo);
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).setSocketTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        StringEntity stringEntity = new StringEntity(req, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            HttpResponse httpResponse = httpClient.execute(httpPost);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}
