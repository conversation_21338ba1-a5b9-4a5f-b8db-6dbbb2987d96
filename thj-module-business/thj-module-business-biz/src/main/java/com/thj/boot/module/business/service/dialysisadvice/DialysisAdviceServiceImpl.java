package com.thj.boot.module.business.service.dialysisadvice;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.interfaces.WrapperFunction;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.CollectionUtils;
import com.thj.boot.module.business.controller.admin.dialysisadvice.vo.AdviceTempsCreateVO;
import com.thj.boot.module.business.controller.admin.wxpublicno.vo.SendMessageCreateReqVO;
import com.thj.boot.module.business.convert.dialysisadvice.DialysisAdviceConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.drug.DrugDO;
import com.thj.boot.module.business.dal.datado.exam.PackageDetailDO;
import com.thj.boot.module.business.dal.datado.facility.FacilityDO;
import com.thj.boot.module.business.dal.datado.hiscombo.HisComboDO;
import com.thj.boot.module.business.dal.datado.hisconsumables.HisConsumablesDO;
import com.thj.boot.module.business.dal.datado.hisdictdata.HisDictData;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.hisinformation.HisInformationDO;
import com.thj.boot.module.business.dal.datado.mottoadvice.MottoAdviceDO;
import com.thj.boot.module.business.dal.datado.patient.PatientDO;
import com.thj.boot.module.business.dal.datado.project.ProjectDO;
import com.thj.boot.module.business.dal.datado.wechatinteraction.WechatInteractionDO;
import com.thj.boot.module.business.dal.mapper.arrangeclasses.ArrangeClassesMapper;
import com.thj.boot.module.business.dal.mapper.dialysisadvice.DialysisAdviceMapper;
import com.thj.boot.module.business.dal.mapper.drug.DrugMapper;
import com.thj.boot.module.business.dal.mapper.exam.PackageDetailMapper;
import com.thj.boot.module.business.dal.mapper.facility.FacilityMapper;
import com.thj.boot.module.business.dal.mapper.hiscombo.HisComboMapper;
import com.thj.boot.module.business.dal.mapper.hisconsumables.HisConsumablesMapper;
import com.thj.boot.module.business.dal.mapper.hisdictdata.HisDictDataMapper;
import com.thj.boot.module.business.dal.mapper.hisdrug.HisDrugMapper;
import com.thj.boot.module.business.dal.mapper.hisinformation.HisInformationMapper;
import com.thj.boot.module.business.dal.mapper.mottoadvice.MottoAdviceMapper;
import com.thj.boot.module.business.dal.mapper.patient.PatientMapper;
import com.thj.boot.module.business.dal.mapper.project.ProjectMapper;
import com.thj.boot.module.business.dal.mapper.wechatinteraction.WechatInteractionMapper;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.*;
import com.thj.boot.module.business.pojo.qualityinspection.vo.QualityInspectionRespVO;
import com.thj.boot.module.business.pojo.useregister.vo.UseRegisterRespVO;
import com.thj.boot.module.business.wxpublicno.WxPublicNoHandle;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import com.thj.boot.module.system.api.user.AdminUserApi;
import com.thj.boot.module.system.api.user.dto.AdminUserRespDTO;
import com.thj.boot.module.system.api.user.dto.UserCreateReqDTO;
import com.thj.boot.module.system.api.user.dto.UserRespDTO;
import com.thj.boot.module.system.dal.datado.dict.DictDataDO;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 透析管理-医嘱 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DialysisAdviceServiceImpl implements DialysisAdviceService {

    @Resource
    private DialysisAdviceMapper dialysisAdviceMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private MottoAdviceMapper mottoAdviceMapper;

    @Resource
    private HisDrugMapper hisDrugMapper;

    @Resource
    private FacilityMapper facilityMapper;

    @Resource
    private WxPublicNoHandle wxPublicNoHandle;

    @Resource
    private WechatInteractionMapper wechatInteractionMapper;

    @Resource
    private ArrangeClassesMapper arrangeClassesMapper;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private HisComboMapper hisComboMapper;

    @Autowired
    private HisInformationMapper hisInformationMapper;

    @Autowired
    private DrugMapper drugMapper;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private DeptApi deptApi;

    @Value("${his.advice}")
    private String adviceUrl;

    @Value("${his.adviceDelete}")
    private String deleteUrl;

    @Autowired
    private HisDictDataMapper hisDictDataMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private HisConsumablesMapper hisConsumablesMapper;

    @Autowired
    private PackageDetailMapper packageDetailMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDialysisAdvice(DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:createDialysisAdvice:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                List<DialysisAdviceDO> dialysisAdviceDOList = new ArrayList<>();
                //临时医嘱已确认，不可新增子药
                if ("0".equals(createReqVO.getType())) {
                    DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(createReqVO.getPid());
                    if (dialysisAdviceDO != null && "1".equals(dialysisAdviceDO.getAdviceState())) {
                        //throw new ServiceException(GlobalErrorCodeConstants.ADVICE_SUCCESS);
                    }
                }
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                DialysisAdviceDO dialysisAdvice = DialysisAdviceConvert.INSTANCE.convert(createReqVO);
                dialysisAdvice.setDateWeek(DateUtil.beginOfDay(createReqVO.getStartTime()));
                dialysisAdvice.setChildName("1");
                //查询是否是耗材
                HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdvice.getAdviceId());
                if (!StringUtils.isEmpty(hisConsumablesDO)) {
                    dialysisAdvice.setDrugType(2);
                    dialysisAdvice.setSpecification(hisConsumablesDO.getConsumSpec());
                    dialysisAdvice.setChildName(hisConsumablesDO.getEnabledMark());
                }
                if (StringUtils.isEmpty(hisConsumablesDO)) {
                    HisDrugDO hisDrugDO = hisDrugMapper.selectOne(HisDrugDO::getFDrugId, dialysisAdvice.getAdviceId());
                    if (!StringUtils.isEmpty(hisDrugDO)) {
                        dialysisAdvice.setChildName(hisDrugDO.getFEnabledMark());
                    }
                }
                if (!StringUtils.isEmpty(createReqVO.getAdviceName()) && (createReqVO.getAdviceName().equals("静脉注射") ||createReqVO.getAdviceName().equals("肌肉注射") ||createReqVO.getAdviceName().equals("皮下注射"))) {
                    dialysisAdvice.setDrugType(2);
                }
                // 查询是否是药品

                //患者来源
                PatientDO patientDO = patientMapper.selectById(createReqVO.getPatientId());
                if (patientDO != null) {
                    dialysisAdvice.setSpellName(patientDO.getSpellName());
                    dialysisAdvice.setPatientSource(patientDO.getPatientSource());
                    dialysisAdvice.setDialyzeNo(patientDO.getDialyzeNo());
                    dialysisAdvice.setPatientNickName(patientDO.getNickName());
                }
                if (arrangeClassesDO != null) {
                    dialysisAdvice.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                    dialysisAdvice.setFacilityId(arrangeClassesDO.getFacilityId());
                    dialysisAdvice.setDialyzeName(arrangeClassesDO.getDialysisName());
                    dialysisAdvice.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                    dialysisAdvice.setWeekDay(arrangeClassesDO.getDayState());
                }
                //
                dialysisAdviceMapper.insert(dialysisAdvice);
                dialysisAdviceDOList.add(dialysisAdvice);

                //子药
                if (CollectionUtil.isNotEmpty(createReqVO.getChildren())) {
                    List<DialysisAdviceDO> dialysisAdviceDOS = DialysisAdviceConvert.INSTANCE.convertList2(createReqVO.getChildren());
                    dialysisAdviceDOS = dialysisAdviceDOS.stream().peek(dialysisAdviceDO -> {
                        dialysisAdviceDO.setPid(dialysisAdvice.getId());
                        dialysisAdviceDO.setType(createReqVO.getType());
                        dialysisAdviceDO.setSpellName(patientDO == null ? null : patientDO.getSpellName());
                        dialysisAdviceDO.setStartTime(createReqVO.getStartTime());
                        dialysisAdviceDO.setChildName("1");
                        if (arrangeClassesDO != null) {
                            dialysisAdviceDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                            dialysisAdviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
                            dialysisAdviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());
                            dialysisAdviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                            dialysisAdviceDO.setWeekDay(arrangeClassesDO.getDayState());
                        }
                        /*dialysisAdviceDO.setAdviceTime(createReqVO.getAdviceTime());
                        dialysisAdviceDO.setDateWeek(createReqVO.getDateWeek());*/
                        //查询是否是耗材
                        HisConsumablesDO hisConsumablesDO1 = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO.getAdviceId());
                        if (!StringUtils.isEmpty(hisConsumablesDO1)) {
                            dialysisAdvice.setDrugType(2);
                            dialysisAdviceDO.setChildName(hisConsumablesDO1.getEnabledMark());
                        }
                        if (StringUtils.isEmpty(hisConsumablesDO1)) {
                            HisDrugDO hisDrugDO = hisDrugMapper.selectOne(HisDrugDO::getFDrugId, dialysisAdviceDO.getAdviceId());
                            if (!StringUtils.isEmpty(hisDrugDO)) {
                                dialysisAdviceDO.setChildName(hisDrugDO.getFEnabledMark());
                            }
                        }
                        if (!StringUtils.isEmpty(dialysisAdviceDO.getAdviceName()) && (dialysisAdviceDO.getAdviceName().equals("静脉注射") ||dialysisAdviceDO.getAdviceName().equals("肌肉注射") ||dialysisAdviceDO.getAdviceName().equals("皮下注射"))) {
                            dialysisAdvice.setDrugType(2);
                        }
                    }).collect(Collectors.toList());
                    dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    dialysisAdviceDOList.addAll(dialysisAdviceDOS);
                }

                AdminUserRespDTO user = adminUserApi.getUser(dialysisAdvice.getAdviceUser());
                String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);

                if (!StringUtils.isEmpty(user) && !org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOList) && !StringUtils.isEmpty(synFlag) && !"1".equals(createReqVO.getType())) {
                    dialysisAdviceDOList.forEach(dialysisAdviceDO -> {
                        Long adviceId = dialysisAdviceDO.getAdviceId();
                        if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO.getType()) && "1".equals(dialysisAdviceDO.getChildName())) {
                            try {
                                Thread.sleep(100);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();

                            PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO.getPatientId());
                            if (!StringUtils.isEmpty(patientDO1)) {
                                synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                            }
                            //Date dateWeek = createReqVO.getDateWeek();
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            if (StringUtils.isEmpty(dialysisAdviceDO.getStartTime())) {

                                String format = simpleDateFormat.format(new Date());

                                synToHisAdviceVo.setDpDate(format);
                            }else {
                                synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO.getStartTime()));
                            }

                            synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO.getAdviceId()));
                            synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                            synToHisAdviceVo.setDrugUsage(dialysisAdviceDO.getDrugWay());
                            synToHisAdviceVo.setOnceUsing(dialysisAdviceDO.getOneNo());
                            synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO.getFpreparaUnit());
                            synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO.getId()));
                            synToHisAdviceVo.setDoctorMobile(user.getMobile());
                            synToHisAdviceVo.setDrugName(dialysisAdviceDO.getAdviceName());
                            synToHisAdviceVo.setDrugDays(1);
                            synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                            if (StringUtils.isEmpty(dialysisAdviceDO.getPrescribeNo())) {
                                dialysisAdviceDO.setPrescribeNo("1");
                            }
                            if (StringUtils.isEmpty(dialysisAdviceDO.getOneNo())) {
                                dialysisAdviceDO.setOneNo("1");
                            }
                            synToHisAdviceVo.setDrugNum(dialysisAdviceDO.getPrescribeNo());
                            DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                            synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                            synToHisAdviceVos.add(synToHisAdviceVo);
                            AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                            if (!StringUtils.isEmpty(adviceResult)) {
                                AdviceData data = adviceResult.getData();
                                if (-1 == data.getInfcode()) {
                                    throw new ServiceException(data.getInfcode(), data.getMessage());
                                }else if (0 == data.getInfcode()) {
                                    // 更新advice
                                    List<AdviceOutput> output = data.getOutput();
                                    if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                        AdviceOutput adviceOutput = output.get(0);
                                        dialysisAdviceDO.setDpId(adviceOutput.getDpId());
                                        dialysisAdviceDO.setDpdId(adviceOutput.getDpdId());
                                        dialysisAdviceMapper.updateById(dialysisAdviceDO);
                                    }
                                }
                            }else {
                                throw new ServiceException(-1, "同步失败");
                            }
                        }

                    });
                }
                // 返回
                return dialysisAdvice.getId();
            } else {
                log.info("没有拿到，离开了.......");
                return null;
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("新增医嘱异常信息:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
        return null;
    }

    private AdviceResult postUrl(String url, List<SynToHisAdviceVo> synToHisAdviceVo) {
        synToHisAdviceVo.forEach(synToHisAdviceVo1 -> {
            // 查询给药途径
            if (!StringUtils.isEmpty(synToHisAdviceVo1.getDrugUsage())) {
                String drugUsage = synToHisAdviceVo1.getDrugUsage();
                String usageTypeId = "314937670834849029";
                HisDictData hisDictData = hisDictDataMapper.selectOne(HisDictData::getFDictionaryTypeId, usageTypeId, HisDictData::getFHmsValue, drugUsage);
                if (!StringUtils.isEmpty(hisDictData)) {
                    synToHisAdviceVo1.setDrugUsage(hisDictData.getFId());
                }else {
                    synToHisAdviceVo1.setDrugUsage("15");
                }
            }

            if (!StringUtils.isEmpty(synToHisAdviceVo1.getDrugInterval())) {
                String drugInterval = synToHisAdviceVo1.getDrugInterval();
                String interval = "308773635894543621";
                HisDictData hisDictData = hisDictDataMapper.selectOne(HisDictData::getFDictionaryTypeId, interval, HisDictData::getFHmsValue, drugInterval);
                if (!StringUtils.isEmpty(hisDictData)) {
                    synToHisAdviceVo1.setDrugInterval(hisDictData.getFId());
                }/*else {
                    synToHisAdviceVo1.setDrugInterval("15");
                }*/
            }
        });

        CloseableHttpClient httpClient = HttpClients.createDefault();
        Gson gson = new Gson();
        String req = gson.toJson(synToHisAdviceVo);
        log.info("requestParam:{}",req);
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).setSocketTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        StringEntity stringEntity = new StringEntity(req, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            HttpResponse response = httpClient.execute(httpPost);
            // 读取响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    result.append(line.trim());
                }
                if (!StringUtils.isEmpty(result)) {
                    String s = result.toString();
                    AdviceResult adviceResult = gson.fromJson(s, AdviceResult.class);
                    log.info("result:{}",adviceResult);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        return adviceResult;
                    }
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try{
                assert httpClient != null;
                httpClient.close();
            } catch (Exception ee){
                ee.printStackTrace();
            }

        }
        return null;
    }

    private AdviceResult deleteUrl(String url, List<AdviceDelete> adviceDeleteVo) {

        CloseableHttpClient httpClient = HttpClients.createDefault();
        Gson gson = new Gson();
        String req = gson.toJson(adviceDeleteVo);
        log.info("deleteReq:{}",req);
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).setSocketTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        StringEntity stringEntity = new StringEntity(req, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            HttpResponse response = httpClient.execute(httpPost);
            // 读取响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = br.readLine()) != null) {
                    result.append(line.trim());
                }
                if (!StringUtils.isEmpty(result)) {
                    String s = result.toString();
                    AdviceResult adviceResult = gson.fromJson(s, AdviceResult.class);
                    log.info("deleteResult:{}",adviceResult);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        return adviceResult;
                    }
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try{
                assert httpClient != null;
                httpClient.close();
            } catch (Exception ee){
                ee.printStackTrace();
            }

        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTempAdvice(DialysisAdviceTempSaveReqVO tempSaveReqVO, HttpServletRequest request) {
        String type = tempSaveReqVO.getType();
        long oldId = tempSaveReqVO.getId();
        long userId = StpUtil.getLoginIdAsLong();
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(tempSaveReqVO.getId());
        //修改执行时间和提醒日期 执行频率
        dialysisAdviceDO.setType(type);
        if(!StringUtils.isEmpty(tempSaveReqVO.getUpdateActivateTime())) {
            dialysisAdviceDO.setStartTime(tempSaveReqVO.getUpdateActivateTime());
            dialysisAdviceDO.setWarnTime(DateUtil.beginOfDay(tempSaveReqVO.getUpdateActivateTime()));
            dialysisAdviceDO.setDateWeek(DateUtil.beginOfDay(tempSaveReqVO.getUpdateActivateTime()));
        }else{
            dialysisAdviceDO.setStartTime(new Date());
        }
        dialysisAdviceDO.setAdviceTime(new Date());
        if(!StringUtils.isEmpty(tempSaveReqVO.getUpdateFrequency())) {
            dialysisAdviceDO.setFrequency(tempSaveReqVO.getUpdateFrequency());
        }

        if (!StringUtils.isEmpty(userId)) {
            dialysisAdviceDO.setAdviceUser(userId);
        }
        List<DialysisAdviceDO> tempDialysisList = new ArrayList<>();
        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, dialysisAdviceDO.getPatientId())
                .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
        //患者来源
        PatientDO patientDO = patientMapper.selectById(dialysisAdviceDO.getPatientId());
        if (patientDO != null) {
            dialysisAdviceDO.setSpellName(patientDO.getSpellName());
            dialysisAdviceDO.setPatientSource(patientDO.getPatientSource());
            dialysisAdviceDO.setDialyzeNo(patientDO.getDialyzeNo());
            dialysisAdviceDO.setPatientNickName(patientDO.getNickName());
        }
        if (arrangeClassesDO != null) {
            dialysisAdviceDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
            dialysisAdviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
            dialysisAdviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());
            dialysisAdviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
            dialysisAdviceDO.setWeekDay(arrangeClassesDO.getDayState());
        }
        //药品名称
        HisDrugDO hisDrugDO = hisDrugMapper.selectById(dialysisAdviceDO.getAdviceId());
        if (!StringUtils.isEmpty(hisDrugDO)) {
            dialysisAdviceDO.setSpecification(hisDrugDO.getFDrugSpec());
            dialysisAdviceDO.setChildName(hisDrugDO.getFEnabledMark());

        }
        if (StringUtils.isEmpty(hisDrugDO)) {
            // 查询自建药
            DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, dialysisAdviceDO.getAdviceId());
            if (!StringUtils.isEmpty(drugDO1)) {
                hisDrugDO = new HisDrugDO();
                hisDrugDO.setFDrugName(drugDO1.getName());
                dialysisAdviceDO.setSpecification(drugDO1.getSpec());
            }
        }
        if (StringUtils.isEmpty(hisDrugDO)) {
            // 查询耗材
            HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO.getAdviceId());
            if (!StringUtils.isEmpty(hisConsumablesDO)) {
                hisDrugDO = new HisDrugDO();
                hisDrugDO.setFDrugName(hisConsumablesDO.getConsumName());
                hisDrugDO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                dialysisAdviceDO.setSpecification(hisConsumablesDO.getConsumSpec());
                dialysisAdviceDO.setChildName(hisConsumablesDO.getEnabledMark());
                dialysisAdviceDO.setDrugType(2);
            }
        }


        dialysisAdviceDO.setStopStatus("0");
        dialysisAdviceDO.setState("0");
        dialysisAdviceDO.setAdviceState("0");
        dialysisAdviceDO.setMedicateState(0);
        dialysisAdviceDO.setId(null);
        dialysisAdviceDO.setCreateTime(new Date());
        dialysisAdviceDO.setUpdateTime(new Date());
        dialysisAdviceDO.setChildName("1");

        //保存为临时医嘱
        dialysisAdviceMapper.insert(dialysisAdviceDO);
        tempDialysisList.add(dialysisAdviceDO);
        //长期医嘱的子药
        List<DialysisAdviceDO> newDisAdviceDOList = null;
        List<DialysisAdviceDO> disAdviceDOList = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPid, oldId);
        if(CollectionUtil.isNotEmpty(disAdviceDOList)){
            newDisAdviceDOList = disAdviceDOList.stream().map(v -> {
                v.setPid(dialysisAdviceDO.getId());
                v.setId(null);
                v.setAdviceTime(dialysisAdviceDO.getAdviceTime());
                v.setStopStatus("0");
                v.setState("0");
                v.setAdviceState("0");
                v.setMedicateState(0);
                v.setStartTime(dialysisAdviceDO.getStartTime());
                v.setWarnTime(dialysisAdviceDO.getWarnTime());
                v.setDateWeek(dialysisAdviceDO.getDateWeek());
                v.setCreateTime(new Date());
                v.setUpdateTime(new Date());
                v.setChildName("1");
                v.setType("0");
                if (!StringUtils.isEmpty(userId)) {
                    v.setAdviceUser(userId);
                }
                v.setFacilityId(arrangeClassesDO.getFacilityId());
                if (StringUtils.isEmpty(v.getPrescribeNo())) {
                    v.setPrescribeNo("1");
                }
                //药品名称
                HisDrugDO hisDrugDO1 = hisDrugMapper.selectById(v.getAdviceId());
                if (!StringUtils.isEmpty(hisDrugDO1)) {
                    v.setSpecification(hisDrugDO1.getFDrugSpec());
                    v.setChildName(hisDrugDO1.getFEnabledMark());
                }
                if (StringUtils.isEmpty(hisDrugDO1)) {
                    // 查询自建药
                    DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, v.getAdviceId());
                    if (!StringUtils.isEmpty(drugDO1)) {
                        hisDrugDO1 = new HisDrugDO();
                        hisDrugDO1.setFDrugName(drugDO1.getName());
                        v.setSpecification(drugDO1.getSpec());
                        v.setChildName(hisDrugDO1.getFEnabledMark());
                    }
                }
                if (StringUtils.isEmpty(hisDrugDO1)) {
                    // 查询耗材
                    HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, v.getAdviceId());
                    if (!StringUtils.isEmpty(hisConsumablesDO)) {
                        hisDrugDO1 = new HisDrugDO();
                        hisDrugDO1.setFDrugName(hisConsumablesDO.getConsumName());
                        hisDrugDO1.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                        v.setSpecification(hisConsumablesDO.getConsumSpec());
                        v.setChildName(hisConsumablesDO.getEnabledMark());
                        v.setDrugType(2);
                    }
                }

                v.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                v.setDialyzeName(arrangeClassesDO.getDialysisName());
                return v;
            }).collect(Collectors.toList());
        }
        //保存子药到临时医嘱
        if(CollectionUtil.isNotEmpty(newDisAdviceDOList)) {
            dialysisAdviceMapper.insertBatch(newDisAdviceDOList);
            tempDialysisList.addAll(newDisAdviceDOList);
        }

        String SystemDeptId = request.getHeader("SystemDeptId");
        String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
        if (!StringUtils.isEmpty(synFlag) && !org.springframework.util.CollectionUtils.isEmpty(tempDialysisList)) {
            AdminUserRespDTO user = adminUserApi.getUser(tempDialysisList.get(0).getAdviceUser());
            tempDialysisList.forEach(dialysisAdviceDO1 -> {
                ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                Long adviceId = dialysisAdviceDO1.getAdviceId();
                if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO1.getType()) && "1".equals(dialysisAdviceDO1.getChildName())) {
                    SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO1.getPatientId());
                    if (!StringUtils.isEmpty(patientDO1)) {
                        synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                    }
                    //Date dateWeek = createReqVO.getDateWeek();
                    //Date startTime = createReqVO.getStartTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getStartTime())) {

                        String format = simpleDateFormat.format(new Date());

                        synToHisAdviceVo.setDpDate(format);
                    }else {
                        synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO1.getStartTime()));
                    }
                    synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO1.getAdviceId()));
                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                    synToHisAdviceVo.setDrugUsage(dialysisAdviceDO1.getDrugWay());
                    synToHisAdviceVo.setOnceUsing(dialysisAdviceDO1.getOneNo());
                    synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO1.getFpreparaUnit());
                    synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO1.getId()));
                    synToHisAdviceVo.setDoctorMobile(user.getMobile());
                    synToHisAdviceVo.setDrugName(dialysisAdviceDO1.getAdviceName());
                    synToHisAdviceVo.setDrugDays(1);
                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                        dialysisAdviceDO1.setPrescribeNo("1");
                    }
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getOneNo())) {
                        dialysisAdviceDO1.setOneNo("1");
                    }
                    synToHisAdviceVo.setDrugNum(dialysisAdviceDO1.getPrescribeNo());
                    DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                    synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                    synToHisAdviceVos.add(synToHisAdviceVo);
                    AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        AdviceData data = adviceResult.getData();
                        if (-1 == data.getInfcode()) {
                            throw new ServiceException(data.getInfcode(), data.getMessage());
                        }else if (0 == data.getInfcode()) {
                            // 更新advice
                            List<AdviceOutput> output = data.getOutput();
                            if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                AdviceOutput adviceOutput = output.get(0);
                                dialysisAdviceDO1.setDpId(adviceOutput.getDpId());
                                dialysisAdviceDO1.setDpdId(adviceOutput.getDpdId());
                                dialysisAdviceMapper.updateById(dialysisAdviceDO1);
                            }
                        }
                    }else {
                        throw new ServiceException(-1, "返回为null,同步失败");
                    }
                }

            });
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDialysisAdvice(DialysisAdviceUpdateReqVO updateReqVO, HttpServletRequest request) {
        if (updateReqVO.getStartTime() != null) {
            boolean sameDay = LocalDateTimeUtil.isSameDay(LocalDateTimeUtil.of(updateReqVO.getStartTime()), LocalDateTime.now());
            if (!sameDay) {
                //throw new ServiceException(GlobalErrorCodeConstants.UPDATE_ADVICE_INFO);
            }
        }
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:updateDialysisAdvice:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                DialysisAdviceDO updateObj = DialysisAdviceConvert.INSTANCE.convert(updateReqVO);
                //确认医嘱
                if (StrUtil.isNotEmpty(updateReqVO.getIds())) {
                    List<Long> idList = Arrays.stream(updateReqVO.getIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    dialysisAdviceMapper.update(new DialysisAdviceDO(), new LambdaUpdateWrapper<DialysisAdviceDO>()
                            .set(DialysisAdviceDO::getAdviceState, updateReqVO.getAdviceState())
                            .in(DialysisAdviceDO::getId, idList)
                            .or()
                            .in(DialysisAdviceDO::getPid, idList));


                    return;
                }

                //如果是续开，旧医嘱停用，新医嘱推送
                if (StrUtil.isNotEmpty(updateReqVO.getContinuation())) {
                    long userId = StpUtil.getLoginIdAsLong();
                    DialysisAdviceDO dialysisAdviceDO1 = new DialysisAdviceDO();
                    dialysisAdviceDO1.setStopTime(DateUtil.date());
                    dialysisAdviceDO1.setStopReasoon("已续开");
                    dialysisAdviceDO1.setStopUserId(userId);
                    dialysisAdviceMapper.update(dialysisAdviceDO1, new LambdaUpdateWrapper<DialysisAdviceDO>()
                            .set(DialysisAdviceDO::getStopStatus, "1")
                            .eq(DialysisAdviceDO::getPid, updateReqVO.getId())
                            .or()
                            .eq(DialysisAdviceDO::getId, updateReqVO.getId()));
                    updateObj.setId(null);
                    dialysisAdviceMapper.insert(updateObj);
                    if (CollectionUtil.isNotEmpty(updateReqVO.getChildren())) {
                        List<DialysisAdviceDO> dialysisAdviceDOS = DialysisAdviceConvert.INSTANCE.convertList2(updateReqVO.getChildren());
                        dialysisAdviceDOS = dialysisAdviceDOS.stream().peek(dialysisAdviceDO -> {
                            dialysisAdviceDO.setId(null);
                            dialysisAdviceDO.setPid(updateObj.getId());
                        }).collect(Collectors.toList());
                        dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    }
                    return;
                }
                ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                        .eqIfPresent(ArrangeClassesDO::getPatientId, updateReqVO.getPatientId())
                        .eqIfPresent(ArrangeClassesDO::getTempType, 0)
                        .betweenIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()), DateUtil.endOfDay(new Date())));
                //患者来源
                PatientDO patientDO = patientMapper.selectById(updateReqVO.getPatientId());
                if (patientDO != null) {
                    updateObj.setPatientSource(patientDO.getPatientSource());
                    updateObj.setDialyzeNo(patientDO.getDialyzeNo());
                    updateObj.setPatientNickName(patientDO.getNickName());
                    updateObj.setSpellName(patientDO.getSpellName());
                }
                if (arrangeClassesDO != null) {
                    updateObj.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                    updateObj.setFacilityId(arrangeClassesDO.getFacilityId());
                    updateObj.setDialyzeName(arrangeClassesDO.getDialysisName());
                    updateObj.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                    updateObj.setWeekDay(arrangeClassesDO.getDayState());
                }
                DialysisAdviceDO dialysisAdviceDO1 = dialysisAdviceMapper.selectById(updateObj.getId());
                if ("0".equals(updateObj.getType())){
                    updateObj.setDpdId(null);
                    updateObj.setDpId(null);
                }
                //查询是否是耗材
                HisConsumablesDO hisConsumablesDO1 = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, updateReqVO.getAdviceId());
                if (!StringUtils.isEmpty(hisConsumablesDO1)) {
                    updateObj.setDrugType(2);
                    updateObj.setChildName(hisConsumablesDO1.getEnabledMark());
                }
                if (StringUtils.isEmpty(hisConsumablesDO1)) {
                    HisDrugDO hisDrugDO = hisDrugMapper.selectOne(HisDrugDO::getFDrugId, updateReqVO.getAdviceId());
                    if (!StringUtils.isEmpty(hisDrugDO)) {
                        updateObj.setChildName(hisDrugDO.getFEnabledMark());
                    }
                }

                dialysisAdviceMapper.updateById(updateObj);

                List<DialysisAdviceDO> deleteDialysisAdviceList = new ArrayList<>();
                if (!StringUtils.isEmpty(dialysisAdviceDO1)) {
                    updateObj.setDpId(dialysisAdviceDO1.getDpId());
                    if (!StringUtils.isEmpty(dialysisAdviceDO1.getDpId())) {
                        Integer countById = getCountById(dialysisAdviceDO1.getDpId());
                        if(!StringUtils.isEmpty(countById) && 1== countById) {
                            dialysisAdviceDO1.setDpId(null);
                        }
                    }
                    deleteDialysisAdviceList.add(dialysisAdviceDO1);
                }


                List<DialysisAdviceDO> dialysisAdviceDOList = new ArrayList<>();
                dialysisAdviceDOList.add(updateObj);
                List<DialysisAdviceDO> oldDialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, updateObj.getId()));
                deleteDialysisAdviceList.addAll(oldDialysisAdviceDOS);
                dialysisAdviceMapper.delete(new LambdaQueryWrapper<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, updateObj.getId()));
                if (CollectionUtil.isNotEmpty(updateReqVO.getChildren())) {
                    List<DialysisAdviceCreateReqVO> collect = updateReqVO.getChildren().stream().peek(dialysis -> {
                        dialysis.setPid(updateObj.getId());
                        dialysis.setSpellName(updateReqVO.getSpellName());
                        dialysis.setStartTime(updateObj.getStartTime());
                        dialysis.setType(updateReqVO.getType());
                        dialysis.setAdviceTime(updateReqVO.getAdviceTime());
                        dialysis.setWarnTime(updateReqVO.getWarnTime());
                        dialysis.setDateWeek(updateReqVO.getDateWeek());
                        dialysis.setAdviceUser(updateObj.getAdviceUser());
                        dialysis.setDialyzeNo(updateReqVO.getDialyzeNo());
                        dialysis.setMedicateState(updateReqVO.getMedicateState());
                        if (arrangeClassesDO != null) {
                            dialysis.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                            dialysis.setFacilityId(arrangeClassesDO.getFacilityId());
                            dialysis.setDialyzeName(arrangeClassesDO.getDialysisName());
                            dialysis.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                            dialysis.setWeekDay(arrangeClassesDO.getDayState());
                        }
                        //查询是否是耗材
                        HisConsumablesDO hisConsumablesDO2 = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysis.getAdviceId());
                        if (!StringUtils.isEmpty(hisConsumablesDO2)) {
                            dialysis.setDrugType(2);
                            dialysis.setChildName(hisConsumablesDO2.getEnabledMark());
                        }
                        if (StringUtils.isEmpty(hisConsumablesDO2)) {
                            HisDrugDO hisDrugDO = hisDrugMapper.selectOne(HisDrugDO::getFDrugId, dialysis.getAdviceId());
                            if (!StringUtils.isEmpty(hisDrugDO)) {
                                dialysis.setChildName(hisDrugDO.getFEnabledMark());
                            }
                        }
                    }).collect(Collectors.toList());
                    List<DialysisAdviceDO> dialysisAdviceDOS = DialysisAdviceConvert.INSTANCE.convertList2(collect);
                    //修改子级

                    dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    dialysisAdviceDOList.addAll(dialysisAdviceDOS);
                }

                if (CollectionUtil.isNotEmpty(deleteDialysisAdviceList)) {
                    for (DialysisAdviceDO dialysisAdviceDO : deleteDialysisAdviceList) {
                        deleteDialysisAdviceSingle(dialysisAdviceDO,request);
                    }
                }

                AdminUserRespDTO user = adminUserApi.getUser(updateObj.getAdviceUser());
                String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);

                if (!StringUtils.isEmpty(user) && !org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOList) && !StringUtils.isEmpty(synFlag) && !"1".equals(updateReqVO.getType())) {

                    ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                    dialysisAdviceDOList.forEach(dialysisAdviceDO -> {

                    Long adviceId = dialysisAdviceDO.getAdviceId();
                    if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO.getType())) {
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();

                        PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO.getPatientId());
                        if (!StringUtils.isEmpty(patientDO1)) {
                            synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                        }
                        //Date dateWeek = createReqVO.getDateWeek();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (StringUtils.isEmpty(dialysisAdviceDO.getStartTime())) {

                            String format = simpleDateFormat.format(new Date());

                            synToHisAdviceVo.setDpDate(format);
                        }else {
                            synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO.getStartTime()));
                        }
                        synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO.getAdviceId()));
                        synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                        synToHisAdviceVo.setDrugUsage(dialysisAdviceDO.getDrugWay());
                        synToHisAdviceVo.setOnceUsing(dialysisAdviceDO.getOneNo());
                        synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO.getFpreparaUnit());
                        synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO.getId()));
                        synToHisAdviceVo.setDoctorMobile(user.getMobile());
                        synToHisAdviceVo.setDrugName(dialysisAdviceDO.getAdviceName());
                        synToHisAdviceVo.setHmsDpId(dialysisAdviceDO.getDpId());
                        synToHisAdviceVo.setDrugDays(1);
                        synToHisAdviceVo.setDrugInterval(dialysisAdviceDO.getFrequency());
                        if (StringUtils.isEmpty(dialysisAdviceDO.getPrescribeNo())) {
                            dialysisAdviceDO.setPrescribeNo("1");
                        }
                        if (StringUtils.isEmpty(dialysisAdviceDO.getOneNo())) {
                            dialysisAdviceDO.setOneNo("1");
                        }
                        synToHisAdviceVo.setDrugNum(dialysisAdviceDO.getPrescribeNo());
                        DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                        synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                        synToHisAdviceVos.add(synToHisAdviceVo);

                    }
                    });
                    if (!org.springframework.util.CollectionUtils.isEmpty(synToHisAdviceVos)) {
                        AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                        if (!StringUtils.isEmpty(adviceResult)) {
                            AdviceData data = adviceResult.getData();
                            if (-1 == data.getInfcode()) {
                                throw new ServiceException(data.getInfcode(), data.getMessage());
                            } else if (0 == data.getInfcode()) {
                                // 更新advice
                                List<AdviceOutput> output = data.getOutput();
                                if (!org.springframework.util.CollectionUtils.isEmpty(output)) {
                                   output.forEach(outData -> {
                                       for (DialysisAdviceDO dialysisAdviceDO: dialysisAdviceDOList) {
                                           if (outData.getHmsAdvId().equals(String.valueOf(dialysisAdviceDO.getId()))) {
                                               dialysisAdviceDO.setDpId(outData.getDpId());
                                               dialysisAdviceDO.setDpdId(outData.getDpdId());
                                               dialysisAdviceMapper.updateById(dialysisAdviceDO);
                                           }
                                       }

                                   });
                                }
                            }
                        }else {
                            throw new ServiceException(-1, "同步失败");
                        }
                    }
                }
            } else {
                log.info("没有拿到，离开了.......");
                return;
            }
        }catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        }
        catch (Exception e) {
            e.printStackTrace();
            log.error("修改医嘱异常信息:{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    @Override
    public void deleteDialysisAdvice(Long id,HttpServletRequest request) {
        // 删除
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
        String SystemDeptId = request.getHeader("SystemDeptId");
        if (StringUtils.isEmpty(SystemDeptId)) {
            throw new ServiceException(-1, "systemDeptId为空，请重新登录");
        }

        String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
        if (!StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getAdviceId()) && dialysisAdviceDO.getAdviceId() > 10000000
                && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && "0".equals(dialysisAdviceDO.getType()) && !StringUtils.isEmpty(dialysisAdviceDO.getDpId()))  {
            AdviceDeleteVo adviceDeleteVo = new AdviceDeleteVo();
            List<AdviceDelete> adviceDeleteList = new ArrayList<>();
            AdviceDelete adviceDelete = new AdviceDelete();
            adviceDelete.setDpId(dialysisAdviceDO.getDpId());
            adviceDelete.setHmsAdvId(String.valueOf(dialysisAdviceDO.getId()));
            adviceDelete.setDrugName(dialysisAdviceDO.getAdviceName());
            adviceDelete.setOnceUsing(dialysisAdviceDO.getOneNo());
            adviceDelete.setDrugNum(dialysisAdviceDO.getPrescribeNo());
            adviceDeleteList.add(adviceDelete);
            adviceDeleteVo.setAdviceDeleteList(adviceDeleteList);

            AdviceResult adviceResult = deleteUrl(deleteUrl, adviceDeleteList);
            if (!StringUtils.isEmpty(adviceResult)) {
                AdviceData data = adviceResult.getData();
                if (-1 == data.getInfcode()) {
                    throw new ServiceException(data.getInfcode(), data.getMessage());
                } else if (0 == data.getInfcode()) {

                }
            }else {
                throw new ServiceException(-1, "删除失败");
            }

        }

        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
        if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS)) {

            for (DialysisAdviceDO adviceDO : dialysisAdviceDOS) {
                if (!StringUtils.isEmpty(adviceDO.getAdviceId()) && adviceDO.getAdviceId() > 10000000 && "0".equals(adviceDO.getType()) && !StringUtils.isEmpty(adviceDO.getDpId()) && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) ) {
                    AdviceDeleteVo adviceDeleteVo1 = new AdviceDeleteVo();
                    List<AdviceDelete> adviceDeleteList1 = new ArrayList<>();
                    AdviceDelete adviceDelete1 = new AdviceDelete();
                    adviceDelete1.setDpId(adviceDO.getDpId());
                    adviceDelete1.setHmsAdvId(String.valueOf(adviceDO.getId()));
                    adviceDelete1.setDrugName(adviceDO.getAdviceName());
                    adviceDelete1.setOnceUsing(adviceDO.getOneNo());
                    adviceDelete1.setDrugNum(adviceDO.getPrescribeNo());
                    adviceDeleteList1.add(adviceDelete1);
                    adviceDeleteVo1.setAdviceDeleteList(adviceDeleteList1);

                    AdviceResult adviceResult1 = deleteUrl(deleteUrl, adviceDeleteList1);
                    if (!StringUtils.isEmpty(adviceResult1)) {
                        AdviceData data = adviceResult1.getData();
                        if (-1 == data.getInfcode()) {
                            throw new ServiceException(data.getInfcode(), data.getMessage());
                        } else if (0 == data.getInfcode()) {

                        }
                    }
                    else {
                        throw new ServiceException(-1, "返回为空，删除失败");
                    }
                }

            }
        }

        dialysisAdviceMapper.deleteById(id);
        dialysisAdviceMapper.delete(new LambdaUpdateWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
    }

    public void deleteDialysisAdviceSingle(DialysisAdviceDO dialysisAdviceDO,HttpServletRequest request) {
        // 删除
        //DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
        String SystemDeptId = request.getHeader("SystemDeptId");
        String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
        if (!StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getAdviceId()) && dialysisAdviceDO.getAdviceId() > 10000000
                && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && "0".equals(dialysisAdviceDO.getType()) && !StringUtils.isEmpty(dialysisAdviceDO.getDpId()))  {
            AdviceDeleteVo adviceDeleteVo = new AdviceDeleteVo();
            List<AdviceDelete> adviceDeleteList = new ArrayList<>();
            AdviceDelete adviceDelete = new AdviceDelete();
            adviceDelete.setDpId(dialysisAdviceDO.getDpId());
            adviceDelete.setHmsAdvId(String.valueOf(dialysisAdviceDO.getId()));
            adviceDelete.setDrugName(dialysisAdviceDO.getAdviceName());
            adviceDelete.setDrugNum(dialysisAdviceDO.getPrescribeNo());
            adviceDelete.setOnceUsing(dialysisAdviceDO.getOneNo());
            adviceDeleteList.add(adviceDelete);
            adviceDeleteVo.setAdviceDeleteList(adviceDeleteList);

            AdviceResult adviceResult = deleteUrl(deleteUrl, adviceDeleteList);
            if (!StringUtils.isEmpty(adviceResult)) {
                AdviceData data = adviceResult.getData();
                if (!StringUtils.isEmpty(data.getMessage()) && data.getMessage().contains("已确认")) {
                    throw new ServiceException(data.getInfcode(), data.getMessage());
                } else if (0 == data.getInfcode()) {

                }
            }else {
                throw new ServiceException(-1, "删除失败");
            }

        }
    }

    @Override
    public DialysisAdviceRespVO getDialysisAdvice(Long id) {
        //仅当日医嘱可修改
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
        DialysisAdviceRespVO dialysisAdviceRespVO = DialysisAdviceConvert.INSTANCE.convert(dialysisAdviceDO);
        if (!StringUtils.isEmpty(dialysisAdviceRespVO)) {

        }
        //if (dialysisAdviceRespVO.getStartTime() != null) {
        //    boolean sameDay = LocalDateTimeUtil.isSameDay(LocalDateTimeUtil.of(dialysisAdviceRespVO.getStartTime()), LocalDateTime.now());
        //    if (!sameDay) {
        //        throw new ServiceException(GlobalErrorCodeConstants.UPDATE_ADVICE_INFO);
        //    }
        //}

        //子药
        UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(dialysisAdviceRespVO.getAdviceUser());
        dialysisAdviceRespVO.setAdviceUserName(adminUserInfo == null ? null : adminUserInfo.getNickname());
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPid, id);
        if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
            //开嘱医生
            dialysisAdviceRespVO.setDialysisAdviceDOS(dialysisAdviceDOS);
        }
        return dialysisAdviceRespVO;
    }

    @Override
    public List<DialysisAdviceDO> getDialysisAdviceList(Collection<Long> ids) {
        return dialysisAdviceMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DialysisAdviceRespVO> getDialysisAdvicePage(DialysisAdvicePageReqVO pageReqVO) {
        if(!com.thj.boot.common.utils.StringUtils.isEmpty(pageReqVO.getMore())) {
            String more = pageReqVO.getMore();
            String[] split = more.split("");
            String join = String.join("%", split);
            pageReqVO.setMore(join);
        }
        // 查询符合条件的患者的排班
        List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, 0)
                .eqIfPresent(ArrangeClassesDO::getDayState, pageReqVO.getWeekDay())
                .inIfPresent(ArrangeClassesDO::getFacilitySubareaId, pageReqVO.getFacilitySubareaId())
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, pageReqVO.getStartTime() != null ? DateUtil.beginOfDay(pageReqVO.getStartTime()) : pageReqVO.getStartTime(), pageReqVO.getEndTime() != null ? DateUtil.endOfDay(pageReqVO.getEndTime()) : pageReqVO.getEndTime())
        ).stream().collect(Collectors.toList());

        if (!org.springframework.util.CollectionUtils.isEmpty(arrangeClassesDOList)) {
            List<Long> patIdList = arrangeClassesDOList.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toList());
            pageReqVO.setPatientIdList(patIdList);
            pageReqVO.setWeekDay(null);
            pageReqVO.setFacilitySubareaId(null);
        }
        MPJLambdaWrapper<DialysisAdviceDO> wrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
        wrapper
                .leftJoin(FacilityDO.class,FacilityDO :: getId,DialysisAdviceDO::getFacilityId)
                .leftJoin(DictDataDO.class, d -> d.eq(DictDataDO::getValue, DialysisAdviceDO::getFrequency)
                        .eq(DictDataDO::getDictType, "frequency"))
                .select(DictDataDO :: getLabel)
                .selectAll(DialysisAdviceDO.class)
                .select(FacilityDO :: getCode)
                .like(StrUtil.isNotEmpty(pageReqVO.getFrequencyLabel()),DictDataDO :: getLabel,pageReqVO.getFrequencyLabel())
                .eq(StrUtil.isNotEmpty(pageReqVO.getWeekDay()),DialysisAdviceDO::getWeekDay, pageReqVO.getWeekDay())
                .eq(StrUtil.isNotEmpty(pageReqVO.getAdviceState()),DialysisAdviceDO::getAdviceState, pageReqVO.getAdviceState())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getFacilitySubareaId()),DialysisAdviceDO::getFacilitySubareaId, pageReqVO.getFacilitySubareaId())
                .in(CollectionUtil.isNotEmpty(pageReqVO.getPatientIdList()),DialysisAdviceDO::getPatientId, pageReqVO.getPatientIdList())
                .eq(StrUtil.isNotEmpty(pageReqVO.getType()),DialysisAdviceDO::getType, pageReqVO.getType())
                .eq(StrUtil.isNotEmpty(pageReqVO.getPatientSource()),DialysisAdviceDO::getPatientSource, pageReqVO.getPatientSource())
                .eq(StrUtil.isNotEmpty(pageReqVO.getAdviceUser()),DialysisAdviceDO::getAdviceUser, pageReqVO.getAdviceUser())
                .like(StrUtil.isNotEmpty(pageReqVO.getKeyword()),DialysisAdviceDO::getAdviceName, pageReqVO.getKeyword())
                .like(StrUtil.isNotEmpty(pageReqVO.getAdviceName()),DialysisAdviceDO::getAdviceName,pageReqVO.getAdviceName())
                .eq(StrUtil.isNotEmpty(pageReqVO.getAdviceDesprition()),DialysisAdviceDO::getAdviceDesprition, pageReqVO.getAdviceDesprition())
                .eq(StrUtil.isNotEmpty(pageReqVO.getOneNo()),DialysisAdviceDO::getOneNo, pageReqVO.getOneNo())
                .eq(StrUtil.isNotEmpty(pageReqVO.getPrescribeNo()),DialysisAdviceDO::getPrescribeNo, pageReqVO.getPrescribeNo())
                .eq(StrUtil.isNotEmpty(pageReqVO.getFrequency()),DialysisAdviceDO::getFrequency, pageReqVO.getFrequency())
                .like(StrUtil.isNotEmpty(pageReqVO.getChildName()),DialysisAdviceDO::getChildName, pageReqVO.getChildName())
                .eq(pageReqVO.getActivateUser() != null,DialysisAdviceDO::getActivateUser, pageReqVO.getActivateUser())
                .eq(pageReqVO.getCheckUser() != null,DialysisAdviceDO::getCheckUser, pageReqVO.getCheckUser())
                .eq(StrUtil.isNotEmpty(pageReqVO.getRemark()),DialysisAdviceDO::getRemark, pageReqVO.getRemark())
                .eq(DialysisAdviceDO::getDeleted,false)
                .eq(pageReqVO.getPatientId() != null,DialysisAdviceDO::getPatientId, pageReqVO.getPatientId())
                .eq(StrUtil.isNotEmpty(pageReqVO.getStopStatus()),DialysisAdviceDO::getStopStatus, pageReqVO.getStopStatus())
                .between(pageReqVO.getStartTime() != null && pageReqVO.getEndTime() != null ,DialysisAdviceDO::getStartTime, pageReqVO.getStartTime() != null ? DateUtil.beginOfDay(pageReqVO.getStartTime()) : pageReqVO.getStartTime(), pageReqVO.getEndTime() != null ? DateUtil.endOfDay(pageReqVO.getEndTime()) : pageReqVO.getEndTime())
                .isNull("0".equals(pageReqVO.getCheck()), DialysisAdviceDO::getCheckUser)
                .isNotNull("1".equals(pageReqVO.getCheck()), DialysisAdviceDO::getCheckUser)
                .isNotNull(DialysisAdviceDO::getDateWeek)
                .and(i -> i
                        .isNull(DialysisAdviceDO::getDrugType)
                        .or()
                        .ne(DialysisAdviceDO::getDrugType,2))
                .and(StrUtil.isNotEmpty(pageReqVO.getMore()), i -> i
                        .like(DialysisAdviceDO::getPatientName, pageReqVO.getMore())
                        .or()
                        .like(DialysisAdviceDO::getSpellName, pageReqVO.getMore())
                        .or()
                        .like(DialysisAdviceDO::getDialyzeNo, pageReqVO.getMore()))
                .in(!org.springframework.util.CollectionUtils.isEmpty(pageReqVO.getDrugWay()),DialysisAdviceDO::getDrugWay, pageReqVO.getDrugWay())
                .orderByDesc(StrUtil.isNotEmpty(pageReqVO.getSort()) && "1".equals(pageReqVO.getSort()) ,DialysisAdviceDO::getStartTime)
                .orderByAsc(StrUtil.isNotEmpty(pageReqVO.getSort()) && "0".equals(pageReqVO.getSort()), FacilityDO :: getCode);
//        PageResult<DialysisAdviceDO> dialysisAdviceDOPageResult = dialysisAdviceMapper.selectPage(pageReqVO);
//        PageResult<DialysisAdviceRespVO> dialysisAdviceRespVOPageResult = DialysisAdviceConvert.INSTANCE.convertPage(dialysisAdviceDOPageResult);

        IPage<DialysisAdviceRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<DialysisAdviceRespVO> iPage = dialysisAdviceMapper.selectJoinPage(page,DialysisAdviceRespVO.class,wrapper);
        PageResult<DialysisAdviceRespVO> dialysisAdviceRespVOPageResult = new PageResult<>();
        dialysisAdviceRespVOPageResult.setList(iPage.getRecords());
        dialysisAdviceRespVOPageResult.setTotal(iPage.getTotal());
        List<DialysisAdviceRespVO> respVOPageResultList = dialysisAdviceRespVOPageResult.getList();
        if (CollectionUtil.isNotEmpty(respVOPageResultList)) {
            if (!org.springframework.util.CollectionUtils.isEmpty(arrangeClassesDOList)) {
                arrangeClassesDOList.forEach(arrangeClassesDO ->  {
                    for (DialysisAdviceRespVO dialysisAdviceRespVO : respVOPageResultList) {
                        if (dialysisAdviceRespVO.getPatientId().equals(arrangeClassesDO.getPatientId())) {
                            dialysisAdviceRespVO.setFacilityId(arrangeClassesDO.getFacilityId());
                        }
                    }

                });
            }


//            List<Long> facilityIdList = CollectionUtils.convertList(respVOPageResultList, DialysisAdviceRespVO::getFacilityId);
//            Map<Long, FacilityDO> longFacilityDOMap;
//            if (CollectionUtil.isNotEmpty(facilityIdList))
//                longFacilityDOMap = CollectionUtils.convertMap(facilityMapper.selectBatchIds(facilityIdList), FacilityDO::getId, item -> item);
//            else
//                longFacilityDOMap = null;
            List<Long> patientIdList = CollectionUtils.convertList(respVOPageResultList, DialysisAdviceRespVO::getPatientId);
            Map<Long, PatientDO> longPatientDOMap = CollectionUtils.convertMap(patientMapper.selectBatchIds(patientIdList), PatientDO::getId, item -> item);

            List<UserRespDTO> userRespDTOS = adminUserApi.adminUserList(new UserRespDTO());
            Map<Long, UserRespDTO> userRespDTOMap = CollectionUtils.convertMap(userRespDTOS, UserRespDTO::getId, userRespDTO -> userRespDTO);
            List<DialysisAdviceRespVO> collect = respVOPageResultList.stream().peek(dialysisAdviceRespVO -> {
                //机号
//                Opt.ofNullable(longFacilityDOMap).ifPresent(map -> Opt.ofNullable(map.get(dialysisAdviceRespVO.getFacilityId())).ifPresent(item -> dialysisAdviceRespVO.setFacilityName(item.getCode())));
                dialysisAdviceRespVO.setFacilityName(dialysisAdviceRespVO.getCode());
                dialysisAdviceRespVO.setFrequencyLabel(dialysisAdviceRespVO.getLabel());
                PatientDO patientDO = longPatientDOMap.get(dialysisAdviceRespVO.getPatientId());
                if (patientDO != null) {
                    dialysisAdviceRespVO.setSex(patientDO.getSex());
                    dialysisAdviceRespVO.setAge(patientDO.getAge() + "");
                    dialysisAdviceRespVO.setPatientName(patientDO.getName());
                    dialysisAdviceRespVO.setDialyzeNo(patientDO.getDialyzeNo());
                    dialysisAdviceRespVO.setPatientSource(patientDO.getPatientSource());
                    dialysisAdviceRespVO.setHospitalNo(patientDO.getHospitalNo());
                }
                buildAdminUser(userRespDTOMap, dialysisAdviceRespVO);
            }).collect(Collectors.toList());
            dialysisAdviceRespVOPageResult.setList(collect);
        }
        return dialysisAdviceRespVOPageResult;
    }

    @Override
    public List<DialysisAdviceRespVO> getDialysisAdviceList(DialysisAdviceCreateReqVO createReqVO) {
        if("1".equals(createReqVO.getType())){
            createReqVO.setDateWeek(null);
        }
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(createReqVO);
        List<DialysisAdviceRespVO> dialysisAdviceRespVOS = DialysisAdviceConvert.INSTANCE.convertList(dialysisAdviceDOS);

        if (CollectionUtil.isNotEmpty(dialysisAdviceRespVOS)) {
            List<UserRespDTO> userRespDTOS = adminUserApi.adminUserList(new UserRespDTO());
            Map<Long, UserRespDTO> userRespDTOMap = CollectionUtils.convertMap(userRespDTOS, UserRespDTO::getId, userRespDTO -> userRespDTO);
            List<HisComboDO> hisComboDOS = hisComboMapper.selectList();
            List<Long> collect = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(hisComboDOS)) {
                collect = hisComboDOS.stream().map(HisComboDO::getPackageId).collect(Collectors.toList());
            }

            List<Long> finalCollect = collect;
            String showNameFlag = redisTemplate.opsForValue().get("showName");
            dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().peek(dialysisAdviceRespVO ->{
                buildAdminUser(userRespDTOMap, dialysisAdviceRespVO);
                if (StringUtils.isEmpty(dialysisAdviceRespVO.getDrugType())) {
                    dialysisAdviceRespVO.setDrugType(1);
                }
                if (CollectionUtil.isNotEmpty(hisComboDOS) && showNameFlag.contains(String.valueOf(dialysisAdviceRespVO.getDeptId()))) {
                    if (finalCollect.contains(dialysisAdviceRespVO.getAdviceId())) {
                        List<PackageDetailDO> packageDetailDOS = packageDetailMapper.selectList(PackageDetailDO::getPackageId, String.valueOf(dialysisAdviceRespVO.getAdviceId()));
                        if (CollectionUtil.isNotEmpty(packageDetailDOS)) {
                            String collect1 = packageDetailDOS.stream().map(PackageDetailDO::getItemName).collect(Collectors.joining(" , "));
                            dialysisAdviceRespVO.setAdviceName(dialysisAdviceRespVO.getAdviceName() + " ( " + collect1 + " ) ");
                            dialysisAdviceRespVO.setShowName(1);
                        }
                    }
                }
            } ).collect(Collectors.toList());
            if ("1".equals(createReqVO.getType())) {
                dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().sorted(Comparator.comparing(DialysisAdviceRespVO::getStopStatus).thenComparing(Comparator.comparing(DialysisAdviceRespVO::getActivateTime,Comparator.nullsFirst(Date::compareTo)).thenComparing(DialysisAdviceRespVO::getStartTime,Comparator.nullsFirst(Date::compareTo)).thenComparing(DialysisAdviceRespVO::getDrugType))).collect(Collectors.toList());
            }else {
                dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().sorted(Comparator.comparing(DialysisAdviceRespVO::getActivateTime,Comparator.nullsFirst(Date::compareTo)).thenComparing(DialysisAdviceRespVO::getStartTime,Comparator.nullsFirst(Date::compareTo))).sorted(Comparator.comparing(DialysisAdviceRespVO::getDrugType)).collect(Collectors.toList());
            }

        }
        return dialysisAdviceRespVOS;
    }

    private static void buildAdminUser(Map<Long, UserRespDTO> userRespDTOMap, DialysisAdviceRespVO dialysisAdviceRespVO) {
        //开嘱医生
        if (dialysisAdviceRespVO.getAdviceUser() != null) {
            Optional.ofNullable(userRespDTOMap.get(dialysisAdviceRespVO.getAdviceUser())).ifPresent(adminUserInfo -> dialysisAdviceRespVO.setAdviceUserName(adminUserInfo.getNickname()));
        }

        //停止医生
        if (dialysisAdviceRespVO.getStopUserId() != null) {
            Optional.ofNullable(userRespDTOMap.get(dialysisAdviceRespVO.getStopUserId())).ifPresent(adminUserInfo -> dialysisAdviceRespVO.setStopUserName(adminUserInfo.getNickname()));
        }
        //执行人员
        if (dialysisAdviceRespVO.getActivateUser() != null) {
            Optional.ofNullable(userRespDTOMap.get(dialysisAdviceRespVO.getActivateUser())).ifPresent(adminUserInfo -> dialysisAdviceRespVO.setActivateUserName(adminUserInfo.getNickname()));
        }
        //核对人员
        if (dialysisAdviceRespVO.getCheckUser() != null) {
            Optional.ofNullable(userRespDTOMap.get(dialysisAdviceRespVO.getCheckUser())).ifPresent(adminUserInfo -> dialysisAdviceRespVO.setCheckUserName(adminUserInfo.getNickname()));
        }
    }

    @Override
    public DialysisAdviceRespVO getCurrentInfo() {
        long userId = StpUtil.getLoginIdAsLong();
        UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
        createReqDTO.setId(userId);
        UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
        DialysisAdviceRespVO dialysisAdviceRespVO = new DialysisAdviceRespVO();
        dialysisAdviceRespVO.setAdviceTime(DateUtil.date());
        dialysisAdviceRespVO.setStartTime(DateUtil.date());
        dialysisAdviceRespVO.setWarnTime(DateUtil.date());
        dialysisAdviceRespVO.setAdviceUserName(adminUser == null ? null : adminUser.getNickname());
        dialysisAdviceRespVO.setAdviceUser(userId);
        return dialysisAdviceRespVO;
    }

    @Override
    public Map<String, Object> getRoleAdvice() {
        Map<String, Object> rootMap = Maps.newHashMap();
        List<UserRespDTO> userRespDTOS = adminUserApi.selectUserJoinRole();
        if (CollectionUtil.isNotEmpty(userRespDTOS)) {
            Map<Long, List<UserRespDTO>> userRoleMap = userRespDTOS.stream().collect(Collectors.groupingBy(userRespDTO -> userRespDTO.getRoleId()));
            rootMap.put("currentUserId", StpUtil.getLoginIdAsLong());
            rootMap.put("userList", userRoleMap);
        }
        return rootMap;
    }

    @Override
    public DialysisAdviceRespVO getDialysisAdviceCarry(Long id) {
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
        DialysisAdviceRespVO dialysisAdviceRespVO = DialysisAdviceConvert.INSTANCE.convert(dialysisAdviceDO);
        if (dialysisAdviceRespVO != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(dialysisAdviceRespVO.getAdviceName())
                    .append(dialysisAdviceRespVO.getAdviceDesprition())
                    .append(dialysisAdviceRespVO.getOneNo())
                    .append(dialysisAdviceRespVO.getPrescribeNo())
                    .append(dialysisAdviceRespVO.getDrugWay())
                    .append(dialysisAdviceRespVO.getFrequency());
            dialysisAdviceRespVO.setAdviceName(sb.toString());
            //子级
            List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPid, id);
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                List<String> adviceNameList = dialysisAdviceDOS.stream().map(dialysisAdviceDO1 -> {
                    StringBuilder sb1 = new StringBuilder();
                    sb1.append(dialysisAdviceDO1.getAdviceName())
                            .append(dialysisAdviceDO1.getAdviceDesprition())
                            .append(dialysisAdviceDO1.getOneNo())
                            .append(dialysisAdviceDO1.getPrescribeNo())
                            .append(dialysisAdviceRespVO.getDrugWay())
                            .append(dialysisAdviceRespVO.getFrequency());
                    return sb1.toString();
                }).collect(Collectors.toList());
                dialysisAdviceRespVO.setAdviceNameList(adviceNameList);
            }
        }

        long userId = StpUtil.getLoginIdAsLong();
        if (dialysisAdviceRespVO.getActivateUser() == null) {
            UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
            createReqDTO.setId(userId);
            UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
            dialysisAdviceRespVO.setActivateUser(userId);
            dialysisAdviceRespVO.setActivateUserName(adminUser == null ? null : adminUser.getNickname());
        } else {
            UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
            createReqDTO.setId(dialysisAdviceRespVO.getAdviceUser());
            UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
            dialysisAdviceRespVO.setActivateUserName(adminUser == null ? null : adminUser.getNickname());
        }

        if (dialysisAdviceRespVO.getCheckUser() == null) {
            UserCreateReqDTO createReqDTO = new UserCreateReqDTO();
            createReqDTO.setId(userId);
            UserRespDTO adminUser = adminUserApi.getAdminUser(createReqDTO);
            dialysisAdviceRespVO.setCheckUser(userId);
            dialysisAdviceRespVO.setCheckUserName(adminUser == null ? null : adminUser.getNickname());
        } else {
            UserCreateReqDTO userCreateReqDTO = new UserCreateReqDTO();
            userCreateReqDTO.setId(dialysisAdviceRespVO.getCheckUser());
            UserRespDTO userRespDTO = adminUserApi.getAdminUser(userCreateReqDTO);
            dialysisAdviceRespVO.setCheckUserName(userRespDTO == null ? null : userRespDTO.getNickname());
        }
        return dialysisAdviceRespVO;
    }

    @Override
    @Transactional
    public void batchSaveAdviceTemp(AdviceTempsCreateVO adviceTempsVO, HttpServletRequest request) {
        String SystemDeptId = request.getHeader("SystemDeptId");
        String key = "patient:batchSaveAdviceTemp:key:" + SystemDeptId;
        RLock lock = redissonClient.getLock(key);
        boolean isLock = false;
        try {
            if ((isLock = lock.tryLock(20L, TimeUnit.SECONDS))) {
                if (adviceTempsVO != null) {
                    PatientDO patientDO = patientMapper.selectById(adviceTempsVO.getPatientId());
                    ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(new LambdaQueryWrapperX<ArrangeClassesDO>()
                            .eqIfPresent(ArrangeClassesDO::getPatientId, adviceTempsVO.getPatientId())
                            .eqIfPresent(ArrangeClassesDO::getClassesTime, DateUtil.beginOfDay(new Date()))
                            .eqIfPresent(ArrangeClassesDO::getTempType, 0));
                    long userId = StpUtil.getLoginIdAsLong();
                    List<AdviceTempsCreateVO.AdviceTempsVO> adviceTempsVOList = adviceTempsVO.getAdviceTempsVO();
                    PatientDO patientDO2 = patientMapper.selectById(adviceTempsVO.getPatientId());

                    for (AdviceTempsCreateVO.AdviceTempsVO tempsVO : adviceTempsVOList) {
                        List<DialysisAdviceDO> tempDialysisList = new ArrayList<>();
                        List<Long> children = tempsVO.getChildren();
                        //根据模版id获取子模版
                        for (Long id : children) {
                            //父级
                            MottoAdviceDO mottoAdviceDO = mottoAdviceMapper.selectById(id);
                            HisDrugDO drugDO = hisDrugMapper.selectById(mottoAdviceDO.getDrugId());
                            if (StringUtils.isEmpty(drugDO)) {
                               HisComboDO hisComboDO  = hisComboMapper.selectOne(HisComboDO::getPackageId,mottoAdviceDO.getDrugId());
                               if (!StringUtils.isEmpty(hisComboDO)) {
                                   drugDO = new HisDrugDO();
                                   drugDO.setFDrugName(hisComboDO.getPackageName());
                                   drugDO.setFEnabledMark(String.valueOf(hisComboDO.getEnabledMark()));
                               }
                            }
                            if (StringUtils.isEmpty(drugDO)) {
                                HisInformationDO hisInformationDO = hisInformationMapper.selectOne(HisInformationDO::getItemId, mottoAdviceDO.getDrugId());
                                if (!StringUtils.isEmpty(hisInformationDO)) {
                                    drugDO = new HisDrugDO();
                                    drugDO.setFDrugName(hisInformationDO.getItemName());
                                    drugDO.setFEnabledMark(hisInformationDO.getEnabledMark());
                                    if (!StringUtils.isEmpty(hisInformationDO.getItemName()) && (hisInformationDO.getItemName().equals("静脉注射") ||hisInformationDO.getItemName().equals("肌肉注射") ||hisInformationDO.getItemName().equals("皮下注射"))) {
                                        drugDO.setGenres(2);
                                    }
                                }
                            }
                            if (StringUtils.isEmpty(drugDO)) {
                                // 查询自建药
                                DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, mottoAdviceDO.getDrugId());
                                if (!StringUtils.isEmpty(drugDO1)) {
                                    drugDO = new HisDrugDO();
                                    drugDO.setFDrugName(drugDO1.getName());
                                    drugDO.setFDrugSpec(drugDO1.getSpec());
                                }
                            }
                            if (StringUtils.isEmpty(drugDO)) {
                                // 查询自建项目
                                ProjectDO projectDO = projectMapper.selectOne(ProjectDO::getId, mottoAdviceDO.getDrugId());
                                if (!StringUtils.isEmpty(projectDO)) {
                                    drugDO = new HisDrugDO();
                                    drugDO.setFDrugName(projectDO.getName());
                                }
                            }
                            if (StringUtils.isEmpty(drugDO)) {
                                // 查询耗材
                                HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, mottoAdviceDO.getDrugId());
                                if (!StringUtils.isEmpty(hisConsumablesDO)) {
                                    drugDO = new HisDrugDO();
                                    drugDO.setFDrugName(hisConsumablesDO.getConsumName());
                                    drugDO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                                    drugDO.setFEnabledMark(hisConsumablesDO.getEnabledMark());
                                    drugDO.setGenres(2);
                                }
                            }
                            Date startTime = null;
                            if (!StringUtils.isEmpty(adviceTempsVO.getStartTime())){
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                                try {
                                    startTime = sdf.parse(adviceTempsVO.getStartTime());
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            }
                            DialysisAdviceDO dialysisAdviceDO = new DialysisAdviceDO();
                            dialysisAdviceDO.setType(mottoAdviceDO.getAdviceType());
                            dialysisAdviceDO.setAdviceTime(adviceTempsVO.getDateWeek() == null ?DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                            dialysisAdviceDO.setStartTime(StringUtils.isEmpty(startTime) ?DateUtil.date() : startTime);
                            dialysisAdviceDO.setWarnTime(adviceTempsVO.getDateWeek() == null ?DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                            dialysisAdviceDO.setAdviceUser(userId);
                            dialysisAdviceDO.setAdviceName(drugDO == null ? null : drugDO.getFDrugName());
                            dialysisAdviceDO.setChildName(drugDO == null ? null: drugDO.getFEnabledMark());
                            dialysisAdviceDO.setAdviceDesprition(mottoAdviceDO.getDescribes());
                            dialysisAdviceDO.setOneNo(mottoAdviceDO.getDose());
                            dialysisAdviceDO.setFpreparaUnit(mottoAdviceDO.getDoseUnit());
                            dialysisAdviceDO.setPrescribeNo(mottoAdviceDO.getNumber() != null ? mottoAdviceDO.getNumber() + "" : "1");
                            dialysisAdviceDO.setFspecUnit(mottoAdviceDO.getNumberUnit());
                            dialysisAdviceDO.setDrugWay(mottoAdviceDO.getRoute());
                            dialysisAdviceDO.setFrequency(mottoAdviceDO.getRate());
                            dialysisAdviceDO.setRemark(mottoAdviceDO.getRemark());
                            dialysisAdviceDO.setAdviceId(mottoAdviceDO.getDrugId());
                            dialysisAdviceDO.setPatientName(patientDO2.getName());
                            dialysisAdviceDO.setSpellName(patientDO2.getSpellName());
                            dialysisAdviceDO.setSpecification(drugDO.getFDrugSpec());
                            dialysisAdviceDO.setDrugType(drugDO.getGenres());
                            dialysisAdviceDO.setPatientNickName(adviceTempsVO.getPatientNickName());
                            dialysisAdviceDO.setDialyzeNo(adviceTempsVO.getDialyzeNo());
                            dialysisAdviceDO.setPatientSource(patientDO == null ? null : patientDO.getPatientSource());
                            dialysisAdviceDO.setDateWeek(adviceTempsVO.getDateWeek() == null ? DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                            dialysisAdviceDO.setPatientId(adviceTempsVO.getPatientId());
                            if (arrangeClassesDO != null) {
                                dialysisAdviceDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                                dialysisAdviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
                                dialysisAdviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());
                                dialysisAdviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                                dialysisAdviceDO.setWeekDay(arrangeClassesDO.getDayState());
                            }
                            dialysisAdviceDO.setSpellName(patientDO == null ? null : patientDO.getSpellName());
                            dialysisAdviceMapper.insert(dialysisAdviceDO);
                            tempDialysisList.add(dialysisAdviceDO);
                            //子级
                            List<MottoAdviceDO> mottoAdviceDOS = mottoAdviceMapper.selectList(MottoAdviceDO::getPid, id);
                            if (CollectionUtil.isNotEmpty(mottoAdviceDOS)) {
                                List<DialysisAdviceDO> adviceDOList = mottoAdviceDOS.stream().map(mottoadvicedo -> {
                                    //药品名称
                                    HisDrugDO hisDrugDO = hisDrugMapper.selectById(mottoadvicedo.getDrugId());
                                    if (StringUtils.isEmpty(hisDrugDO)) {
                                        HisComboDO hisComboDO  = hisComboMapper.selectOne(HisComboDO::getPackageId,mottoadvicedo.getDrugId());
                                        if (!StringUtils.isEmpty(hisComboDO)) {
                                            hisDrugDO = new HisDrugDO();
                                            hisDrugDO.setFDrugName(hisComboDO.getPackageName());
                                            hisDrugDO.setFEnabledMark(String.valueOf(hisComboDO.getEnabledMark()));
                                        }
                                    }
                                    if (StringUtils.isEmpty(hisDrugDO)) {
                                        HisInformationDO hisInformationDO = hisInformationMapper.selectOne(HisInformationDO::getItemId, mottoadvicedo.getDrugId());
                                        if (!StringUtils.isEmpty(hisInformationDO)) {
                                            hisDrugDO = new HisDrugDO();
                                            hisDrugDO.setFDrugName(hisInformationDO.getItemName());
                                            hisDrugDO.setFEnabledMark(hisInformationDO.getEnabledMark());
                                            if (!StringUtils.isEmpty(hisInformationDO.getItemName()) && (hisInformationDO.getItemName().equals("静脉注射") ||hisInformationDO.getItemName().equals("肌肉注射") ||hisInformationDO.getItemName().equals("皮下注射"))) {
                                                hisDrugDO.setGenres(2);
                                            }
                                        }
                                    }
                                    if (StringUtils.isEmpty(hisDrugDO)) {
                                        // 查询自建药
                                        DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, mottoadvicedo.getDrugId());
                                        if (!StringUtils.isEmpty(drugDO1)) {
                                            hisDrugDO = new HisDrugDO();
                                            hisDrugDO.setFDrugName(drugDO1.getName());
                                            hisDrugDO.setFDrugSpec(drugDO1.getSpec());
                                        }
                                    }
                                    if (StringUtils.isEmpty(hisDrugDO)) {
                                        // 查询耗材
                                        HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, mottoadvicedo.getDrugId());
                                        if (!StringUtils.isEmpty(hisConsumablesDO)) {
                                            hisDrugDO = new HisDrugDO();
                                            hisDrugDO.setFDrugName(hisConsumablesDO.getConsumName());
                                            hisDrugDO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                                            hisDrugDO.setFEnabledMark(hisConsumablesDO.getEnabledMark());
                                            hisDrugDO.setGenres(2);
                                        }
                                    }
                                    if (StringUtils.isEmpty(hisDrugDO)) {
                                        // 查询自建项目
                                        ProjectDO projectDO = projectMapper.selectOne(ProjectDO::getId, mottoadvicedo.getDrugId());
                                        if (!StringUtils.isEmpty(projectDO)) {
                                            hisDrugDO = new HisDrugDO();
                                            hisDrugDO.setFDrugName(projectDO.getName());
                                        }
                                    }
                                    DialysisAdviceDO adviceDO = new DialysisAdviceDO();
                                    adviceDO.setType(mottoadvicedo.getAdviceType());
                                    Date startTime1 = null;
                                    if (!StringUtils.isEmpty(adviceTempsVO.getStartTime())){
                                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                                        try {
                                            startTime1 = sdf.parse(adviceTempsVO.getStartTime());
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    adviceDO.setAdviceTime(adviceTempsVO.getDateWeek() == null ?DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                                    adviceDO.setStartTime(StringUtils.isEmpty(startTime1)?DateUtil.date() : startTime1);
                                    adviceDO.setWarnTime(adviceTempsVO.getDateWeek() == null ?DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                                    adviceDO.setAdviceUser(userId);
                                    adviceDO.setAdviceName(hisDrugDO == null ? null : hisDrugDO.getFDrugName());
                                    adviceDO.setChildName(hisDrugDO == null ? null : hisDrugDO.getFEnabledMark());
                                    adviceDO.setDrugType(hisDrugDO == null ? null:hisDrugDO.getGenres());
                                    adviceDO.setAdviceDesprition(mottoadvicedo.getDescribes());
                                    adviceDO.setOneNo(mottoadvicedo.getDose());
                                    adviceDO.setFpreparaUnit(mottoadvicedo.getDoseUnit());
                                    adviceDO.setPrescribeNo(mottoadvicedo.getNumber() != null ? mottoadvicedo.getNumber() + "" : null);
                                    adviceDO.setFspecUnit(mottoadvicedo.getNumberUnit());
                                    adviceDO.setDrugWay(mottoadvicedo.getRoute());
                                    adviceDO.setFrequency(mottoadvicedo.getRate());
                                    adviceDO.setRemark(mottoadvicedo.getRemark());
                                    adviceDO.setAdviceId(mottoadvicedo.getDrugId());
                                    adviceDO.setPatientName(patientDO2.getName());
                                    adviceDO.setPatientNickName(adviceTempsVO.getPatientNickName());
                                    adviceDO.setDialyzeNo(adviceTempsVO.getDialyzeNo());
                                    adviceDO.setPatientSource(adviceTempsVO.getPatientSource());
                                    adviceDO.setPid(dialysisAdviceDO.getId());
                                    adviceDO.setDateWeek(adviceTempsVO.getDateWeek() == null ? DateUtil.beginOfDay(new Date()) : adviceTempsVO.getDateWeek());
                                    adviceDO.setPatientId(adviceTempsVO.getPatientId());
                                    adviceDO.setSpellName(patientDO2.getSpellName());
                                    adviceDO.setSpecification(hisDrugDO.getFDrugSpec());
                                    if (arrangeClassesDO != null) {
                                        adviceDO.setFacilitySubareaId(arrangeClassesDO.getFacilitySubareaId());
                                        adviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
                                        adviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());
                                        adviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                                        adviceDO.setWeekDay(arrangeClassesDO.getDayState());
                                    }
                                    return adviceDO;
                                }).collect(Collectors.toList());
                                dialysisAdviceMapper.insertBatch(adviceDOList);
                                tempDialysisList.addAll(adviceDOList);
                            }
                        }

                        String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
                        if (!StringUtils.isEmpty(synFlag) && !org.springframework.util.CollectionUtils.isEmpty(tempDialysisList)) {
                            AdminUserRespDTO user = adminUserApi.getUser(tempDialysisList.get(0).getAdviceUser());
                            tempDialysisList.forEach(dialysisAdviceDO1 -> {
                                ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                                Long adviceId = dialysisAdviceDO1.getAdviceId();
                                if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO1.getType()) && "1".equals(dialysisAdviceDO1.getChildName())) {
                                    try {
                                        Thread.sleep(100);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                    SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();

                                    PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO1.getPatientId());
                                    if (!StringUtils.isEmpty(patientDO1)) {
                                        synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                                    }
                                    //Date dateWeek = createReqVO.getDateWeek();
                                    //Date startTime = createReqVO.getStartTime();
                                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getStartTime())) {

                                        String format = simpleDateFormat.format(new Date());

                                        synToHisAdviceVo.setDpDate(format);
                                    }else {
                                        synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO1.getStartTime()));
                                    }
                                    synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO1.getAdviceId()));
                                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                                    synToHisAdviceVo.setDrugUsage(dialysisAdviceDO1.getDrugWay());
                                    synToHisAdviceVo.setOnceUsing(dialysisAdviceDO1.getOneNo());
                                    synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO1.getFpreparaUnit());
                                    synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO1.getId()));
                                    synToHisAdviceVo.setDoctorMobile(user.getMobile());
                                    synToHisAdviceVo.setDrugName(dialysisAdviceDO1.getAdviceName());
                                    synToHisAdviceVo.setDrugDays(1);
                                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                                        dialysisAdviceDO1.setPrescribeNo("1");
                                    }
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getOneNo())) {
                                        dialysisAdviceDO1.setOneNo("1");
                                    }
                                    synToHisAdviceVo.setDrugNum(dialysisAdviceDO1.getPrescribeNo());
                                    DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                                    synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                                    synToHisAdviceVos.add(synToHisAdviceVo);
                                    AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                                    if (!StringUtils.isEmpty(adviceResult)) {
                                        AdviceData data = adviceResult.getData();
                                        if (-1 == data.getInfcode()) {
                                            throw new ServiceException(data.getInfcode(), data.getMessage());
                                        }else if (0 == data.getInfcode()) {
                                            // 更新advice
                                            List<AdviceOutput> output = data.getOutput();
                                            if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                                AdviceOutput adviceOutput = output.get(0);
                                                dialysisAdviceDO1.setDpId(adviceOutput.getDpId());
                                                dialysisAdviceDO1.setDpdId(adviceOutput.getDpdId());
                                                dialysisAdviceMapper.updateById(dialysisAdviceDO1);
                                            }
                                        }
                                    }else {
                                        throw new ServiceException(-1, "同步失败");
                                    }
                                }

                            });
                        }
                    }
                }
            } else {
                log.info("没有拿到，离开了.......");
                return;
            }
        }catch (ServiceException e) {
            throw new ServiceException(e.getCode(), e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            log.error("批量新增医嘱模版异常信息：{}", e.getMessage());
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }
    }

    @Override
    public void activeAdvice(DialysisAdviceUpdateReqVO updateReqVO) {
        DialysisAdviceDO dialysisAdviceDO = new DialysisAdviceDO();
        dialysisAdviceDO.setActivateTime(updateReqVO.getActivateTime());
        dialysisAdviceDO.setActivateUser(updateReqVO.getActivateUser());
        dialysisAdviceDO.setCheckUser(updateReqVO.getCheckUser());
        dialysisAdviceDO.setState("1");
        dialysisAdviceDO.setStopStatus("1");
        dialysisAdviceDO.setRemark(updateReqVO.getRemark());
        dialysisAdviceMapper.update(dialysisAdviceDO, new LambdaUpdateWrapper<DialysisAdviceDO>()
                    .eq(DialysisAdviceDO::getPid, updateReqVO.getId())
                    .or()
                    .eq(DialysisAdviceDO::getId, updateReqVO.getId()));

    }

    @Override
    public void stopAdvice(DialysisAdviceUpdateReqVO updateReqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        List<Long> idList = Arrays.stream(updateReqVO.getIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>()
                .in(DialysisAdviceDO::getId, idList)
                .eq(DialysisAdviceDO::getStopStatus, "1"));
        if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
            throw new ServiceException(GlobalErrorCodeConstants.STOP_ADVICE_STATUS, dialysisAdviceDOS.size() + "");
        }
        DialysisAdviceDO dialysisAdviceDO = DialysisAdviceConvert.INSTANCE.convert(updateReqVO);
        dialysisAdviceDO.setStopStatus("1");
        dialysisAdviceDO.setMedicateState(0);
        dialysisAdviceDO.setStopUserId(userId);
        dialysisAdviceDO.setStopUserName(user == null ? null : user.getNickname());
        dialysisAdviceMapper.update(dialysisAdviceDO, new LambdaUpdateWrapper<DialysisAdviceDO>()
                .in(DialysisAdviceDO::getId, idList)
                .or()
                .in(DialysisAdviceDO::getPid, idList)
                .or()
                .in(DialysisAdviceDO::getLongAdviceId,idList));
    }

    @Override
    public void wxSend(AdviceTempsCreateVO createVO) {
        List<Long> adviceIdList = Arrays.stream(createVO.getAdviceIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        if (adviceIdList.size() > 1) {
            createVO.setAdviceId(adviceIdList.get(0));
        } else {
            createVO.setAdviceId(Long.valueOf(createVO.getAdviceIds()));
        }
        PatientDO patientDO = patientMapper.selectById(createVO.getPatientId());
        DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(createVO.getAdviceId());
        if (patientDO != null && dialysisAdviceDO != null) {
            if ("0".equals(patientDO.getState())) {
                throw new ServiceException(GlobalErrorCodeConstants.NO_BIND_PATIENT);
            }
            SendMessageCreateReqVO createReqVO = new SendMessageCreateReqVO();
            createReqVO.setOpenId(patientDO.getOpenId());
            if (dialysisAdviceDO.getAdviceUser() != null) {
                AdminUserRespDTO user = adminUserApi.getUser(dialysisAdviceDO.getAdviceUser());
                createReqVO.setName(user == null ? null : user.getNickname());
            }
            createReqVO.setAdviceName(dialysisAdviceDO.getAdviceName());
            createReqVO.setState(2);
            String message = wxPublicNoHandle.sendMessage(createReqVO);
            if (StrUtil.isEmpty(message)) {
                throw new ServiceException(GlobalErrorCodeConstants.WX_SEND_INFO);
            }
            //新增微信互动医嘱模块
            WechatInteractionDO wechatInteractionDO = new WechatInteractionDO();
            wechatInteractionDO.setSendTime(DateUtil.date());
            wechatInteractionDO.setType("1");
            wechatInteractionDO.setContent(dialysisAdviceDO.getAdviceName());
            wechatInteractionDO.setPatientId(patientDO.getId());
            wechatInteractionDO.setPatientName(patientDO.getName());
            wechatInteractionDO.setPatientNickName(patientDO.getNickName());
            wechatInteractionDO.setDialyzeNo(patientDO.getDialyzeNo());
            wechatInteractionDO.setSpellName(patientDO.getSpellName());
            wechatInteractionMapper.insert(wechatInteractionDO);
        }
    }

    @Override
    public void batchDeleteDialysisAdvice(String ids,HttpServletRequest request) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        for (Long id : idList) {
            deleteDialysisAdvice(id,request);
            List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
            if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                for (DialysisAdviceDO dialysisAdviceDO : dialysisAdviceDOS) {
                    deleteDialysisAdvice(dialysisAdviceDO.getId(),request);
                }
            }
            dialysisAdviceMapper.delete(new LambdaQueryWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getLongAdviceId, id)
                    .eq(DialysisAdviceDO::getPushStatus,1));
        }

        //dialysisAdviceMapper.deleteBatchIds(idList);
/*        for (Long id : idList) {
            //dialysisAdviceMapper.delete(new LambdaQueryWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
            // 删除推送医嘱

        }*/
    }

    @Override
    public void batchSaveDialysisAdvice(DialysisAdviceCreateReqVO createReqVO) {
        List<Long> patientIds = Arrays.stream(createReqVO.getPatientIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<PatientDO> patientDOS = patientMapper.selectList(PatientDO::getId, patientIds);
        if (CollectionUtil.isNotEmpty(patientDOS)) {
            for (PatientDO patientDO : patientDOS) {
                for (DialysisAdviceCreateReqVO dialysisAdviceCreateReqVO : createReqVO.getDialysisAdviceCreateReqVOS()) {
                    DialysisAdviceDO dialysisAdviceDO = DialysisAdviceConvert.INSTANCE.convert(dialysisAdviceCreateReqVO);
                    dialysisAdviceDO.setPatientId(patientDO.getId());
                    dialysisAdviceDO.setPatientName(patientDO.getName());
                    dialysisAdviceDO.setPatientNickName(patientDO.getNickName());
                    dialysisAdviceDO.setPatientSource(patientDO.getPatientSource());
                    dialysisAdviceMapper.insert(dialysisAdviceDO);
                    if (CollectionUtil.isNotEmpty(dialysisAdviceCreateReqVO.getChildren())) {
                        List<DialysisAdviceCreateReqVO> dialysisAdviceCreateReqVOS = dialysisAdviceCreateReqVO.getChildren().stream().peek(dialysisadvice -> {
                            dialysisadvice.setPid(dialysisAdviceDO.getId());
                        }).collect(Collectors.toList());
                        List<DialysisAdviceDO> dialysisAdviceDOS = DialysisAdviceConvert.INSTANCE.convertList2(dialysisAdviceCreateReqVOS);
                        dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    }
                }
            }
        }
    }

    @Override
    public List<DialysisAdviceRespVO> getHistoryAdvice(DialysisAdviceCreateReqVO createReqVO) {
        DateTime month = DateUtil.offsetMonth(new Date(), -1);
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                .betweenIfPresent(DialysisAdviceDO::getDateWeek, DateUtil.beginOfDay(month), DateUtil.endOfDay(new Date()))
                .eqIfPresent(DialysisAdviceDO::getPatientId, createReqVO.getPatientId()));
        List<DialysisAdviceRespVO> dialysisAdviceRespVOS = DialysisAdviceConvert.INSTANCE.convertList(dialysisAdviceDOS);
        if (CollectionUtil.isNotEmpty(dialysisAdviceRespVOS)) {
            dialysisAdviceRespVOS = dialysisAdviceRespVOS.stream().peek(dialysisAdviceRespVO -> {
                //开嘱医生
                if (dialysisAdviceRespVO.getAdviceUser() != null) {
                    UserRespDTO adminUserInfo = adminUserApi.getAdminUserInfo(dialysisAdviceRespVO.getAdviceUser());
                    dialysisAdviceRespVO.setAdviceUserName(adminUserInfo == null ? null : adminUserInfo.getNickname());
                }

                //停止医生
                if (dialysisAdviceRespVO.getStopUserId() != null) {
                    UserRespDTO userRespDTO = adminUserApi.getAdminUserInfo(dialysisAdviceRespVO.getStopUserId());
                    dialysisAdviceRespVO.setStopUserName(userRespDTO == null ? null : userRespDTO.getNickname());
                }
                //执行人员
                if (dialysisAdviceRespVO.getActivateUser() != null) {
                    UserRespDTO activateUser = adminUserApi.getAdminUserInfo(dialysisAdviceRespVO.getActivateUser());
                    dialysisAdviceRespVO.setActivateUserName(activateUser == null ? null : activateUser.getNickname());
                }
                //核对人员
                if (dialysisAdviceRespVO.getCheckUser() != null) {
                    UserRespDTO checkUser = adminUserApi.getAdminUserInfo(dialysisAdviceRespVO.getCheckUser());
                    dialysisAdviceRespVO.setCheckUserName(checkUser == null ? null : checkUser.getNickname());
                }
            }).collect(Collectors.toList());
        }

        return dialysisAdviceRespVOS;
    }

    @Override
    public void medicatePush(DialysisAdviceCreateReqVO createReqVO) {
        if (StrUtil.isNotEmpty(createReqVO.getBrevityTime())) {
            String format = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
            DateTime parse = DateUtil.parse(format + " " + createReqVO.getBrevityTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
            createReqVO.setStartTime(parse);
        }
        DialysisAdviceDO dialysisAdviceDO = DialysisAdviceConvert.INSTANCE.convert(createReqVO);
        List<Long> adviceIdList = Arrays.stream(createReqVO.getAdviceIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(adviceIdList) && !StringUtils.isEmpty(createReqVO.getDateList())) {
            adviceIdList.forEach(adviceId -> {
                DialysisAdviceDO dialysisAdviceDO1 = dialysisAdviceMapper.selectById(adviceId);
                // 查询子药
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaUpdateWrapper<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, adviceId)
                        .eq(DialysisAdviceDO::getDeleted, 0)
                        .eq(DialysisAdviceDO::getStopStatus, 0));

                // 该医嘱已推送，删除推送的医嘱
                if (dialysisAdviceDO1.getMedicateState() == 1) {
                    dialysisAdviceMapper.delete(new LambdaUpdateWrapper<DialysisAdviceDO>()
                            //.eq(DialysisAdviceDO::getAdviceId,dialysisAdviceDO1.getAdviceId())
                            .eq(DialysisAdviceDO::getPushStatus,1)
                            .eq(DialysisAdviceDO::getPatientId,dialysisAdviceDO1.getPatientId())
                            .eq(DialysisAdviceDO::getLongAdviceId,adviceId));
                    if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                        dialysisAdviceDOS.forEach(dialysisAdviceDO2 -> {
                            dialysisAdviceMapper.delete(new LambdaUpdateWrapper<DialysisAdviceDO>()
                                    //.eq(DialysisAdviceDO::getAdviceId,dialysisAdviceDO2.getAdviceId())
                                    .eq(DialysisAdviceDO::getPushStatus,1)
                                    .eq(DialysisAdviceDO::getPatientId,dialysisAdviceDO2.getPatientId())
                                    .eq(DialysisAdviceDO::getLongAdviceId,adviceId));
                        });
                    }
                }
                Date[] dateList = createReqVO.getDateList();
                for (Date date : dateList) {
                    DialysisAdviceDO dialysisAdviceDO2 = new DialysisAdviceDO();
                    BeanUtils.copyProperties(dialysisAdviceDO1,dialysisAdviceDO2);
                    dialysisAdviceDO2.setId(null);
                    dialysisAdviceDO2.setWarnTime(date);
                    dialysisAdviceDO2.setAdviceTime(date);
                    dialysisAdviceDO2.setType("0");
                    dialysisAdviceDO2.setLongAdviceId(adviceId);
                    dialysisAdviceDO2.setPushStatus(1);
                    dialysisAdviceDO2.setFrequency(createReqVO.getFrequency());
                    dialysisAdviceDO2.setStartTime(null);
                    dialysisAdviceDO2.setDateWeek(null);
                    dialysisAdviceDO2.setMedicateState(1);
                    dialysisAdviceDO2.setCreateTime(new Date());
                    String format = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
                    if (StrUtil.isNotEmpty(createReqVO.getBrevityTime())) {
                        DateTime parse = DateUtil.parse(format + " " + createReqVO.getBrevityTime(), DatePattern.NORM_DATETIME_PATTERN);

                        dialysisAdviceDO2.setStartTime(parse);
                    }
                    dialysisAdviceMapper.insert(dialysisAdviceDO2);
                    if (!org.springframework.util.CollectionUtils.isEmpty(dialysisAdviceDOS)) {
                        dialysisAdviceDOS.forEach(dialysisAdviceDO3 -> {
                            DialysisAdviceDO dialysisAdviceDO4 = new DialysisAdviceDO();
                            BeanUtils.copyProperties(dialysisAdviceDO3,dialysisAdviceDO4);
                            dialysisAdviceDO4.setId(null);
                            dialysisAdviceDO4.setWeekDay(dialysisAdviceDO2.getWeekDay());
                            dialysisAdviceDO4.setFacilitySubareaId(dialysisAdviceDO2.getFacilitySubareaId());
                            dialysisAdviceDO4.setFacilityId(dialysisAdviceDO2.getFacilityId());
                            dialysisAdviceDO4.setDialyzeName(dialysisAdviceDO2.getDialyzeName());
                            dialysisAdviceDO4.setDialyzeDictValue(dialysisAdviceDO2.getDialyzeDictValue());
                            dialysisAdviceDO4.setWarnTime(date);
                            dialysisAdviceDO4.setAdviceTime(date);
                            dialysisAdviceDO4.setFrequency(createReqVO.getFrequency());
                            dialysisAdviceDO4.setType("0");
                            dialysisAdviceDO4.setLongAdviceId(dialysisAdviceDO3.getId());
                            dialysisAdviceDO4.setPushStatus(1);
                            dialysisAdviceDO4.setStartTime(null);
                            dialysisAdviceDO4.setDateWeek(null);
                            dialysisAdviceDO4.setMedicateState(1);
                            dialysisAdviceDO4.setCreateTime(new Date());
                            dialysisAdviceDO4.setPid(dialysisAdviceDO2.getId());
                            if (StrUtil.isNotEmpty(createReqVO.getBrevityTime())) {
                                DateTime parse = DateUtil.parse(format + " " + createReqVO.getBrevityTime(), DatePattern.NORM_DATETIME_PATTERN);

                                dialysisAdviceDO4.setStartTime(parse);
                            }
                            dialysisAdviceMapper.insert(dialysisAdviceDO4);
                        });
                    }
                }

            });
        }

        //查看该患者是否在模版中有排班|有推送，没有未推送
        DateTime dateTime = DateUtil.offsetWeek(new Date(), 3);
        List<ArrangeClassesDO> arrangeClassesDOS = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eqIfPresent(ArrangeClassesDO::getPatientId, createReqVO.getPatientId())
                .notIn(ArrangeClassesDO::getTempType, 0));
        if (CollectionUtil.isNotEmpty(arrangeClassesDOS)) {
            dialysisAdviceDO.setMedicateState(1);

            dialysisAdviceMapper.update(null,new LambdaUpdateWrapper<DialysisAdviceDO>()
                    .in(DialysisAdviceDO::getId, adviceIdList)
                    .or()
                    .in(DialysisAdviceDO::getPid, adviceIdList)
                    .set(DialysisAdviceDO::getMedicateState,1));

        } else {
            dialysisAdviceDO.setMedicateState(0);
            dialysisAdviceMapper.update(dialysisAdviceDO, new LambdaUpdateWrapper<DialysisAdviceDO>()
                    .in(DialysisAdviceDO::getId, adviceIdList)
                    .or()
                    .in(DialysisAdviceDO::getPid, adviceIdList));
        }
    }

    @Override
    public void medicatePushBatch(DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        List<String> idList = Arrays.stream(createReqVO.getAdviceIds().split(",")).collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(idList) && !StringUtils.isEmpty(createReqVO.getAdviceIds())) {
            idList = idList.stream().distinct().collect(Collectors.toList());
        }else {
            return;
        }

        ArrangeClassesDO arrangeClassesDO = arrangeClassesMapper.selectOne(ArrangeClassesDO::getPatientId, createReqVO.getPatientId(), ArrangeClassesDO::getClassesTime, createReqVO.getDateWeek(),ArrangeClassesDO::getTempType,0);
        String weekStates = null;
        if (!StringUtils.isEmpty(arrangeClassesDO)) {
            weekStates = arrangeClassesDO.getDayState();
        }
        long userId = StpUtil.getLoginIdAsLong();
        final String weekState = weekStates;

        for (String id : idList) {
            List<DialysisAdviceDO> tempDialysisList = new ArrayList<>();
            DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
            if ( !StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getPushStatus()) && 1 == dialysisAdviceDO.getPushStatus()) {
                if (StringUtils.isEmpty(dialysisAdviceDO.getStartTime())) {
                    dialysisAdviceDO.setStartTime(new Date());
                }
                if (!StringUtils.isEmpty(dialysisAdviceDO.getLongAdviceId())) {
                    DialysisAdviceDO dialysisAdviceDO1 = dialysisAdviceMapper.selectById(dialysisAdviceDO.getLongAdviceId());
                    if (StringUtils.isEmpty(dialysisAdviceDO1)) {
                        continue;
                    }else  {
                        dialysisAdviceDO.setPrescribeNo(dialysisAdviceDO1.getPrescribeNo());
                        dialysisAdviceDO.setOneNo(dialysisAdviceDO1.getOneNo());
                        dialysisAdviceDO.setAdviceId(dialysisAdviceDO1.getAdviceId());
                        dialysisAdviceDO.setAdviceName(dialysisAdviceDO1.getAdviceName());
                        dialysisAdviceDO.setDrugWay(dialysisAdviceDO1.getDrugWay());
                        dialysisAdviceDO.setAdviceDesprition(dialysisAdviceDO1.getAdviceDesprition());
                        dialysisAdviceDO.setSpecification(dialysisAdviceDO1.getSpecification());
                        dialysisAdviceDO.setFspecUnit(dialysisAdviceDO1.getFspecUnit());
                        dialysisAdviceDO.setFpreparaUnit(dialysisAdviceDO1.getFpreparaUnit());
                        //dialysisAdviceDO.setFrequency(dialysisAdviceDO1.getFrequency());
                    }
                }

                dialysisAdviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
                if (StringUtils.isEmpty(dialysisAdviceDO.getPrescribeNo())) {
                    dialysisAdviceDO.setPrescribeNo("1");
                }
                dialysisAdviceDO.setId(null);
                dialysisAdviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                dialysisAdviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());
                dialysisAdviceDO.setDateWeek(DateUtil.beginOfDay(new Date()));
                dialysisAdviceDO.setCreateTime(new Date());
                dialysisAdviceDO.setMedicateState(0);
                dialysisAdviceDO.setChildName("1");
                dialysisAdviceDO.setPushStatus(0);
                if (!StringUtils.isEmpty(userId)) {
                    dialysisAdviceDO.setAdviceUser(userId);
                }

                if (!StringUtils.isEmpty(weekState)) {
                    dialysisAdviceDO.setWeekDay(weekState);
                }
                //药品名称
                HisDrugDO hisDrugDO = hisDrugMapper.selectById(dialysisAdviceDO.getAdviceId());
                if (!StringUtils.isEmpty(hisDrugDO)) {
                    dialysisAdviceDO.setSpecification(hisDrugDO.getFDrugSpec());
                    dialysisAdviceDO.setChildName(hisDrugDO.getFEnabledMark());
                }
                if (StringUtils.isEmpty(hisDrugDO)) {
                    // 查询自建药
                    DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, dialysisAdviceDO.getAdviceId());
                    if (!StringUtils.isEmpty(drugDO1)) {
                        hisDrugDO = new HisDrugDO();
                        hisDrugDO.setFDrugName(drugDO1.getName());
                        dialysisAdviceDO.setSpecification(drugDO1.getSpec());
                        dialysisAdviceDO.setChildName(hisDrugDO.getFEnabledMark());
                        //dialysisAdviceDO.setDrugType(2);
                    }
                }
                if (StringUtils.isEmpty(hisDrugDO)) {
                    // 查询耗材
                    HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO.getAdviceId());
                    if (!StringUtils.isEmpty(hisConsumablesDO)) {
                        hisDrugDO = new HisDrugDO();
                        hisDrugDO.setFDrugName(hisConsumablesDO.getConsumName());
                        hisDrugDO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                        dialysisAdviceDO.setSpecification(hisConsumablesDO.getConsumSpec());
                        dialysisAdviceDO.setChildName(hisConsumablesDO.getEnabledMark());
                    }
                }

                dialysisAdviceMapper.insert(dialysisAdviceDO);
                tempDialysisList.add(dialysisAdviceDO);

                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaUpdateWrapper<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, id)
                        .eq(DialysisAdviceDO::getDeleted,0)
                        .eq(DialysisAdviceDO::getPushStatus,1)
                        .isNotNull(DialysisAdviceDO::getLongAdviceId)
                        .eq(DialysisAdviceDO::getStopStatus,0));
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                    dialysisAdviceDOS = dialysisAdviceDOS.stream().peek(dialysisAdviceDO1 -> {
                        if (!StringUtils.isEmpty(dialysisAdviceDO1.getLongAdviceId())) {
                            DialysisAdviceDO dialysisAdviceDO2 = dialysisAdviceMapper.selectById(dialysisAdviceDO1.getLongAdviceId());
                            if (!StringUtils.isEmpty(dialysisAdviceDO2)) {
                                dialysisAdviceDO1.setAdviceId(dialysisAdviceDO2.getAdviceId());
                                dialysisAdviceDO1.setDrugWay(dialysisAdviceDO2.getDrugWay());
                                dialysisAdviceDO1.setAdviceName(dialysisAdviceDO2.getAdviceName());
                                dialysisAdviceDO1.setOneNo(dialysisAdviceDO2.getOneNo());
                                dialysisAdviceDO1.setPrescribeNo(dialysisAdviceDO2.getPrescribeNo());
                                dialysisAdviceDO1.setAdviceDesprition(dialysisAdviceDO2.getAdviceDesprition());
                                dialysisAdviceDO1.setSpecification(dialysisAdviceDO2.getSpecification());
                                dialysisAdviceDO1.setFspecUnit(dialysisAdviceDO2.getFspecUnit());
                                dialysisAdviceDO1.setFpreparaUnit(dialysisAdviceDO2.getFpreparaUnit());
                                //dialysisAdviceDO1.setFrequency(dialysisAdviceDO2.getFrequency());
                            }
                        }

                        dialysisAdviceDO1.setDateWeek(DateUtil.beginOfDay(new Date()));
                        dialysisAdviceDO1.setMedicateState(0);
                        dialysisAdviceDO1.setId(null);
                        dialysisAdviceDO1.setCreateTime(new Date());
                        dialysisAdviceDO1.setPushStatus(0);
                        dialysisAdviceDO1.setChildName("1");
                        dialysisAdviceDO1.setFacilityId(arrangeClassesDO.getFacilityId());
                        if (!StringUtils.isEmpty(userId)) {
                            dialysisAdviceDO1.setAdviceUser(userId);
                        }
                        if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                            dialysisAdviceDO1.setPrescribeNo("1");
                        }


                        //药品名称
                        HisDrugDO hisDrugDO1 = hisDrugMapper.selectById(dialysisAdviceDO1.getAdviceId());
                        if (!StringUtils.isEmpty(hisDrugDO1)) {
                            dialysisAdviceDO1.setSpecification(hisDrugDO1.getFDrugSpec());
                            dialysisAdviceDO1.setChildName(hisDrugDO1.getFEnabledMark());
                        }
                        if (StringUtils.isEmpty(hisDrugDO1)) {
                            // 查询自建药
                            DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, dialysisAdviceDO1.getAdviceId());
                            if (!StringUtils.isEmpty(drugDO1)) {
                                hisDrugDO1 = new HisDrugDO();
                                hisDrugDO1.setFDrugName(drugDO1.getName());
                                dialysisAdviceDO1.setSpecification(drugDO1.getSpec());
                            }
                        }
                        if (StringUtils.isEmpty(hisDrugDO1)) {
                            // 查询耗材
                            HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO1.getAdviceId());
                            if (!StringUtils.isEmpty(hisConsumablesDO)) {
                                hisDrugDO1 = new HisDrugDO();
                                hisDrugDO1.setFDrugName(hisConsumablesDO.getConsumName());
                                hisDrugDO1.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                                dialysisAdviceDO1.setSpecification(hisConsumablesDO.getConsumSpec());
                                dialysisAdviceDO1.setChildName(hisConsumablesDO.getEnabledMark());
                                dialysisAdviceDO1.setDrugType(2);
                            }
                        }


                        dialysisAdviceDO1.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                        dialysisAdviceDO1.setDialyzeName(arrangeClassesDO.getDialysisName());

                        if (!StringUtils.isEmpty(weekState)) {
                            dialysisAdviceDO1.setWeekDay(weekState);
                        }
                        dialysisAdviceDO1.setPid(dialysisAdviceDO.getId());
//                    dialysisAdviceDO1.setAdviceUser(dialysisAdviceDO.getAdviceUser());
//                    dialysisAdviceDO1.setStartTime(dialysisAdviceDO.getStartTime());
                        //开始时间
                        String format1 = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
                        if (dialysisAdviceDO1.getStartTime() == null) {
                            dialysisAdviceDO1.setStartTime(new Date());
                        }

                    }).collect(Collectors.toList());
                    dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    tempDialysisList.addAll(dialysisAdviceDOS);
                }
            }else {
                //开始时间
                String format = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
                /*if (dialysisAdviceDO.getStartTime() != null) {
                    DateTime parse = DateUtil.parse(format + " " + DateUtil.format(dialysisAdviceDO.getStartTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
                    dialysisAdviceDO.setStartTime(parse);
                } else {*/
                    dialysisAdviceDO.setStartTime(new Date());
                //}
                //医嘱时间
                dialysisAdviceDO.setAdviceTime(new Date());
                dialysisAdviceDO.setId(null);
                dialysisAdviceDO.setType("0");
                dialysisAdviceDO.setChildName("1");
                dialysisAdviceDO.setFacilityId(arrangeClassesDO.getFacilityId());
                if (StringUtils.isEmpty(dialysisAdviceDO.getPrescribeNo())) {
                    dialysisAdviceDO.setPrescribeNo("1");
                }
                if (!StringUtils.isEmpty(userId)) {
                    dialysisAdviceDO.setAdviceUser(userId);
                }
                dialysisAdviceDO.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                dialysisAdviceDO.setDialyzeName(arrangeClassesDO.getDialysisName());

                //药品名称
                HisDrugDO hisDrugDO = hisDrugMapper.selectById(dialysisAdviceDO.getAdviceId());
                if (!StringUtils.isEmpty(hisDrugDO)) {
                    dialysisAdviceDO.setSpecification(hisDrugDO.getFDrugSpec());
                    dialysisAdviceDO.setChildName(hisDrugDO.getFEnabledMark());

                }
                if (StringUtils.isEmpty(hisDrugDO)) {
                    // 查询自建药
                    DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, dialysisAdviceDO.getAdviceId());
                    if (!StringUtils.isEmpty(drugDO1)) {
                        hisDrugDO = new HisDrugDO();
                        hisDrugDO.setFDrugName(drugDO1.getName());
                        dialysisAdviceDO.setSpecification(drugDO1.getSpec());
                    }
                }
                if (StringUtils.isEmpty(hisDrugDO)) {
                    // 查询耗材
                    HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO.getAdviceId());
                    if (!StringUtils.isEmpty(hisConsumablesDO)) {
                        hisDrugDO = new HisDrugDO();
                        hisDrugDO.setFDrugName(hisConsumablesDO.getConsumName());
                        hisDrugDO.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                        dialysisAdviceDO.setSpecification(hisConsumablesDO.getConsumSpec());
                        dialysisAdviceDO.setChildName(hisConsumablesDO.getEnabledMark());
                        dialysisAdviceDO.setDrugType(2);
                    }
                }


                dialysisAdviceDO.setPatientId(createReqVO.getPatientId());
                dialysisAdviceDO.setDateWeek(DateUtil.beginOfDay(new Date()));
                dialysisAdviceDO.setCreateTime(new Date());
                dialysisAdviceDO.setMedicateState(0);
                if (!StringUtils.isEmpty(weekState)) {
                    dialysisAdviceDO.setWeekDay(weekState);
                }
                dialysisAdviceMapper.insert(dialysisAdviceDO);
                tempDialysisList.add(dialysisAdviceDO);
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getPid, id);
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                    dialysisAdviceDOS = dialysisAdviceDOS.stream().peek(dialysisAdviceDO1 -> {
                        dialysisAdviceDO1.setId(null);
                        dialysisAdviceDO1.setType("0");
                        dialysisAdviceDO1.setPatientId(createReqVO.getPatientId());
                        dialysisAdviceDO1.setDateWeek(DateUtil.beginOfDay(new Date()));
                        dialysisAdviceDO1.setMedicateState(0);
                        dialysisAdviceDO1.setChildName("1");
                        dialysisAdviceDO1.setCreateTime(new Date());
                        if (!StringUtils.isEmpty(userId)) {
                            dialysisAdviceDO1.setAdviceUser(userId);
                        }
                        dialysisAdviceDO1.setFacilityId(arrangeClassesDO.getFacilityId());
                        if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                            dialysisAdviceDO1.setPrescribeNo("1");
                        }

                        //药品名称
                        HisDrugDO hisDrugDO1 = hisDrugMapper.selectById(dialysisAdviceDO1.getAdviceId());
                        if (!StringUtils.isEmpty(hisDrugDO1)) {
                            dialysisAdviceDO1.setSpecification(hisDrugDO1.getFDrugSpec());
                            dialysisAdviceDO1.setChildName(hisDrugDO1.getFEnabledMark());
                        }
                        if (StringUtils.isEmpty(hisDrugDO1)) {
                            // 查询自建药
                            DrugDO drugDO1 = drugMapper.selectOne(DrugDO::getId, dialysisAdviceDO1.getAdviceId());
                            if (!StringUtils.isEmpty(drugDO1)) {
                                hisDrugDO1 = new HisDrugDO();
                                hisDrugDO1.setFDrugName(drugDO1.getName());
                                dialysisAdviceDO1.setSpecification(drugDO1.getSpec());
                                dialysisAdviceDO1.setChildName(hisDrugDO1.getFEnabledMark());
                            }
                        }
                        if (StringUtils.isEmpty(hisDrugDO1)) {
                            // 查询耗材
                            HisConsumablesDO hisConsumablesDO = hisConsumablesMapper.selectOne(HisConsumablesDO::getConsumId, dialysisAdviceDO1.getAdviceId());
                            if (!StringUtils.isEmpty(hisConsumablesDO)) {
                                hisDrugDO1 = new HisDrugDO();
                                hisDrugDO1.setFDrugName(hisConsumablesDO.getConsumName());
                                hisDrugDO1.setFDrugSpec(hisConsumablesDO.getConsumSpec());
                                dialysisAdviceDO1.setSpecification(hisConsumablesDO.getConsumSpec());
                                dialysisAdviceDO1.setChildName(hisConsumablesDO.getEnabledMark());
                                dialysisAdviceDO1.setDrugType(2);
                            }
                        }

                        dialysisAdviceDO1.setDialyzeDictValue(arrangeClassesDO.getDialysisValue());
                        dialysisAdviceDO1.setDialyzeName(arrangeClassesDO.getDialysisName());

                        if (!StringUtils.isEmpty(weekState)) {
                            dialysisAdviceDO1.setWeekDay(weekState);
                        }
                        dialysisAdviceDO1.setPid(dialysisAdviceDO.getId());
//                    dialysisAdviceDO1.setAdviceUser(dialysisAdviceDO.getAdviceUser());
//                    dialysisAdviceDO1.setStartTime(dialysisAdviceDO.getStartTime());
                        //开始时间
                        String format1 = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
                        /*if (dialysisAdviceDO1.getStartTime() != null) {
                            DateTime parse = DateUtil.parse(format1 + " " + DateUtil.format(dialysisAdviceDO1.getStartTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
                            dialysisAdviceDO1.setStartTime(parse);
                        }else {*/
                            dialysisAdviceDO1.setStartTime(new Date());
                        //}
                        //医嘱时间
                        dialysisAdviceDO1.setAdviceTime(new Date());
                    }).collect(Collectors.toList());
                    dialysisAdviceMapper.insertBatch(dialysisAdviceDOS);
                    tempDialysisList.addAll(dialysisAdviceDOS);
                }
            }



                String SystemDeptId = request.getHeader("SystemDeptId");
                String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
                if (!StringUtils.isEmpty(synFlag) && !org.springframework.util.CollectionUtils.isEmpty(tempDialysisList)) {
                    AdminUserRespDTO user = adminUserApi.getUser(tempDialysisList.get(0).getAdviceUser());
                    tempDialysisList.forEach(dialysisAdviceDO1 -> {
                                ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                                Long adviceId = dialysisAdviceDO1.getAdviceId();
                                if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO1.getType()) && "1".equals(dialysisAdviceDO1.getChildName())) {
                                    SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();
                                    try {
                                        Thread.sleep(100);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                    PatientDO patientDO1 = patientMapper.selectById(dialysisAdviceDO1.getPatientId());
                                    if (!StringUtils.isEmpty(patientDO1)) {
                                        synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                                    }
                                    //Date dateWeek = createReqVO.getDateWeek();
                                    //Date startTime = createReqVO.getStartTime();
                                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getStartTime())) {

                                        String format = simpleDateFormat.format(new Date());

                                        synToHisAdviceVo.setDpDate(format);
                                    }else {
                                        synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO1.getStartTime()));
                                    }
                                    synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO1.getAdviceId()));
                                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                                    synToHisAdviceVo.setDrugUsage(dialysisAdviceDO1.getDrugWay());
                                    synToHisAdviceVo.setOnceUsing(dialysisAdviceDO1.getOneNo());
                                    synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO1.getFpreparaUnit());
                                    synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO1.getId()));
                                    synToHisAdviceVo.setDoctorMobile(user.getMobile());
                                    synToHisAdviceVo.setDrugName(dialysisAdviceDO1.getAdviceName());
                                    synToHisAdviceVo.setDrugDays(1);
                                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                                        dialysisAdviceDO1.setPrescribeNo("1");
                                    }
                                    if (StringUtils.isEmpty(dialysisAdviceDO1.getOneNo())) {
                                        dialysisAdviceDO1.setOneNo("1");
                                    }
                                    synToHisAdviceVo.setDrugNum(dialysisAdviceDO1.getPrescribeNo());
                                    DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                                    synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                                    synToHisAdviceVos.add(synToHisAdviceVo);
                                    AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                                    if (!StringUtils.isEmpty(adviceResult)) {
                                        AdviceData data = adviceResult.getData();
                                        if (-1 == data.getInfcode()) {
                                            throw new ServiceException(data.getInfcode(), data.getMessage());
                                        }else if (0 == data.getInfcode()) {
                                            // 更新advice
                                            List<AdviceOutput> output = data.getOutput();
                                            if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                                AdviceOutput adviceOutput = output.get(0);
                                                dialysisAdviceDO1.setDpId(adviceOutput.getDpId());
                                                dialysisAdviceDO1.setDpdId(adviceOutput.getDpdId());
                                                dialysisAdviceMapper.updateById(dialysisAdviceDO1);
                                            }
                                        }
                                    }else {
                                        throw new ServiceException(-1, "返回为null,同步失败");
                                    }
                                }

                            });
                  }
            }

    }



    @Override
    public List<DialysisAdviceRespVO> adviceStatistics(DialysisAdvicePageReqVO pageReqVO) {
        List<ArrangeClassesDO> arrangeClassesDOList = arrangeClassesMapper.selectList(new LambdaQueryWrapperX<ArrangeClassesDO>()
                .eq(ArrangeClassesDO::getTempType, 0)
                .eqIfPresent(ArrangeClassesDO::getDayState, pageReqVO.getWeekDay())
                .inIfPresent(ArrangeClassesDO::getFacilitySubareaId, pageReqVO.getFacilitySubareaId())
                .betweenIfPresent(ArrangeClassesDO::getClassesTime, pageReqVO.getStartTime() != null ? DateUtil.beginOfDay(pageReqVO.getStartTime()) : pageReqVO.getStartTime(), pageReqVO.getEndTime() != null ? DateUtil.endOfDay(pageReqVO.getEndTime()) : pageReqVO.getEndTime())
        ).stream().collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(arrangeClassesDOList)) {
            List<Long> patIdList = arrangeClassesDOList.stream().map(ArrangeClassesDO::getPatientId).collect(Collectors.toList());
            pageReqVO.setPatientIdList(patIdList);
            pageReqVO.setWeekDay(null);
            pageReqVO.setFacilitySubareaId(null);
        }

        MPJLambdaWrapper<DialysisAdviceDO> wrapper = new MPJLambdaWrapper<>(DialysisAdviceDO.class);
        wrapper.leftJoin(PatientDO.class, PatientDO::getId, DialysisAdviceDO::getPatientId)
                .select(DialysisAdviceDO::getId, DialysisAdviceDO::getPatientId
                        , DialysisAdviceDO::getSpecification, DialysisAdviceDO::getSpellName
                        , DialysisAdviceDO::getFspecUnit, DialysisAdviceDO::getAdviceName,DialysisAdviceDO::getAdviceId)
                .selectAs(PatientDO::getName, "patientName")
                .selectSum(DialysisAdviceDO::getPrescribeNo, "adviceNameCount")
                .between(pageReqVO.getStartTime() != null && pageReqVO.getEndTime() != null, DialysisAdviceDO::getStartTime
                        , pageReqVO.getStartTime() != null ? DateUtil.beginOfDay(pageReqVO.getStartTime()) : null
                        , pageReqVO.getEndTime() != null ? DateUtil.endOfDay(pageReqVO.getEndTime()) : null)
                .eq(StrUtil.isNotEmpty(pageReqVO.getWeekDay()), DialysisAdviceDO::getWeekDay, pageReqVO.getWeekDay())
                .eq(StrUtil.isNotEmpty(pageReqVO.getPatientSource()), DialysisAdviceDO::getPatientSource, pageReqVO.getPatientSource())
                .eq(StrUtil.isNotEmpty(pageReqVO.getAdviceState()), DialysisAdviceDO::getAdviceState, pageReqVO.getAdviceState())
                .eq(StrUtil.isNotEmpty(pageReqVO.getType()),DialysisAdviceDO::getType,pageReqVO.getType())
                .in(!org.springframework.util.CollectionUtils.isEmpty(pageReqVO.getPatientIdList()),DialysisAdviceDO::getPatientId,pageReqVO.getPatientIdList())
                .eq(DialysisAdviceDO::getDeleted,0)
                .and(i->i.isNull(DialysisAdviceDO::getPushStatus).or().ne(DialysisAdviceDO::getPushStatus,1))
                .and(i -> i
                        .isNull(DialysisAdviceDO::getDrugType)
                        .or()
                        .ne(DialysisAdviceDO::getDrugType,2))
                .isNotNull(DialysisAdviceDO::getDateWeek)
                .in(!org.springframework.util.CollectionUtils.isEmpty(pageReqVO.getDrugWay()), DialysisAdviceDO::getDrugWay, pageReqVO.getDrugWay())
                .in(!org.springframework.util.CollectionUtils.isEmpty(pageReqVO.getFacilitySubareaId()),DialysisAdviceDO::getFacilitySubareaId,pageReqVO.getFacilitySubareaId())
                .like(StrUtil.isNotEmpty(pageReqVO.getPatientName()), DialysisAdviceDO::getPatientName, pageReqVO.getPatientName())
                .like(StrUtil.isNotEmpty(pageReqVO.getMore()),PatientDO::getName,pageReqVO.getMore())
                .like(StrUtil.isNotEmpty(pageReqVO.getKeyword()),DialysisAdviceDO::getAdviceName,pageReqVO.getKeyword())
                .groupBy(1==pageReqVO.getStatisticsType(),DialysisAdviceDO::getPatientId, DialysisAdviceDO::getAdviceName,DialysisAdviceDO::getAdviceDesprition)
                .groupBy(2==pageReqVO.getStatisticsType(), DialysisAdviceDO::getAdviceName,DialysisAdviceDO::getSpecification)
                .orderByDesc(1==pageReqVO.getStatisticsType(),"patientName")
                .orderByDesc(2==pageReqVO.getStatisticsType(),"adviceNameCount" );
        List<DialysisAdviceRespVO> dialysisAdviceRespVOList = dialysisAdviceMapper.selectJoinList(DialysisAdviceRespVO.class, wrapper);
        if (!CollectionUtil.isEmpty(dialysisAdviceRespVOList)) {
            List<HisComboDO> hisComboDOS = hisComboMapper.selectList();
            if (!CollectionUtil.isEmpty(hisComboDOS)) {
                List<Long> collect = hisComboDOS.stream().map(HisComboDO::getPackageId).collect(Collectors.toList());
                dialysisAdviceRespVOList = dialysisAdviceRespVOList.stream().filter(dialysisAdviceRespVO -> !collect.contains(dialysisAdviceRespVO.getAdviceId())).collect(Collectors.toList());
            }
            List<HisInformationDO> hisInformationDOS = hisInformationMapper.selectList();
            if (!CollectionUtil.isEmpty(hisInformationDOS)) {
                List<Long> collect = hisInformationDOS.stream().map(HisInformationDO::getItemId).collect(Collectors.toList());
                dialysisAdviceRespVOList = dialysisAdviceRespVOList.stream().filter(dialysisAdviceRespVO -> !collect.contains(dialysisAdviceRespVO.getAdviceId())).collect(Collectors.toList());
            }
        }
        return dialysisAdviceRespVOList;
    }

    @Override
    public Integer getCountById(String id) {
        Integer result = 0;
        List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(DialysisAdviceDO::getDpId, id);
        if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
            dialysisAdviceDOS = dialysisAdviceDOS.stream().filter(dialysisAdviceDO -> dialysisAdviceDO.getAdviceId() > 1000000).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                result = dialysisAdviceDOS.size();
            }
        }
        return result;
    }

    @Override
    public void deleteAllDialysisAdvice(DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        if (CollectionUtil.isNotEmpty(createReqVO.getDialysisIdList())) {
            String SystemDeptId = request.getHeader("SystemDeptId");
            String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
            List<DialysisAdviceDO> dialysisAdviceDOList = new ArrayList<>();
            // 删除
            for (Long id: createReqVO.getDialysisIdList()) {
                DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
                if (!StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getAdviceId()) && dialysisAdviceDO.getAdviceId() > 10000000
                        && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && "0".equals(dialysisAdviceDO.getType()) && !StringUtils.isEmpty(dialysisAdviceDO.getDpId())){
                    dialysisAdviceDOList.add(dialysisAdviceDO);
                }
                // 查询子药
                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, dialysisAdviceDO.getId())
                        .eq(DialysisAdviceDO::getDeleted, 0)
                        .eq(DialysisAdviceDO::getStopStatus, "0"));
                if (CollectionUtil.isNotEmpty(dialysisAdviceDOS)) {
                    dialysisAdviceDOS.forEach(dialysisAdviceDO1 -> {
                        if (!StringUtils.isEmpty(dialysisAdviceDO1) && !StringUtils.isEmpty(dialysisAdviceDO1.getAdviceId()) && dialysisAdviceDO1.getAdviceId() > 10000000
                                && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && "0".equals(dialysisAdviceDO1.getType()) && !StringUtils.isEmpty(dialysisAdviceDO1.getDpId())){
                            dialysisAdviceDOList.add(dialysisAdviceDO1);
                        }
                    });
                }

                dialysisAdviceMapper.deleteById(id);
                dialysisAdviceMapper.delete(new LambdaUpdateWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
            }

            if (CollectionUtil.isNotEmpty(dialysisAdviceDOList)) {
                DialysisAdviceDO dialysisAdviceDO = dialysisAdviceDOList.get(0);

                List<Long> dialysisIdList = createReqVO.getDialysisIdList();
                String idStringList = dialysisAdviceDOList.stream().map(DialysisAdviceDO::getId).map(String::valueOf).collect(Collectors.joining(","));

                if (!StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getAdviceId()) && dialysisAdviceDO.getAdviceId() > 10000000
                        && !StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && "0".equals(dialysisAdviceDO.getType()) && !StringUtils.isEmpty(dialysisAdviceDO.getDpId()))  {
                    AdviceDeleteVo adviceDeleteVo = new AdviceDeleteVo();
                    List<AdviceDelete> adviceDeleteList = new ArrayList<>();
                    AdviceDelete adviceDelete = new AdviceDelete();
                    adviceDelete.setDpId(dialysisAdviceDO.getDpId());
                    adviceDelete.setHmsAdvId(idStringList);
                    adviceDelete.setDrugName(dialysisAdviceDO.getAdviceName());
                    adviceDelete.setOnceUsing(dialysisAdviceDO.getOneNo());
                    adviceDelete.setDrugNum(dialysisAdviceDO.getPrescribeNo());
                    adviceDelete.setDelDpMark(1);
                    adviceDeleteList.add(adviceDelete);
                    adviceDeleteVo.setAdviceDeleteList(adviceDeleteList);

                    AdviceResult adviceResult = deleteUrl(deleteUrl, adviceDeleteList);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        AdviceData data = adviceResult.getData();
                        if (-1 == data.getInfcode()) {
                            throw new ServiceException(data.getInfcode(), data.getMessage());
                        } else if (0 == data.getInfcode()) {

                        }
                    }else {
                        throw new ServiceException(-1, "删除失败");
                    }

                }
            }
            for (Long id: createReqVO.getDialysisIdList()) {
                dialysisAdviceMapper.deleteById(id);
                dialysisAdviceMapper.delete(new LambdaUpdateWrapper<DialysisAdviceDO>().eq(DialysisAdviceDO::getPid, id));
            }
        }
    }

    @Override
    public void reSyncAdvice(DialysisAdviceCreateReqVO createReqVO, HttpServletRequest request) {
        if (CollectionUtil.isNotEmpty(createReqVO.getDialysisIdList())) {
            String SystemDeptId = request.getHeader("SystemDeptId");
            String synFlag = redisTemplate.opsForValue().get("synAdvice:" + SystemDeptId);
            List<DialysisAdviceDO> tempDialysisList = new ArrayList<>();
            // 查询
            List<Long> dialysisIdList = createReqVO.getDialysisIdList();
            dialysisIdList.forEach(id -> {
                DialysisAdviceDO dialysisAdviceDO = dialysisAdviceMapper.selectById(id);
                if (!StringUtils.isEmpty(dialysisAdviceDO) && !StringUtils.isEmpty(dialysisAdviceDO.getAdviceId())  && dialysisAdviceDO.getAdviceId() > 10000000) {
                    tempDialysisList.add(dialysisAdviceDO);
                }

                List<DialysisAdviceDO> dialysisAdviceDOS = dialysisAdviceMapper.selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                        .eq(DialysisAdviceDO::getPid, dialysisAdviceDO.getId())
                        .eq(DialysisAdviceDO::getDeleted, 0)
                        .eq(DialysisAdviceDO::getPushStatus,0)
                        .gt(DialysisAdviceDO::getAdviceId,100000)
                        .eq(DialysisAdviceDO::getType, "0"));

                tempDialysisList.addAll(dialysisAdviceDOS);
            });

            if (!StringUtils.isEmpty(SystemDeptId) && !StringUtils.isEmpty(synFlag) && CollectionUtil.isNotEmpty(tempDialysisList)) {
                DialysisAdviceDO dialysisAdviceDO = tempDialysisList.get(0);
                List<AdviceDelete> adviceDeleteList = new ArrayList<>();
                AdviceDelete adviceDelete = new AdviceDelete();
                adviceDelete.setDpId(dialysisAdviceDO.getDpId());
                adviceDelete.setHmsAdvId(null);
                adviceDelete.setDrugName(dialysisAdviceDO.getAdviceName());
                adviceDelete.setOnceUsing(dialysisAdviceDO.getOneNo());
                adviceDelete.setDrugNum(dialysisAdviceDO.getPrescribeNo());
                adviceDelete.setDelDpMark(1);
                adviceDeleteList.add(adviceDelete);
                AdviceResult adviceResult = deleteUrl(deleteUrl, adviceDeleteList);
                if (!StringUtils.isEmpty(adviceResult)) {
                    AdviceData data = adviceResult.getData();
                    if (-1 == data.getInfcode()) {
                        throw new ServiceException(data.getInfcode(), data.getMessage());
                    } else if (0 == data.getInfcode()) {
                        // 把dpid更新为null
                        List<Long> collect = tempDialysisList.stream().map(DialysisAdviceDO::getId).collect(Collectors.toList());

                        dialysisAdviceMapper.upDpIdNull(collect);
                    }
                }else {
                    throw new ServiceException(-1, "删除失败");
                }
            }
            // 重新同步

            AdminUserRespDTO user = adminUserApi.getUser(tempDialysisList.get(0).getAdviceUser());
            PatientDO patientDO1 = patientMapper.selectById(tempDialysisList.get(0).getPatientId());
            tempDialysisList.forEach(dialysisAdviceDO1 -> {
                ArrayList<SynToHisAdviceVo> synToHisAdviceVos = new ArrayList<>();
                Long adviceId = dialysisAdviceDO1.getAdviceId();
                if (!StringUtils.isEmpty(adviceId) && adviceId > 10000000 && "0".equals(dialysisAdviceDO1.getType())) {
                    SynToHisAdviceVo synToHisAdviceVo = new SynToHisAdviceVo();
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }

                    if (!StringUtils.isEmpty(patientDO1)) {
                        synToHisAdviceVo.setPatientIdCard(patientDO1.getIdCard());
                    }
                    //Date dateWeek = createReqVO.getDateWeek();
                    //Date startTime = createReqVO.getStartTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getStartTime())) {

                        String format = simpleDateFormat.format(new Date());

                        synToHisAdviceVo.setDpDate(format);
                    }else {
                        synToHisAdviceVo.setDpDate(simpleDateFormat.format(dialysisAdviceDO1.getStartTime()));
                    }
                    synToHisAdviceVo.setDrugId(String.valueOf(dialysisAdviceDO1.getAdviceId()));
                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                    synToHisAdviceVo.setDrugUsage(dialysisAdviceDO1.getDrugWay());
                    synToHisAdviceVo.setOnceUsing(dialysisAdviceDO1.getOneNo());
                    synToHisAdviceVo.setOnceUsingUnit(dialysisAdviceDO1.getFpreparaUnit());
                    synToHisAdviceVo.setHmsAdvId(String.valueOf(dialysisAdviceDO1.getId()));
                    synToHisAdviceVo.setDoctorMobile(user.getMobile());
                    synToHisAdviceVo.setDrugName(dialysisAdviceDO1.getAdviceName());
                    synToHisAdviceVo.setDrugDays(1);
                    synToHisAdviceVo.setDrugInterval(dialysisAdviceDO1.getFrequency());
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getPrescribeNo())) {
                        dialysisAdviceDO1.setPrescribeNo("1");
                    }
                    if (StringUtils.isEmpty(dialysisAdviceDO1.getOneNo())) {
                        dialysisAdviceDO1.setOneNo("1");
                    }
                    synToHisAdviceVo.setDrugNum(dialysisAdviceDO1.getPrescribeNo());
                    DeptRespDTO dept = deptApi.getDept(Long.valueOf(SystemDeptId));
                    synToHisAdviceVo.setHospitalId(dept.getHospitalId());
                    synToHisAdviceVos.add(synToHisAdviceVo);
                    AdviceResult adviceResult = postUrl(adviceUrl, synToHisAdviceVos);
                    if (!StringUtils.isEmpty(adviceResult)) {
                        AdviceData data = adviceResult.getData();
                        if (-1 == data.getInfcode()) {
                            throw new ServiceException(data.getInfcode(), data.getMessage());
                        }else if (0 == data.getInfcode()) {
                            // 更新advice
                            List<AdviceOutput> output = data.getOutput();
                            if (!org.springframework.util.CollectionUtils.isEmpty(output)){
                                AdviceOutput adviceOutput = output.get(0);
                                dialysisAdviceDO1.setDpId(adviceOutput.getDpId());
                                dialysisAdviceDO1.setDpdId(adviceOutput.getDpdId());
                                dialysisAdviceMapper.updateById(dialysisAdviceDO1);
                            }
                        }
                    }else {
                        throw new ServiceException(-1, "返回为null,同步失败");
                    }
                }

            });
        }

    }

    @Override
    public void activeAdviceBatch(DialysisAdviceUpdateReqVO updateReqVO) {
        DialysisAdviceDO dialysisAdviceDO = new DialysisAdviceDO();
        dialysisAdviceDO.setActivateTime(updateReqVO.getActivateTime());
        dialysisAdviceDO.setActivateUser(updateReqVO.getActivateUser());
        dialysisAdviceDO.setCheckUser(updateReqVO.getCheckUser());
        dialysisAdviceDO.setState("1");
        dialysisAdviceDO.setStopStatus("1");
        dialysisAdviceDO.setRemark(updateReqVO.getRemark());
        if(updateReqVO.getSelectedAdviceIds().size() >= 1){
            dialysisAdviceMapper.update(dialysisAdviceDO, new LambdaUpdateWrapper<DialysisAdviceDO>()
                    .in(DialysisAdviceDO::getPid, updateReqVO.getSelectedAdviceIds())
                    .or()
                    .in(DialysisAdviceDO::getId,updateReqVO.getSelectedAdviceIds()));
        }
    }

}
