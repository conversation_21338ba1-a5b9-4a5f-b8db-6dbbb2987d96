package com.thj.boot.module.business.controller.admin.statistics;

import cn.dev33.satoken.annotation.SaIgnore;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassStatisticsVO;
import com.thj.boot.module.business.service.statistics.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/statistics")
public class StatisticsController {


    @Autowired
    private StatisticsService statisticsService;

    /**
     * 大屏统计
     */
    @SaIgnore
    @PostMapping("/todayData")
    public List<TodayDataVO> getTodayData() {
        return statisticsService.getTodayData();
    }


    /**
     * 任职数据统计
     */
    @SaIgnore
    @PostMapping("/employeeStatistics")
    public String getEmployeeStatistics() throws IOException {
        return statisticsService.getEmployeeStatistics();
    }
}
