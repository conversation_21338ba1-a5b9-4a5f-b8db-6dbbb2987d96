package com.thj.boot.module.business.controller.admin.wateruseregister;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.wateruseregister.WaterUseRegisterConvert;
import com.thj.boot.module.business.dal.datado.wateruseregister.WaterUseRegisterDO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterCreateReqVO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterPageReqVO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterRespVO;
import com.thj.boot.module.business.pojo.wateruseregister.vo.WaterUseRegisterUpdateReqVO;
import com.thj.boot.module.business.service.wateruseregister.WaterUseRegisterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 水处理机使用状态
 */
@RestController
@RequestMapping("/business/water-use-register")
@Validated
public class WaterUseRegisterController {

    @Resource
    private WaterUseRegisterService waterUseRegisterService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createWaterUseRegister(@RequestBody WaterUseRegisterCreateReqVO createReqVO) {
        return success(waterUseRegisterService.createWaterUseRegister(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateWaterUseRegister(@RequestBody WaterUseRegisterUpdateReqVO updateReqVO) {
        waterUseRegisterService.updateWaterUseRegister(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteWaterUseRegister(@RequestParam("id") Long id) {
        waterUseRegisterService.deleteWaterUseRegister(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<WaterUseRegisterRespVO> getWaterUseRegister(@RequestParam("id") Long id) {
        WaterUseRegisterDO waterUseRegister = waterUseRegisterService.getWaterUseRegister(id);
        return success(WaterUseRegisterConvert.INSTANCE.convert(waterUseRegister));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<WaterUseRegisterRespVO>> getWaterUseRegisterList(WaterUseRegisterCreateReqVO createReqVO) {
        List<WaterUseRegisterDO> list = waterUseRegisterService.getWaterUseRegisterList(createReqVO);
        return success(WaterUseRegisterConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<WaterUseRegisterRespVO>> getWaterUseRegisterPage(@RequestBody WaterUseRegisterPageReqVO pageVO) {
        PageResult<WaterUseRegisterDO> pageResult = waterUseRegisterService.getWaterUseRegisterPage(pageVO);
        return success(WaterUseRegisterConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 保存完之后显示最新的一条数据
     */
    @GetMapping("/getNew")
    public CommonResult<WaterUseRegisterRespVO> getNewUseRegister(WaterUseRegisterCreateReqVO createReqVO) {
        return success(waterUseRegisterService.getNewUseRegister(createReqVO));
    }


}
