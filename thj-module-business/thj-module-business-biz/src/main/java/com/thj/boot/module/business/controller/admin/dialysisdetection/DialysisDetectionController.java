package com.thj.boot.module.business.controller.admin.dialysisdetection;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.dialysisdetection.vo.*;
import com.thj.boot.module.business.convert.dialysisdetection.DialysisDetectionConvert;
import com.thj.boot.module.business.dal.datado.detectionwarn.DetectionWarnDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionPageReqVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionRespVO;
import com.thj.boot.module.business.pojo.dialysisdetection.vo.DialysisDetectionUpdateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.boot.module.business.service.dialysisdetection.DialysisDetectionService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 检测记录
 */
@RestController
@RequestMapping("/business/dialysis-detection")
@Validated
public class DialysisDetectionController {

    @Resource
    private DialysisDetectionService dialysisDetectionService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialysisDetection(@RequestBody DialysisDetectionCreateReqVO createReqVO,HttpServletRequest request) throws ParseException {
        return success(dialysisDetectionService.createDialysisDetection(createReqVO,request));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialysisDetection(@RequestBody DialysisDetectionUpdateReqVO updateReqVO) throws ParseException {
        dialysisDetectionService.updateDialysisDetection(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialysisDetection(@RequestParam("id") Long id, String state) {
        dialysisDetectionService.deleteDialysisDetection(id, state);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DialysisDetectionRespVO> getDialysisDetection(@RequestParam("id") Long id) {
        return success(dialysisDetectionService.getDialysisDetection(id));
    }

    /**
     * 不分页
     */
    @PostMapping("/list")
    public CommonResult<List<DialysisDetectionRespVO>> getDialysisDetectionList(@RequestBody DialysisDetectionCreateReqVO createReqVO) {
        List<DialysisDetectionDO> list = dialysisDetectionService.getDialysisDetectionList(createReqVO);
        return success(DialysisDetectionConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialysisDetectionRespVO>> getDialysisDetectionPage(@RequestBody DialysisDetectionPageReqVO pageVO) {
        PageResult<DialysisDetectionDO> pageResult = dialysisDetectionService.getDialysisDetectionPage(pageVO);
        return success(DialysisDetectionConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 检测记录：默认超滤率
     */
    @PostMapping("/getUltrafiltration")
    public CommonResult<DialysisDetectionRespVO> getUltrafiltration(@RequestBody HemodialysisManagerCreateReqVO createReqVO) {
        return success(dialysisDetectionService.getUltrafiltration(createReqVO));
    }

    /**
     * 获取开始-结束透析时间
     *
     * @param vo
     * @return
     */
    @PostMapping("/getDialysisTime")
    public CommonResult<DialysisTimeVo> getDialysisTime(@RequestBody DialysisTimeVo vo) {
        return success(dialysisDetectionService.getDialysisTime(vo));
    }

    /***
     * <AUTHOR>
     * @date 2024/5/17 13:59
     * @Description 透析检测-血压列表只展示当天透析患者信息
     **/
    @PostMapping("/getBloodPressure")
    public CommonResult<PageResult<BloodPressureVO>> getBloodPressure(@RequestBody BloodPressureVO bloodPressureVO, HttpServletRequest request) {
        return success(dialysisDetectionService.getBloodPressure(bloodPressureVO, request));
    }

    /**
     * 导出
     */
    @PostMapping("/export-excel")
    public void exportDevManExcel(@RequestBody BloodPressureVO exportReqVO,
                                  HttpServletResponse response, HttpServletRequest request) throws IOException {
        PageResult<BloodPressureVO> bloodPressure = dialysisDetectionService.getBloodPressure(exportReqVO, request);
        if (1 == exportReqVO.getType()) {
            List<BloodPressureExcelVO> bloodPressureExcelVOS = DialysisDetectionConvert.INSTANCE.convertExecl(bloodPressure.getList());
            EasyExcelUtils.write(response, "血压.xls", "血压", BloodPressureExcelVO.class, bloodPressureExcelVOS);
        } else if (2 == exportReqVO.getType()) {
            List<BloodPressureRecordExcelVO> bloodPressureRecordExcelVOS = DialysisDetectionConvert.INSTANCE.convertExecl2(bloodPressure.getList());
            EasyExcelUtils.write(response, "检测记录.xls", "检测记录", BloodPressureRecordExcelVO.class, bloodPressureRecordExcelVOS);
        }
    }

    /**
     * 新增预警值
     */
    @PostMapping("/globalWarn")
    public CommonResult<Boolean> globalWarn(@RequestBody DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        dialysisDetectionService.globalWarn(warnVO, request);
        return success(true);
    }


    /**
     * 预警值修改
     */
    @PostMapping("/updateGlobalWarn")
    public CommonResult<Boolean> updateGlobalWarn(@RequestBody DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        dialysisDetectionService.updateGlobalWarn(warnVO, request);
        return success(true);
    }


    /**
     * 预警值详情
     */
    @PostMapping("/globalWarnInfo")
    public CommonResult<DetectionWarnDO> globalWarnInfo(@RequestBody DialysisDetectionWarnVO warnVO, HttpServletRequest request) {
        return success(dialysisDetectionService.globalWarnInfo(warnVO, request));
    }

    /**
     * 获取最近的记录
     */
    @GetMapping("/getRecentRecord")
    public CommonResult<DialysisDetectionRespVO> getRecentRecord(@RequestParam("deviceCode") String deviceCode,@RequestParam("orgCode") String orgCode, @RequestParam("searchDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date searchDate) {
        return success(dialysisDetectionService.getRecentRecord(deviceCode, orgCode,searchDate));
    }



}
