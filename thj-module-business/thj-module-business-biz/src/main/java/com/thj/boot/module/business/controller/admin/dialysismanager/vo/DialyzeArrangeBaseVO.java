package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/2 17:54
 * @description
 */
@Data
public class DialyzeArrangeBaseVO implements Serializable {
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 机号
     */
    private String facilityName;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 分区名称
     */
    private String facilitySubareaName;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 姓名
     */
    private String name;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 住院号
     */
    private String hospitalNo;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 透析方式
     */
    private String dialyzeWayValue;

}
