package com.thj.boot.module.business.pojo.arrangeclasses.vo;

import cn.hutool.core.date.DateTime;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArrangeClassesRespVO extends ArrangeClassesBaseVO {

    /**
     * 周几
     */
    private String weekName;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 签到患者姓名
     */
    private String registerName;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 干体重状态
     */
    private String dryState;
    /**
     * 姓名
     */
    private String name;
    /**
     * 1-男，2-女
     */
    private String sex;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 病区
     */
    private String endemicArea;
    /**
     * 床号
     */
    private String bedNo;
    /**
     * 初始透析次数
     */
    private String initDialyzeNo;
    /**
     * 首次透析日期
     */
    private Date firstReceiveTime;
    /**
     * 疾病诊断
     */
    private String diseaseReasonNames;
    /**
     * 入科方式
     */
    private String enterWay;
    /**
     * 拼音
     */
    private String spellName;

    /**
     * 日期
     */
    private String dateStr;
    /**
     * 班次
     */
    private String weekDay;
    /**
     * 病区
     */
    private String facilitySubareaName;
    /**
     * 冻结解冻时间
     */
    private String classesTimeStr;

    private Integer sort;

    private String endTimeMinNow;

    private Integer checkStatus;

    /**
     * 透析方案id
     */
    private Long protocolId;

    /**
     * 签到时间
     */
    private Date registerTime;

    /**
     * 传染病
     */
    private String infect;

}
