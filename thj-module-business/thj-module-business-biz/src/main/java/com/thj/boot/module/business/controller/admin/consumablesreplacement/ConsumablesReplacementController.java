package com.thj.boot.module.business.controller.admin.consumablesreplacement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.patient.vo.FirstTreatmentExcelVO;
import com.thj.boot.module.business.pojo.consumablesReplacement.vo.ConsumablesReplacementExcelVO;
import com.thj.boot.module.business.pojo.consumablesReplacement.vo.ConsumablesReplacementPageReqVO;
import com.thj.boot.module.business.pojo.consumablesReplacement.vo.ConsumablesReplacementReqVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import com.thj.boot.module.business.service.consumablesreplacement.ConsumablesReplacementService;
import com.thj.boot.module.system.pojo.dept.vo.DeptCreateReqVO;
import com.thj.boot.module.system.pojo.dept.vo.DeptRespVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 耗材更换预警
 */
@RestController
@RequestMapping("/business/consumables-replacement")
public class ConsumablesReplacementController {

    @Resource
    private ConsumablesReplacementService consumablesReplacementService;

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<ConsumablesReplacementReqVO>> getConsumablesReplacementPage(@RequestBody ConsumablesReplacementPageReqVO pageReqVO){
        return success(consumablesReplacementService.getConsumablesReplacementPage(pageReqVO));
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void exportConsumablesReplacement(@RequestBody ConsumablesReplacementPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        // 获取导出的数据
        PageResult<ConsumablesReplacementReqVO> consumablesReplacementPage = consumablesReplacementService.getConsumablesReplacementPage(pageReqVO);
        List<ConsumablesReplacementReqVO> consumablesReplacementPageList = consumablesReplacementPage.getList();

        // 数据序号
        AtomicInteger indexCounter = new AtomicInteger(1);
        // 处理数据
        List<ConsumablesReplacementExcelVO> excelVOList = consumablesReplacementPageList.stream().map(item -> {
            ConsumablesReplacementExcelVO consumablesReplacementExcelVO = new ConsumablesReplacementExcelVO();
            // 设置序号
            consumablesReplacementExcelVO.setIndex(indexCounter.getAndIncrement());
            // 设置序列号
            consumablesReplacementExcelVO.setCode(item.getCode());
            // 设置设备名称

            // 设置设备型号

            // 设置透析机消毒液更换
            consumablesReplacementExcelVO.setLiquidReplaceDiffMonths(this.getWaringText(item.getLiquidReplaceDiffMonths()));
            // 设置细菌过滤器更换
            consumablesReplacementExcelVO.setBacterialFilterDiffMonths(this.getWaringText(item.getBacterialFilterDiffMonths()));
            // 设置空气滤网更换
            consumablesReplacementExcelVO.setAirFilterDiffMonths(this.getWaringText(item.getAirFilterDiffMonths()));

            return consumablesReplacementExcelVO;
        })
        .collect(Collectors.toList());
        EasyExcelUtils.write(response, "耗材更换预警.xls", "耗材更换预警", ConsumablesReplacementExcelVO.class, excelVOList);
    }

    /**
     * 预警文字
     * @param noClearMonth
     * @return
     */
    private String getWaringText(double noClearMonth) {
        if (noClearMonth == 0) return "";
        int number = (int) noClearMonth;
        if (number == -1 || number > 12) return "您已超过一年未更换请注意检查";

        Map<Integer, String> numMap = Stream.of(
                new AbstractMap.SimpleEntry<>(1, "一"),
                new AbstractMap.SimpleEntry<>(2, "二"),
                new AbstractMap.SimpleEntry<>(3, "三"),
                new AbstractMap.SimpleEntry<>(4, "四"),
                new AbstractMap.SimpleEntry<>(5, "五"),
                new AbstractMap.SimpleEntry<>(6, "六"),
                new AbstractMap.SimpleEntry<>(7, "七"),
                new AbstractMap.SimpleEntry<>(8, "八"),
                new AbstractMap.SimpleEntry<>(9, "九"),
                new AbstractMap.SimpleEntry<>(10, "十"),
                new AbstractMap.SimpleEntry<>(11, "十一"),
                new AbstractMap.SimpleEntry<>(12, "十二")
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (number > 0 && number <= 12) {
            return "您已" + numMap.get(number) + "月未更换请注意检查";
        }
        return "";
    }



}
