package com.thj.boot.module.business.controller.admin.dialysisdetection.vo;

import com.thj.boot.common.pojo.PageParam;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/17 13:53
 * @description
 */
@Data
public class BloodPressureVO extends PageParam {
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者id
     */
    private String patientId;
    /**
     * 机号名称
     */
    private String facilityName;
    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 病区id
     */
    private String facilitySubareaId;
    /**
     * 病区名称
     */
    private String faciitySubareaName;
    /**
     * 透析模式字典值
     */
    private String dialysisValue;
    /**
     * 透析模式名称
     */
    private String dialysisName;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 目标脱水
     */
    private String dehydration;
    /**
     * 超滤总量
     */
    private String ultrafiltrationTotal;
    /**
     * 实际超滤量
     */
    private String actualUltrafiltrationCapacity;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeight;
    /**
     * 透前血压脉搏
     */
    private String beforeBlood;
    /**
     * p次数
     */
    private String pno;
    /**
     * bp
     */
    private String bpNoOne;
    /**
     * bp
     */
    private String bpNoTwo;
    /**
     * 第1次(血压+脉搏)
     */
    private String one;
    /**
     * 第2次
     */
    private String two;
    /**
     * 第3次
     */
    private String three;
    /**
     * 第4次
     */
    private String four;
    /**
     * 第5次
     */
    private String five;
    /**
     * 第n次
     */
    private String more;

    /**
     * 第n次
     */
    private List<BloodPressureVO> moreList = new ArrayList<>();
    /**
     * 透后p
     */
    private String afterPNo;
    /**
     * 透后bp
     */
    private String afterBpOne;
    /**
     * 透后bp
     */
    private String afterBpTwo;
    /**
     * 透后血压脉搏
     */
    private String afterBlood;
    /**
     * 排班id
     */
    private Long arrangeClassesId;
    /**
     * 每天时间段
     */
    private String dayState;
    /**
     * 搜索字段
     */
    private String keyword;
    /**
     * 搜索多个病区
     */
    private List<String> facilitySubareaIds;
    /**
     * type  1血压    2 监测记录
     */
    private Integer type;
    /**
     * 脉搏
     */
    private String pulse;
    /**
     * 呼吸
     */
    private String breathe;
    /**
     * KT/V(在线)
     */
    private String ktv;
    /**
     * 血流量
     */
    private String bloodFlow;
    /**
     * 动脉压
     */
    private String arterialPressure;
    /**
     * 静脉压
     */
    private String venousPressure;
    /**
     * 跨膜压
     */
    private String transmembranePressure;
    /**
     * 超滤率
     */
    private String ultrafiltrationRate;
    /**
     * 超滤量
     */
    private String ultrafiltrationCapacity;
    /**
     * 钠浓度
     */
    private String sodiumConcentration;
    /**
     * 电导度
     */
    private String conductance;
    /**
     * 透析液温度
     */
    private String dialysateTemperature;
    /**
     * 置换率
     */
    private String replacementRate;
    /**
     * 置换量
     */
    private String displacementQuantity;

    /**
     * 透前血压背景色
     */
    private String bgColorBefore;
    /**
     * 透后血压背景色
     */
    private String bgColorAfter;

    private String bgColorOne;
    private String bgColorTwo;
    private String bgColorThree;
    private String bgColorFour;
    private String bgColorFive;
    private String bgColorMore;

    //==========检测记录=========
    /**
     * 脉搏
     */
    private String bgColorPulse;
    /**
     * 呼吸
     */
    private String bgColorBreathe;
    /**
     * KT/V(在线)
     */
    private String bgColorKtv;
    /**
     * 血流量
     */
    private String bgColorBloodFlow;
    /**
     * 动脉压
     */
    private String bgColorArterialPressure;
    /**
     * 静脉压
     */
    private String bgColorVenousPressure;
    /**
     * 跨膜压
     */
    private String bgColorTransmembranePressure;
    /**
     * 超滤率
     */
    private String bgColorUltrafiltrationRate;
    /**
     * 超滤量
     */
    private String bgColorUltrafiltrationCapacity;
    /**
     * 钠浓度
     */
    private String bgColorSodiumConcentration;
    /**
     * 电导度
     */
    private String bgColorConductance;
    /**
     * 透析液温度
     */
    private String bgColorDialysateTemperature;
    /**
     * 置换率
     */
    private String bgColorReplacementRate;
    /**
     * 置换量
     */
    private String bgColorDisplacementQuantity;

}
