package com.thj.boot.module.business.dal.mapper.bloodroad;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.dialysismanager.vo.BloodPassageComplicationRespVO;
import com.thj.boot.module.business.dal.datado.bloodroad.BloodRoadDO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadCreateReqVO;
import com.thj.boot.module.business.pojo.bloodroad.vo.BloodRoadPageReqVO;
import com.thj.boot.module.business.pojo.repairregister.vo.RepairRegisterRespVO;
import com.thj.boot.module.business.service.departmentControl.param.VascularAccessParams;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 血管通路 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BloodRoadMapper extends BaseMapperX<BloodRoadDO> {

    default PageResult<BloodRoadDO> selectPage(BloodRoadPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BloodRoadDO>()
                .eqIfPresent(BloodRoadDO::getPatientId, reqVO.getPatientId())
                .likeIfPresent(BloodRoadDO::getPatientName, reqVO.getPatientName())
                .likeIfPresent(BloodRoadDO::getPatientNickName, reqVO.getPatientNickName())
                .eqIfPresent(BloodRoadDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(BloodRoadDO::getPart, reqVO.getPart())
                .eqIfPresent(BloodRoadDO::getType, reqVO.getType())
                .eqIfPresent(BloodRoadDO::getAccessRoad, reqVO.getAccessRoad())
                .eqIfPresent(BloodRoadDO::getRecipe, reqVO.getRecipe())
                .eqIfPresent(BloodRoadDO::getUserId, reqVO.getUserId())
                .eqIfPresent(BloodRoadDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BloodRoadDO::getStopRemark, reqVO.getStopRemark())
                .eqIfPresent(BloodRoadDO::getUseDay, reqVO.getUseDay())
                .eqIfPresent(BloodRoadDO::getRemark, reqVO.getRemark())
                .orderByDesc(BloodRoadDO::getId));
    }

    default List<BloodRoadDO> selectList(BloodRoadCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BloodRoadDO>()
                .eqIfPresent(BloodRoadDO::getPatientId, reqVO.getPatientId())
                .likeIfPresent(BloodRoadDO::getPatientName, reqVO.getPatientName())
                .likeIfPresent(BloodRoadDO::getPatientNickName, reqVO.getPatientNickName())
                .eqIfPresent(BloodRoadDO::getDialyzeNo, reqVO.getDialyzeNo())
                .eqIfPresent(BloodRoadDO::getPart, reqVO.getPart())
                .eqIfPresent(BloodRoadDO::getType, reqVO.getType())
                .eqIfPresent(BloodRoadDO::getAccessRoad, reqVO.getAccessRoad())
                .eqIfPresent(BloodRoadDO::getRecipe, reqVO.getRecipe())
                .eqIfPresent(BloodRoadDO::getUserId, reqVO.getUserId())
                .eqIfPresent(BloodRoadDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BloodRoadDO::getStopRemark, reqVO.getStopRemark())
                .eqIfPresent(BloodRoadDO::getUseDay, reqVO.getUseDay())
                .eqIfPresent(BloodRoadDO::getRemark, reqVO.getRemark())
                .inIfPresent(BloodRoadDO::getType, reqVO.getTypeList())
                .orderByDesc(BloodRoadDO::getInitTime));
    }

    Long getInternalFistulaSurvivalRate(@Param("req") VascularAccessParams vascularAccessParams);
    Page<Map<String, Object>> getVascularAccessPage(@Param("page") Page page, @Param("req") VascularAccessParams vascularAccessParams);

    List<Map<String, Object>> getBloodChannelTypeStatistics(@Param("req") VascularAccessParams vascularAccessParams);

    List<Map<String, Object>> getChannelTypeStatistics(@Param("req") VascularAccessParams vascularAccessParams);

    List<BloodPassageComplicationRespVO> getList(String format);

    List<RepairRegisterRespVO> getRepairRegister(String format);

    Page<Map<String, Object>> getVascularAccessPage1(@Param("page") Page page, @Param("req") VascularAccessParams vascularAccessParams);

    List<Map<String, Object>> getVascularAccessPage2(@Param("req") VascularAccessParams vascularAccessParams);
}
