package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/3 14:45
 * @description
 */
@Data
public class DialysisCollectRespVO extends DialysisDrugRespVO{
    /**
     * 透析处方json
     */
    private String prescription;
    /**
     * 透前评估json
     */
    private String beforeEvaluate;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;
    /**
     * 甲
     */
    private String potassium;
    /**
     * 钙
     */
    private String calcium;
    /**换药包(套)/穿刺包(个)*/
    /**穿刺针*/
    /**抗凝剂(首剂)(维持/追加)(总量)*/
    /**封管液(份)*/
}
