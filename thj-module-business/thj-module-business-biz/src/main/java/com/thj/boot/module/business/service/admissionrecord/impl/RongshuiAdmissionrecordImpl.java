package com.thj.boot.module.business.service.admissionrecord.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.enums.CourseRecordEnum;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.dal.datado.PhysicalExamRecord;
import com.thj.boot.module.business.dal.datado.admissionrecord.AdmissionRecord;
import com.thj.boot.module.business.dal.mapper.PhysicalExamRecordMapper;
import com.thj.boot.module.business.dal.mapper.QichunAdmissionRecordMapper;
import com.thj.boot.module.business.dal.mapper.admissionrecord.AdmissionRecordMapper;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordFactory;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordTemple;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Component
public class RongshuiAdmissionrecordImpl extends CourseRecordTemple {
    @Resource
    AdmissionRecordMapper admissionRecordMapper;
    @Resource
    PhysicalExamRecordMapper physicalExamRecordMapper;

    @Resource
    HttpServletRequest httpServletRequest;
    @Resource
    QichunAdmissionRecordMapper qichunAdmissionRecordMapper;
    @Override
    public AdmissionRecordVo getList(Long patientId) {
        AdmissionRecord admissionRecord = admissionRecordMapper.selectOne(
                new LambdaQueryWrapper<AdmissionRecord>()
                        .eq(AdmissionRecord::getPatientId,patientId)
                        .last("limit 1"));
        PhysicalExamRecord physicalExamRecord = physicalExamRecordMapper
                .selectOne(new LambdaQueryWrapper<PhysicalExamRecord>()
                        .eq(PhysicalExamRecord::getPatientId,patientId)
                        .last("limit 1"));

        AdmissionRecordVo admissionRecordVo = new AdmissionRecordVo();
        if(admissionRecord != null){
            BeanUtils.copyProperties(admissionRecord,admissionRecordVo);
        }
        if(physicalExamRecord != null){
            BeanUtils.copyProperties(physicalExamRecord,admissionRecordVo);
        }

        return admissionRecordVo;
    }

    @Override
    public boolean saveOrUpdate(AdmissionRecordVo admissionRecordVo) {
        admissionRecordVo.setId(null);
        AdmissionRecord admissionRecord = new AdmissionRecord();
        PhysicalExamRecord physicalExamRecord = new PhysicalExamRecord();

        BeanUtils.copyProperties(admissionRecordVo,admissionRecord);
        BeanUtils.copyProperties(admissionRecordVo,physicalExamRecord);

        int i = admissionRecordMapper.update(admissionRecord,
                new LambdaQueryWrapper<AdmissionRecord>().eq(AdmissionRecord::getPatientId,admissionRecord.getPatientId()));
        int j = physicalExamRecordMapper.update(physicalExamRecord,new LambdaQueryWrapper<PhysicalExamRecord>()
                .eq(PhysicalExamRecord::getPatientId,physicalExamRecord.getPatientId()));
//        int k = courseOfDiseaseRecordMapper.update(courseOfDiseaseRecord,new LambdaQueryWrapper<CourseOfDiseaseRecord>()
//                .eq(CourseOfDiseaseRecord::getPatientId,courseOfDiseaseRecord.getPatientId()));
        if(i == 0){
            admissionRecordMapper.insert(admissionRecord);
        }
        if(j == 0){
            physicalExamRecordMapper.insert(physicalExamRecord);
        }
//        if(k == 0){
//            courseOfDiseaseRecordMapper.insert(courseOfDiseaseRecord);
//        }
//        String deptId = httpServletRequest.getHeader("systemdeptid") != null ? httpServletRequest.getHeader("systemdeptid").toString() : null;
//        if(DeptEnum.QICHUN.getDeptId().equals(deptId)) {
//            QichunAdmissionRecord qichunAdmissionRecord = new QichunAdmissionRecord();
//            BeanUtils.copyProperties(admissionRecordVo,qichunAdmissionRecord);
//            int h = qichunAdmissionRecordMapper.update(qichunAdmissionRecord,new LambdaQueryWrapper<QichunAdmissionRecord>()
//                    .eq(QichunAdmissionRecord::getPatientId,qichunAdmissionRecord.getPatientId()));
//            if(h == 0){
//                qichunAdmissionRecordMapper.insert(qichunAdmissionRecord);
//            }
//        }
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CourseRecordFactory.map.put(CourseRecordEnum.RONGSHUIADMISSIONRECORD.getName(),this);
    }
}
