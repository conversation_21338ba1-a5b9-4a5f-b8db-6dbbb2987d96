package com.thj.boot.module.business.controller.admin.medicaldischargesummary;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.dal.datado.MedicalDischargeSummary;
import com.thj.boot.module.business.service.admissionrecord.AdmissionRecordService;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordFactory;
import com.thj.boot.module.business.service.medicaldischargesummary.MedicalDischargeSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.thj.boot.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/business/medicalDischargeSummary")
@Validated
public class MedicalDischargeSummaryController {
    @Autowired
    MedicalDischargeSummaryService medicalDischargeSummaryService;
    @GetMapping("/getById")
    public CommonResult<MedicalDischargeSummary> medicalDischargeSummaryGetById(@RequestParam("id") Long patientId) {
        return success(medicalDischargeSummaryService.medicalDischargeSummaryGetById(patientId));
    }
    @PostMapping("/saveOrUpdateById")
    public CommonResult<Boolean> saveOrUpdateById(@RequestBody MedicalDischargeSummary medicalDischargeSummary) {
        return success(medicalDischargeSummaryService.saveOrUpdateById(medicalDischargeSummary));
    }
}
