package com.thj.boot.module.business.service.courserecord;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordBatch;
import com.thj.boot.module.business.dal.datado.courserecord.CourseRecordDO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordPageReqVO;
import com.thj.boot.module.business.pojo.courserecord.vo.CourseRecordUpdateReqVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 病程记录 Service 接口
 *
 * <AUTHOR>
 */
public interface CourseRecordService {

    /**
     * 创建病程记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCourseRecord( CourseRecordCreateReqVO createReqVO);

    /**
     * 更新病程记录
     *
     * @param updateReqVO 更新信息
     */
    void updateCourseRecord( CourseRecordUpdateReqVO updateReqVO);

    /**
     * 删除病程记录
     *
     * @param id 编号
     */
    void deleteCourseRecord(Long id);

    /**
     * 获得病程记录
     *
     * @param id 编号
     * @return 病程记录
     */
    CourseRecordDO getCourseRecord(Long id);

    /**
     * 获得病程记录列表
     *
     * @param ids 编号
     * @return 病程记录列表
     */
    List<CourseRecordDO> getCourseRecordList(Collection<Long> ids);

    /**
     * 获得病程记录分页
     *
     * @param pageReqVO 分页查询
     * @return 病程记录分页
     */
    PageResult<CourseRecordDO> getCourseRecordPage(CourseRecordPageReqVO pageReqVO);

    /**
     * 获得病程记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 病程记录列表
     */
    List<CourseRecordDO> getCourseRecordList(CourseRecordCreateReqVO createReqVO);

    void generateSummary(CourseRecordCreateReqVO createReqVO,HttpServletResponse response);

    void getResult();

    void updateSourceCourseRecord();

    void updateBatch(CourseRecordBatch courseRecordBatch);
}
