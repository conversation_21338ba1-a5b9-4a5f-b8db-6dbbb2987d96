package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/28 18:20
 * @description
 */
@Data
public class BloodPassageComplicationRespVO implements Serializable {
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 通路类型(字典获取)
     */
    private String type;
    /**
     * 项目
     */
    private String project;
    /**
     * 结果
     */
    private String result;
    /**
     * 处理
     */
    private String dispose;
    /**
     * 并发症(字典获取)
     */
    private String occurType;
    /**
     * 症状
     */
    private String symptom;
    /**
     * 处理
     */
    private String description;
}
