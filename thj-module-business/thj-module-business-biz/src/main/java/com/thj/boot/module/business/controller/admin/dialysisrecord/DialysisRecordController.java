package com.thj.boot.module.business.controller.admin.dialysisrecord;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.common.utils.EasyExcelUtils;
import com.thj.boot.module.business.controller.admin.dialysisrecord.vo.DialysisRecordExcelVO;
import com.thj.boot.module.business.convert.dialysisrecord.DialysisRecordConvert;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeRecordDO;
import com.thj.boot.module.business.dal.datado.dialysisrecord.DialysisRecordDO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordPageReqVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordRespVO;
import com.thj.boot.module.business.pojo.dialysisrecord.vo.DialysisRecordUpdateReqVO;
import com.thj.boot.module.business.service.dialysisrecord.DialysisRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 透析记录
 */
@RestController
@RequestMapping("/business/dialysis-record")
@Validated
public class DialysisRecordController {

    @Resource
    private DialysisRecordService dialysisRecordService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createDialysisRecord(@RequestBody DialysisRecordCreateReqVO createReqVO) {
        return success(dialysisRecordService.createDialysisRecord(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateDialysisRecord(@RequestBody DialysisRecordUpdateReqVO updateReqVO) {
        dialysisRecordService.updateDialysisRecord(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteDialysisRecord(@RequestParam("id") Long id) {
        dialysisRecordService.deleteDialysisRecord(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<DialysisRecordRespVO> getDialysisRecord(@RequestParam("id") Long id) {
        DialysisRecordDO dialysisRecord = dialysisRecordService.getDialysisRecord(id);
        return success(DialysisRecordConvert.INSTANCE.convert(dialysisRecord));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<DialysisRecordRespVO>> getDialysisRecordList(DialysisRecordCreateReqVO createReqVO) {
        List<DialysisRecordDO> list = dialysisRecordService.getDialysisRecordList(createReqVO);
        return success(DialysisRecordConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<DialysisRecordRespVO>> getDialysisRecordPage(@RequestBody DialysisRecordPageReqVO pageVO) {
        PageResult<DialysisRecordDO> pageResult = dialysisRecordService.getDialysisRecordPage(pageVO);
        return success(DialysisRecordConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 分页
     */
    @PostMapping("/getArrangeRecordList")
    public CommonResult<PageResult<ArrangeRecordDO>> getArrangeRecordList(@RequestBody DialysisRecordPageReqVO pageVO) {
        PageResult<ArrangeRecordDO> pageResult = dialysisRecordService.getDialysisLogList(pageVO);
        return success(pageResult);
    }

    /**
     * 指标曲线
     */
    @PostMapping("/indexCurve")
    public CommonResult<Map<String, Object>> getIndexCurve(@RequestBody DialysisRecordCreateReqVO createReqVO) {
        return success(dialysisRecordService.getIndexCurve(createReqVO));
    }

    /**
     * 导出
     */
    @PostMapping("/export-excel")
    public void exportDevManExcel(@RequestBody DialysisRecordPageReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        PageResult<DialysisRecordDO> pageResult = dialysisRecordService.getDialysisRecordPage(exportReqVO);
        PageResult<DialysisRecordExcelVO> dialysisRecordRespVOPageResult = DialysisRecordConvert.INSTANCE.convertPage2(pageResult);
        if (CollectionUtil.isNotEmpty(dialysisRecordRespVOPageResult.getList())) {
            List<DialysisRecordExcelVO> collect = dialysisRecordRespVOPageResult.getList().stream().peek(dialysisRecordExcelVO -> {
                StringBuilder sb = new StringBuilder();
                sb.append(StrUtil.isNotEmpty(dialysisRecordExcelVO.getBeforeBpNoOne()) ? dialysisRecordExcelVO.getBeforeBpNoOne() + "/" : "")
                        .append(StrUtil.isNotEmpty(dialysisRecordExcelVO.getBeforeBpNoTwo()) ? dialysisRecordExcelVO.getBeforeBpNoTwo() : "");
                dialysisRecordExcelVO.setBeforeBpNo(sb.toString());
            }).collect(Collectors.toList());
            dialysisRecordRespVOPageResult.setList(collect);
        }
        EasyExcelUtils.write(response, "患者透析记录.xls", "患者透析记录", DialysisRecordExcelVO.class, dialysisRecordRespVOPageResult.getList());
    }


}
