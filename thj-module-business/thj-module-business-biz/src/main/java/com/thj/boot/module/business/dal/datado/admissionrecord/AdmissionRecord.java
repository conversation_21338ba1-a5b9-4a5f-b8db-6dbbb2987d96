package com.thj.boot.module.business.dal.datado.admissionrecord;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 入院记录表
 * @TableName admission_record
 */
@TableName(value ="admission_record")
@Data
public class AdmissionRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 病人id
     */
    private Long patientId;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 出生地
     */
    private String birthPlace;

    /**
     * 病史陈述者
     */
    private String historyCollector;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 婚育史
     */
    private String marriageHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 血尿常规
     */
    private String bloodRoutine;

    /**
     * 血液生化
     */
    private String bloodBiochemical;

    /**
     * B超·X线及其他特殊检查
     */
    private String specialExam;

    /**
     * 入院时病例分型
     */
    private String admissionDiagnosis;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    private Long deptId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime diagnosisDate;
    private String chiefComplaint;
    private String presentIllness;//（现病史）、
    private String ecg;//（心电图）、
    private String preliminaryDiagnosis;//（初步诊断）、
    private String attendingPhysician;
    /**
     * 入院日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime admissionDate;
}