package com.thj.boot.module.business.controller.admin.facilitygroup;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.facilitygroup.FacilityGroupConvert;
import com.thj.boot.module.business.dal.datado.facilitygroup.FacilityGroupDO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupCreateReqVO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupPageReqVO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupRespVO;
import com.thj.boot.module.business.pojo.facilitygroup.vo.FacilityGroupUpdateReqVO;
import com.thj.boot.module.business.service.facilitygroup.FacilityGroupService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;


@RestController
@RequestMapping("/business/facility-group")
@Validated
public class FacilityGroupController {

    @Resource
    private FacilityGroupService facilityGroupService;

    @PostMapping("/create")
    public CommonResult<Long> createFacilityGroup(@RequestBody FacilityGroupCreateReqVO createReqVO) {
        return success(facilityGroupService.createFacilityGroup(createReqVO));
    }

    @PostMapping("/update")
    public CommonResult<Boolean> updateFacilityGroup(@RequestBody FacilityGroupUpdateReqVO updateReqVO) {
        facilityGroupService.updateFacilityGroup(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    public CommonResult<Boolean> deleteFacilityGroup(@RequestParam("id") Long id) {
        facilityGroupService.deleteFacilityGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    public CommonResult<FacilityGroupRespVO> getFacilityGroup(@RequestParam("id") Long id) {
        FacilityGroupDO facilityGroup = facilityGroupService.getFacilityGroup(id);
        return success(FacilityGroupConvert.INSTANCE.convert(facilityGroup));
    }

    @GetMapping("/list")
    public CommonResult<List<FacilityGroupRespVO>> getFacilityGroupList(FacilityGroupCreateReqVO createReqVO) {
        List<FacilityGroupDO> list = facilityGroupService.getFacilityGroupList(createReqVO);
        return success(FacilityGroupConvert.INSTANCE.convertList(list));
    }

    @PostMapping("/page")
    public CommonResult<PageResult<FacilityGroupRespVO>> getFacilityGroupPage(@RequestBody FacilityGroupPageReqVO pageVO) {
        return success(facilityGroupService.getFacilityGroupPage(pageVO));
    }


}
