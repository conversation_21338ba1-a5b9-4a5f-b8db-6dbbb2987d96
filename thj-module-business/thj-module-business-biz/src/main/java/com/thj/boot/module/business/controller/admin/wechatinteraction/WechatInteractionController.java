package com.thj.boot.module.business.controller.admin.wechatinteraction;

import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.wechatinteraction.vo.PatientSendMsgReqVO;
import com.thj.boot.module.business.pojo.wechatinteraction.vo.WechatInteractionPageReqVO;
import com.thj.boot.module.business.pojo.wechatinteraction.vo.WechatInteractionRespVO;
import com.thj.boot.module.business.service.wechatinteraction.WechatInteractionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/1/25 16:29
 * @description
 */

/**
 * 微信互动
 */
@RestController
@Slf4j
@RequestMapping("/business/wechat")
public class WechatInteractionController {

    @Resource
    private WechatInteractionService wechatInteractionService;

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<WechatInteractionRespVO>> getWechatInteractionPage(@RequestBody WechatInteractionPageReqVO pageVO) {
        return success(wechatInteractionService.getWechatInteractionPage(pageVO));
    }

    /**
     * 推送消息
     */
    @PostMapping("/create")
    public CommonResult<Boolean> sendMsg(@RequestBody PatientSendMsgReqVO patientSendMsgReqVO) {
        return success(wechatInteractionService.sendMsg(patientSendMsgReqVO));
    }
}
