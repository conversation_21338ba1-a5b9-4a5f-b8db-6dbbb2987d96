package com.thj.boot.module.business.service.medicaldischargesummary.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.thj.boot.module.business.dal.datado.CourseOfDiseaseRecord;
import com.thj.boot.module.business.dal.datado.PhysicalExamRecord;
import com.thj.boot.module.business.dal.datado.admissionrecord.AdmissionRecord;
import com.thj.boot.module.business.dal.mapper.MedicalDischargeSummaryMapper;
import com.thj.boot.module.business.dal.mapper.admissionrecord.AdmissionRecordMapper;
import com.thj.boot.module.business.service.medicaldischargesummary.MedicalDischargeSummaryService;
import com.thj.boot.module.business.dal.datado.MedicalDischargeSummary;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【medical_discharge_summary(出院小结表)】的数据库操作Service实现
* @createDate 2025-07-29 11:19:59
*/
@Service
public class MedicalDischargeSummaryServiceImpl extends ServiceImpl<MedicalDischargeSummaryMapper, MedicalDischargeSummary>
    implements MedicalDischargeSummaryService {
    @Autowired
    MedicalDischargeSummaryMapper medicalDischargeSummaryMapper;
    @Autowired
    AdmissionRecordMapper admissionRecordMapper;

    @Override
    public MedicalDischargeSummary medicalDischargeSummaryGetById(Long patientId) {
        MedicalDischargeSummary medicalDischargeSummary
                = medicalDischargeSummaryMapper
                .selectOne(new LambdaQueryWrapper<MedicalDischargeSummary>()
                        .eq(MedicalDischargeSummary::getPatientId,patientId)
                        );

        if(medicalDischargeSummary == null){
            AdmissionRecord admissionRecord = admissionRecordMapper.selectOne(new LambdaQueryWrapper<AdmissionRecord>()
                    .eq(AdmissionRecord::getPatientId,patientId)
                    );
            medicalDischargeSummary = new MedicalDischargeSummary();
            medicalDischargeSummary.setAdmissionDate(admissionRecord != null ? admissionRecord.getAdmissionDate() : null);
        }
        return medicalDischargeSummary;
    }

    @Override
    public Boolean saveOrUpdateById(MedicalDischargeSummary medicalDischargeSummary) {

        int k = medicalDischargeSummaryMapper.update(medicalDischargeSummary,new LambdaQueryWrapper<MedicalDischargeSummary>()
                .eq(MedicalDischargeSummary::getPatientId,medicalDischargeSummary.getPatientId()));

        if(k == 0){
            medicalDischargeSummaryMapper.insert(medicalDischargeSummary);
        }
        return true;
    }
}




