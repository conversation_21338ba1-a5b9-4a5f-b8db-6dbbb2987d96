package com.thj.boot.module.business.service.hemodialysismanager;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.gk.vo.RecentlyDialysisListVo;
import com.thj.boot.module.business.controller.admin.hemodialysismanager.vo.*;
import com.thj.boot.module.business.dal.datado.arrangeclasses.ArrangeClassesDO;
import com.thj.boot.module.business.dal.datado.hemodialysismanager.HemodialysisManagerDO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesCreateReqVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesLastVo;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceRespVO;
import com.thj.boot.module.business.pojo.facilitymanager.vo.FacilityManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerCreateReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerPageReqVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerUpdateReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientPageReqVO;
import com.thj.boot.module.business.pojo.patient.vo.PatientRespVO;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 血液透析管理 Service 接口
 *
 * <AUTHOR>
 */
public interface HemodialysisManagerService {

    /**
     * 创建血液透析管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createHemodialysisManager(@Valid HemodialysisManagerCreateReqVO createReqVO, HttpServletRequest request);

    /**
     * 更新血液透析管理
     *
     * @param updateReqVO 更新信息
     */
    void updateHemodialysisManager(@Valid HemodialysisManagerUpdateReqVO updateReqVO);

    /**
     * 删除血液透析管理
     *
     * @param id 编号
     */
    void deleteHemodialysisManager(Long id);

    /**
     * 获得血液透析管理
     *
     * @param id 编号
     * @return 血液透析管理
     */
    HemodialysisManagerRespVO getHemodialysisManager(HemodialysisManagerCreateReqVO createReqVO);

    /**
     * 获得血液透析管理列表
     *
     * @param ids 编号
     * @return 血液透析管理列表
     */
    /*List<HemodialysisManagerDO> getHemodialysisManagerList(Collection<Long> ids);*/

    /**
     * 获得血液透析管理分页
     *
     * @param pageReqVO 分页查询
     * @return 血液透析管理分页
     */
    PageResult<HemodialysisManagerDO> getHemodialysisManagerPage(HemodialysisManagerPageReqVO pageReqVO);

    /**
     * 获得血液透析管理列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 血液透析管理列表
     */
    List<HemodialysisManagerDO> getHemodialysisManagerList(HemodialysisManagerCreateReqVO createReqVO);


    /****
     * <AUTHOR>
     * @date 2024/3/11 11:31
     * @Description 开始透析
     **/
    Long startDialysis(HemodialysisManagerCreateReqVO createReqVO,HttpServletRequest request);

    /***
     * <AUTHOR>
     * @date 2024/3/13 16:14
     * @Description 结束透析
     **/
    Long endDialysis(HemodialysisManagerCreateReqVO createReqVO,HttpServletRequest request);

    /***
     * <AUTHOR>
     * @date 2024/3/13 17:23
     * @Description 归档
     **/
    void pigeonholeDialysis(HemodialysisManagerCreateReqVO createReqVO);

    /**
     * 透析准备
     */
    List<HemodialysisManagerRespVO> consumablePage(HemodialysisManagerRespVO pageVO);

    /**
     * 结束透析，透析器使用列表
     */
    List<HemodialysisManagerRespVO> useInstrumentInfo(HemodialysisManagerCreateReqVO createReqVO);

    /****
     * <AUTHOR>
     * @date 2024/3/20 10:37
     * @Description 透析单打印详情
     **/
    List<HemodialysisManagerPrintRespVO> getPrintInfo(HemodialysisManagerPrintRespVO printRespVO);

    /****
     * <AUTHOR>
     * @date 2024/3/21 14:16
     * @Description 通过透析时长计算结束透析时间
     **/
    HemodialysisManagerRespVO getDailysisTime(HemodialysisManagerCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date 2024/3/21 19:07
     * @Description 用药推送
     **/
    List<DialysisAdviceRespVO> medicatePush(HemodialysisManagerCreateReqVO createReqVO);

    List<RecentlyDialysisListVo> getRecentlyDialysisList(String patientId);

    /***
     * <AUTHOR>
     * @date 2024/3/29 14:10
     * @Description 开始透析获取上次穿刺方式/型号/方向信息
     **/
    HemodialysisManagerRespVO getLastHealInfo(HemodialysisManagerCreateReqVO createReqVO);

    List<ArrangeClassesDO> getLastRecord(ArrangeClassesLastVo vo);

    /***
     * <AUTHOR>
     * @date 2024/4/3 10:31
     * @Description 透析准备
     **/
    PageResult<DialysisPreparationRespVO> getDialysisPreparation(HemodialysisManagerPageReqVO pageReqVO);

    /***
     * <AUTHOR>
     * @date 2024/4/9 9:43
     * @Description 透析单打印
     **/
    List<HemodialysisManagerPrintDialyzeRespVO> getPrintDialyze(HemodialysisManagerCreateReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2024/4/12 11:41
     * @Description 修改干体重信息
     **/
    void updateDryWeight(HemodialysisReachReqVO createReqVO);

    /***
     * <AUTHOR>
     * @date  2024/6/26 11:01
     * @Description 统计
     **/
    List<DialysisPreparationRespVO> getPreparationStatistics(HemodialysisManagerPageReqVO pageReqVO);

    Map<String, Object> getHemodialysisManagerLast(HemodialysisManagerCreateReqVO createReqVO);


    List<HemodialysisSiteRecordRespVO> getSiteRecordList(Long patientId, Integer size);

    /**
     * 获取患者治疗记录详情
     *
     * @param pageReqVO 分页查询
     * @return 获取患者治疗记录详情
     */
    PageResult<HemodialysisManagerRespVO> getPatientTreatRecordDetail(HemodialysisManagerPageReqVO pageReqVO);

    List<HemodialysisManagerPrintDialyzeRespVO> getPrintDialyzeBatch(HemodialysisManagerCreateReqVO createReqVO);

    HemodialysisManagerDO getHemodialysisManagerGetById(Long patientId);
}
