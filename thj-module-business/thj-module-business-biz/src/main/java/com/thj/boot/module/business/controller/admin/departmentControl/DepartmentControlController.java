package com.thj.boot.module.business.controller.admin.departmentControl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.pojo.departmentControl.BaseChartResp;
import com.thj.boot.module.business.pojo.departmentControl.PatientDetailResp;
import com.thj.boot.module.business.pojo.departmentControl.TableChartResp;
import com.thj.boot.module.business.service.departmentControl.DepartmentControlService;
import com.thj.boot.module.business.service.departmentControl.context.StatisticsContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 科室质控
 */

@Validated
@RestController
@RequestMapping("/business/depart-control")
public class DepartmentControlController {


    @Resource
    private DepartmentControlService departmentControlService;


    @PostMapping("/statistics")
    public CommonResult<BaseChartResp<Object>> statistics(@RequestBody Map<String, Object> pageParams, @RequestHeader(required = false) Long SystemDeptId) {
        if (pageParams.get("moduleType") == null || pageParams.get("chartType") == null)
            return CommonResult.error(400, "参数不能为空");
//        pageParams.put("deptId", SystemDeptId);
        return CommonResult.success(StatisticsContext.getContextByType(pageParams));
    }


    @PostMapping("/get-detail")
    public CommonResult<PageResult<Object>> getDetail(@RequestBody Map<String, Object> pageParams, @RequestHeader(required = false) Long SystemDeptId) {
        if (pageParams.get("moduleType") == null) return CommonResult.error(400, "参数不能为空");
//        pageParams.put("deptId", SystemDeptId);
        return CommonResult.success(StatisticsContext.pageResult(pageParams));
    }

    @PostMapping("/get-total-detail")
    public CommonResult<BaseChartResp<Object>> getTotalDetail(@RequestBody Map<String, Object> pageParams, @RequestHeader(required = false) Long SystemDeptId) {
        if (pageParams.get("moduleType") == null) return CommonResult.error(400, "参数不能为空");
//        pageParams.put("deptId", SystemDeptId);
        return CommonResult.success(StatisticsContext.getContextByType(pageParams));
    }

    /**
     * 导出 透析总量汇总详情
     * @param pageParams
     * @return
     */
    @PostMapping("/total-detail/export")
    public void TotalDetailExport(@RequestBody Map<String, Object> pageParams, HttpServletResponse response) throws IOException {
        if (pageParams.get("moduleType") == null) throw new RuntimeException("参数不能为空");;
        TableChartResp contextByType = (TableChartResp)StatisticsContext.getContextByType(pageParams);
        // 自定义表头
        List<String> head = new ArrayList<>(Arrays.asList("序号", "患者姓名", "透析号", "性别"));
        // 获取表头
        List<TableChartResp.TableColumn> tableColumnList = contextByType.getTableColumnList();
        // 获取数据
        List<Map<String, Object>> values = contextByType.getValues();


        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;");

        // 封装excel表头
        List<String> collect = tableColumnList.stream()
                .map(TableChartResp.TableColumn::getLabel)
                .collect(Collectors.toList());
        head.addAll(collect);
        List<List<String>> header = head.stream().map(Collections::singletonList).collect(Collectors.toList());

        // 封装excel数据
        AtomicInteger index = new AtomicInteger();
        List<List<Object>> data = new ArrayList<>();
        values.forEach(map -> {
            List<Object> list = new ArrayList<>();
            list.add(index.getAndIncrement());
            list.add(map.get("patientName"));
            list.add(map.get("dialyzeNo"));
            list.add(map.get("sex"));
            list.addAll(tableColumnList.stream().map(TableChartResp.TableColumn::getLabel).map(item -> {
                if ("总数".equals(item)) {
                    return Integer.parseInt(map.get("total").toString());
                }
                return map.get(item);
            }).collect(Collectors.toList()));
            data.add(list);
        });
        EasyExcel.write(response.getOutputStream())
                //设置默认样式及写入头信息开始的行数
                .useDefaultStyle(true).relativeHeadRowIndex(0)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(10))
                .sheet("透析总量汇总详情表")
                .head(header)
                .doWrite(data);
    }

    @PostMapping("/export-excel")
    public void exportExcel(@RequestBody Map<String, Object> pageParams, @RequestHeader(required = false) Long SystemDeptId, HttpServletResponse response) {
        pageParams.put("deptId", SystemDeptId);
        StatisticsContext.exportExcel(pageParams, response);
    }

    @PostMapping("/export-statistics")
    public void exportStatisticsExcel(@RequestBody Map<String, Object> pageParams, @RequestHeader(required = false) Long SystemDeptId, HttpServletResponse response) throws IOException {
        pageParams.put("deptId", SystemDeptId);
        StatisticsContext.exportStatisticsExcel(pageParams, response);
    }


    /**
     * 患者详情
     *
     * @param patientId
     * @return
     */
    @GetMapping("/info")
    public CommonResult<PatientDetailResp> getInfo(@RequestParam(value = "patientId") Long patientId) {
        if (patientId == null) return CommonResult.error(400, "患者id不能为空");
        return CommonResult.success(departmentControlService.getInfo(patientId));
    }
}
