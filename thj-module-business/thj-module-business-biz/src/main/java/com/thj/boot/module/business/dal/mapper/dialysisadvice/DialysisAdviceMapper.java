package com.thj.boot.module.business.dal.mapper.dialysisadvice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdviceCreateReqVO;
import com.thj.boot.module.business.pojo.dialysisadvice.vo.DialysisAdvicePageReqVO;
import com.thj.starter.mybatis.mapper.BaseMapperX;
import com.thj.starter.mybatis.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 透析管理-医嘱 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DialysisAdviceMapper extends BaseMapperX<DialysisAdviceDO> {

    default PageResult<DialysisAdviceDO> selectPage(DialysisAdvicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DialysisAdviceDO>()
                .eqIfPresent(DialysisAdviceDO::getWeekDay, reqVO.getWeekDay())
                .eqIfPresent(DialysisAdviceDO::getAdviceState, reqVO.getAdviceState())
                .inIfPresent(DialysisAdviceDO::getFacilitySubareaId, reqVO.getFacilitySubareaId())
                .inIfPresent(DialysisAdviceDO::getPatientId, reqVO.getPatientIdList())
                .eqIfPresent(DialysisAdviceDO::getType, reqVO.getType())
                .eqIfPresent(DialysisAdviceDO::getPatientSource, reqVO.getPatientSource())
                .eqIfPresent(DialysisAdviceDO::getAdviceUser, reqVO.getAdviceUser())
                .likeIfPresent(DialysisAdviceDO::getAdviceName, reqVO.getKeyword())
                .likeIfPresent(DialysisAdviceDO::getAdviceName,reqVO.getAdviceName())
                .eqIfPresent(DialysisAdviceDO::getAdviceDesprition, reqVO.getAdviceDesprition())
                .eqIfPresent(DialysisAdviceDO::getOneNo, reqVO.getOneNo())
                .eqIfPresent(DialysisAdviceDO::getPrescribeNo, reqVO.getPrescribeNo())
                .eqIfPresent(DialysisAdviceDO::getFrequency, reqVO.getFrequency())
                .likeIfPresent(DialysisAdviceDO::getChildName, reqVO.getChildName())
                .eqIfPresent(DialysisAdviceDO::getActivateUser, reqVO.getActivateUser())
                .eqIfPresent(DialysisAdviceDO::getCheckUser, reqVO.getCheckUser())
                .eqIfPresent(DialysisAdviceDO::getRemark, reqVO.getRemark())
                .eq(DialysisAdviceDO::getDeleted,false)
                .eqIfPresent(DialysisAdviceDO::getPatientId, reqVO.getPatientId())
                .eqIfPresent(DialysisAdviceDO::getStopStatus, reqVO.getStopStatus())
                .betweenIfPresent(DialysisAdviceDO::getStartTime, reqVO.getStartTime() != null ? DateUtil.beginOfDay(reqVO.getStartTime()) : reqVO.getStartTime(), reqVO.getEndTime() != null ? DateUtil.endOfDay(reqVO.getEndTime()) : reqVO.getEndTime())
                .isNull("0".equals(reqVO.getCheck()), DialysisAdviceDO::getCheckUser)
                .isNotNull("1".equals(reqVO.getCheck()), DialysisAdviceDO::getCheckUser)
                .isNotNull(DialysisAdviceDO::getDateWeek)
                .and(i -> i
                .isNull(DialysisAdviceDO::getDrugType)
                .or()
                .ne(DialysisAdviceDO::getDrugType,2))
                .and(StrUtil.isNotEmpty(reqVO.getMore()), i -> i
                        .like(DialysisAdviceDO::getPatientName, reqVO.getMore())
                        .or()
                        .like(DialysisAdviceDO::getSpellName, reqVO.getMore())
                        .or()
                        .like(DialysisAdviceDO::getDialyzeNo, reqVO.getMore()))
                .in(!CollectionUtils.isEmpty(reqVO.getDrugWay()),DialysisAdviceDO::getDrugWay, reqVO.getDrugWay())
                .orderByDesc(StrUtil.isNotEmpty(reqVO.getSort()) && "1".equals(reqVO.getSort()) ,DialysisAdviceDO::getCreateTime)
                .orderByAsc(StrUtil.isNotEmpty(reqVO.getSort()) && "0".equals(reqVO.getSort()), DialysisAdviceDO::getFacilityId));
    }

    default List<DialysisAdviceDO> selectList(DialysisAdviceCreateReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DialysisAdviceDO>()
                .eqIfPresent(DialysisAdviceDO::getType, reqVO.getType())
                .eqIfPresent(DialysisAdviceDO::getAdviceUser, reqVO.getAdviceUser())
                .likeIfPresent(DialysisAdviceDO::getAdviceName, reqVO.getAdviceName())
                .eqIfPresent(DialysisAdviceDO::getAdviceDesprition, reqVO.getAdviceDesprition())
                .eqIfPresent(DialysisAdviceDO::getOneNo, reqVO.getOneNo())
                .eqIfPresent(DialysisAdviceDO::getPrescribeNo, reqVO.getPrescribeNo())
                .eqIfPresent(DialysisAdviceDO::getDrugWay, reqVO.getDrugWay())
                .eqIfPresent(DialysisAdviceDO::getFrequency, reqVO.getFrequency())
                .likeIfPresent(DialysisAdviceDO::getChildName, reqVO.getChildName())
                .eqIfPresent(DialysisAdviceDO::getActivateUser, reqVO.getActivateUser())
                .eqIfPresent(DialysisAdviceDO::getCheckUser, reqVO.getCheckUser())
                .eqIfPresent(DialysisAdviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DialysisAdviceDO::getPatientId, reqVO.getPatientId())
                .eqIfPresent(DialysisAdviceDO::getType, reqVO.getType())
                //.eqIfPresent(DialysisAdviceDO::getStopStatus,reqVO.getStopStatus())
                .betweenIfPresent(DialysisAdviceDO::getDateWeek, reqVO.getDateWeek() != null ? DateUtil.beginOfDay(reqVO.getDateWeek()) : reqVO.getDateWeek(), reqVO.getDateWeek() != null ? DateUtil.endOfDay(reqVO.getDateWeek()) : reqVO.getDateWeek())
                .orderByAsc(DialysisAdviceDO::getStopStatus)
                .orderByAsc(DialysisAdviceDO::getStartTime));
    }

    void upDpIdNull(@Param("collect") List<Long> collect);
}
