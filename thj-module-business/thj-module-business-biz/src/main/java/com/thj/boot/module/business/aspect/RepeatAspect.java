package com.thj.boot.module.business.aspect;

import com.thj.boot.common.exception.ErrorCode;
import com.thj.boot.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 深圳万狼科技有限公司
 * E-MAIL：<EMAIL>
 * 地址：深圳市宝安区西乡街道铁仔路40号九方广场2栋901
 * 网址：http://www.wanlang.cn/
 * 我们的宗旨是：让每一个网站都产生价值！
 *
 * @Classname SysLogAspect
 * @Description 万狼科技
 * @Version 1.0.0
 * @Date 2024/2/22 17:48
 * @<NAME_EMAIL>
 */
@Aspect
@Component
@Slf4j
public class RepeatAspect {
    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Pointcut("@annotation(com.thj.boot.common.annotation.RepeatSubmit)")
    public void repeatPointCut() {

    }

    /**
     *
     * @param point
     * @return
     * @throws Throwable
     */

    @Around("repeatPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String requestToken = request.getHeader("Authorization");

         // 获取注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        // 获取类，方法
        String className = method.getDeclaringClass().getName();
        String methodName = method.getName();

        // 组装key：用户唯一标识+操作类+方法
        String key = requestToken + "#" + className + "#" + methodName;
        String keyHashCode = String.valueOf(Math.abs(key.hashCode()));
        //log.info("key:{},keyHashcode:{}", key, keyHashCode);


        // 从缓存给中根据key获取数据
        String value = redisTemplate.opsForValue().get(keyHashCode);

        if (value != null) {
            // 如果value不为空; return "请勿重复提交";
            log.info("重复提交-key:{},keyHashcode:{}", key, keyHashCode);
            throw new ServiceException(new ErrorCode(4001, "正在提交中，请勿重复操作！"));

        } else {
            //log.info("首次提交");
            // value为空，则加入缓存，并设置过期过期时间
            redisTemplate.opsForValue().set(keyHashCode, "1", 2000, TimeUnit.MILLISECONDS);
        }

        //执行Object
        Object object = point.proceed();

        return object;
    }

}
