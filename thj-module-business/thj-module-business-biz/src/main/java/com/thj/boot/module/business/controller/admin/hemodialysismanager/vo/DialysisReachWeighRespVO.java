package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/13 9:37
 * @description
 */
@Data
public class DialysisReachWeighRespVO implements Serializable {
    /**
     * 衣物重
     */
    private String clothing;
    /**
     * 透前称重
     */
    private String dialyzeBeforeWeigh;
    /**
     * 透前体重
     */
    private String dialyzeBeforeWeight;
    /**
     * 目标脱水
     */
    private String dehydration;
    /**
     * 脱水百分比
     */
    private String dehydrationPercent;
    /**
     * 摄氏度
     */
    private String degreeCelsius;
    /**
     * p次数
     */
    private String pno;
    /**
     * r次数
     */
    private String rno;
    /**
     * bp
     */
    private String bpNoOne;
    /**
     * bp
     */
    private String bpNoTwo;
    /**
     * 透后称重
     */
    private String dialyzeAfterWeigh;
    /**
     * 体重减少
     */
    private String weights;
    /**
     * 透后体重
     */
    private String dialyzeAfterWeight;
    /**
     * 透后p
     */
    private String afterPNo;
    /**
     * 透后r
     */
    private String afterRNo;
    /**
     * 透后bp
     */
    private String afterBpOne;
    /**
     * 透后bp
     */
    private String afterBpTwo;
    /**
     * 0-未签到，1-已签到
     */
    private String registerState;
    /**
     * 已签到时间
     */
    private Date registerTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 透后摄氏度
     */
    private String afterDegreeCelsius;
    /**
     * 门店id
     */
    private Long deptId;
    /**
     * 班次
     */
    private String dayState;
}
