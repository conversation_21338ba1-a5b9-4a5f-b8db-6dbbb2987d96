package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/2 17:20
 * @description 透析准备-透析耗材
 */
@Data
public class DialysisConsumablesRespVO extends DialyzeArrangeBaseVO {
    /**
     * 透析处方json
     */
    private String prescription;
    /**
     * 治疗小结
     */
    private String recoverSummary;
    /**
     * 透前状态
     */
    private String prescriptionState;
    /**
     * 血透器
     */
    private String hemodialysisDevice;
    /**
     * 血滤器
     */
    private String bloodFilter;
    /**
     * 灌流器
     */
    private String perfumer;
    /**
     * 甲
     */
    private String potassium;
    /**
     * 钙
     */
    private String calcium;
    /**
     * 葡萄糖
     */
    private String glucose;
    /**
     * 穿刺针
     */
    private String PunctureNeedle;
    /**
     * 穿刺针型号
     */
    private String punctureNeedleModel;
}
