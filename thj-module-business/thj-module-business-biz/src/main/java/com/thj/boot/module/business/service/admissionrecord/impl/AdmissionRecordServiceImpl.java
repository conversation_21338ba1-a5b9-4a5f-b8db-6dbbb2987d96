package com.thj.boot.module.business.service.admissionrecord.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.AbstractChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.thj.boot.common.enums.DeptEnum;
import com.thj.boot.module.business.controller.admin.admissionrecord.vo.AdmissionRecordVo;
import com.thj.boot.module.business.dal.datado.CourseOfDiseaseRecord;
import com.thj.boot.module.business.dal.datado.PhysicalExamRecord;
import com.thj.boot.module.business.dal.datado.QichunAdmissionRecord;
import com.thj.boot.module.business.dal.mapper.CourseOfDiseaseRecordMapper;
import com.thj.boot.module.business.dal.mapper.PhysicalExamRecordMapper;
import com.thj.boot.module.business.dal.mapper.QichunAdmissionRecordMapper;
import com.thj.boot.module.business.dal.mapper.admissionrecord.AdmissionRecordMapper;
import com.thj.boot.module.business.service.admissionrecord.AdmissionRecordService;
import com.thj.boot.module.business.dal.datado.admissionrecord.AdmissionRecord;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordFactory;
import com.thj.boot.module.business.service.admissionrecord.CourseRecordTemple;
import jodd.bean.BeanUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
* <AUTHOR>
* @description 针对表【admission_record(入院记录表)】的数据库操作Service实现
* @createDate 2025-07-17 15:11:09
*/
@Service
public class AdmissionRecordServiceImpl extends ServiceImpl<AdmissionRecordMapper, AdmissionRecord>
    implements AdmissionRecordService {



}




