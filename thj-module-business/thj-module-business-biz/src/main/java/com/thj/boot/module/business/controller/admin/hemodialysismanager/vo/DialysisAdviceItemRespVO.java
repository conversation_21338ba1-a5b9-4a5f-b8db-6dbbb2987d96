package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/7 11:18
 * @description
 */
@Data
public class DialysisAdviceItemRespVO implements Serializable {
    /**
     * 药品名称
     */
    private String fDrugName;
    /**
     * 药品规格
     */
    private String fDrugSpec;
    /**
     * 一次用量
     */
    private String fOnceUsing;
    /**
     * 剂量单位
     */
    private String fSpecUnit;
    /**
     * 开药数量
     */
    private String prescribeNo;
    /**
     * 给药途径
     */
    private String drugWay;
    /**
     * 执行频率
     */
    private String frequency;
    /**
     * 用药推送 0-未推送，1-已推送
     */
    private Integer medicateState;

    private Boolean deleted;

    private Boolean stopStatus;

    private Integer pushStatus;

}
