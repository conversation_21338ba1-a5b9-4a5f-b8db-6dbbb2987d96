package com.thj.boot.module.business.controller.admin.dialysisdetection.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/27 17:28
 * @description
 */
@Data
public class DialysisDetectionWarnBaseVO implements Serializable {

    private Long id;

    /**
     * 类型：血压是1，检测记录是2
     */
    private String type;

    /**
     * 透析号
     */
    private String dialyzeNo;

    /**
     * 收缩压低于
     */
    private String shrinkLow;

    /**
     * 收缩压高于
     */
    private String shrinkHigh;

    /**
     * 脉搏低于
     */
    private String pulseLow;

    /**
     * 脉搏高于
     */
    private String pulseHigh;

    /**
     * 舒张压低于
     */
    private String diastoleLow;

    /**
     * 舒张压高于
     */
    private String diastoleHigh;

    /**
     * 血压波动大于
     */
    private String blood;

    //===============检测记录=================

    /**
     * 脉搏低于
     */
    private String detectionPulseLow;

    /**
     * 脉搏高于
     */
    private String detectionPulseHigh;

    /**
     * 呼吸低于
     */
    private String breatheLow;

    /**
     * 呼吸高于
     */
    private String breatheHigh;

    /**
     * kt/v低于
     */
    private String ktvLow;

    /**
     * ktv高于
     */
    private String ktvHigh;

    /**
     * 血流量低于
     */
    private String bloodFlowLow;
    /**
     * 血流量高于
     */
    private String bloodFlowHigh;

    /**
     * 动脉压低于
     */
    private String arterialPressureLow;

    /**
     * 动脉压高于
     */
    private String arterialPressureHigh;

    /**
     * 静脉压低于
     */
    private String venousPressureLow;

    /**
     * 静脉压高于
     */
    private String venousPressureHigh;

    /**
     * 跨膜压低于
     */
    private String transmembranePressureLow;

    /**
     * 跨膜压高于
     */
    private String transmembranePressureHigh;

    /**
     * 超滤率低于
     */
    private String ultrafiltrationRateLow;

    /**
     * 超滤率高于
     */
    private String ultrafiltrationRateHigh;

    /**
     * 超滤量低于
     */
    private String ultrafiltrationCapacityLow;

    /**
     * 超滤量高于
     */
    private String ultrafiltrationCapacityHigh;

    /**
     * 钠浓度低于
     */
    private String sodiumConcentrationLow;

    /**
     * 钠浓度高于
     */
    private String sodiumConcentrationHigh;

    /**
     * 透析液低于
     */
    private String dialysateTemperatureLow;

    /**
     * 透析液高于
     */
    private String dialysateTemperatureHigh;

    /**
     * 置换率低于
     */
    private String replacementRateLow;

    /**
     * 置换率高于
     */
    private String replacementRateHigh;

    /**
     * 置换率低于
     */
    private String displacementQuantityLow;

    /**
     * 置换率低高于
     */
    private String displacementQuantityHigh;

    /**
     * 电导度低于
     */
    private String conductanceLow;

    /**
     * 电导度高于
     */
    private String conductanceHigh;

    /**
     * 门店id
     */
    private Long deptId;
}
