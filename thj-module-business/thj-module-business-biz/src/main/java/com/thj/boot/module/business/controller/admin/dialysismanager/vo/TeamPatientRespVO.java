package com.thj.boot.module.business.controller.admin.dialysismanager.vo;

import com.thj.boot.common.dataobject.PatientBaseDO;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/12/23 14:18
 * @description
 */
@Data
public class TeamPatientRespVO extends PatientBaseDO {

    /**
     * 机号id
     */
    private Long facilityId;
    /**
     * 分区id
     */
    private Long facilitySubareaId;
    /**
     * 机号名称
     */
    private String facilityName;
    /**
     * 状态 0-未签到，1-已签到，2-透析中，3-透析后
     */
    private String state;
    /**
     * 干体重
     */
    private String dryWeight;
    /**
     * 透析模式名称
     */
    private String dialyzeName;
    /**
     * 透析模式id
     */
    private String dialyzeDictValue;
    /**
     * 时间
     */
    private Date dateWeek;
    /**
     * 具体的上午，下午，晚上
     */
    private String weekDay;

}
