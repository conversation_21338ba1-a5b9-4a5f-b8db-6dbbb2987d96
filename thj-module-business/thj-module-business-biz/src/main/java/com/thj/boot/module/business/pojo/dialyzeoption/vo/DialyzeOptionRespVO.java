package com.thj.boot.module.business.pojo.dialyzeoption.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thj.boot.module.business.dal.datado.dialyzeoption.DialyzeOptionDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DialyzeOptionRespVO extends DialyzeOptionBaseVO {
    /**
     * 子级
     */
    private List<DialyzeOptionDO> children;

    /**
     * 子级评率
     */
    private String childFrequency;
    /**
     * 唯一标识
     */
    private String code;
    /**
     * 频次
     */
    private String frequencyName;
    /**
     * 血管通路
     */
    private Long bloodPart;
    private Long bloodType;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date updateTime;

    private String doctorName;
}
