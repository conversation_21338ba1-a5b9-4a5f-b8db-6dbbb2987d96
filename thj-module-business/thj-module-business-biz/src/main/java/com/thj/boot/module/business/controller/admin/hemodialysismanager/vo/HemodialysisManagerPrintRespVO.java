package com.thj.boot.module.business.controller.admin.hemodialysismanager.vo;

import com.thj.boot.module.business.dal.datado.dialysisadvice.DialysisAdviceDO;
import com.thj.boot.module.business.dal.datado.dialysisdetection.DialysisDetectionDO;
import com.thj.boot.module.business.pojo.contradict.vo.ContradictRespVO;
import com.thj.boot.module.business.pojo.hemodialysismanager.vo.HemodialysisManagerBaseVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20 10:21
 * @description
 */
@Data
public class HemodialysisManagerPrintRespVO extends HemodialysisManagerBaseVO {

    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 透析号
     */
    private String dialyzeNo;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 患者来源
     */
    private String patientSource;
    /**
     * 病区
     */
    private String endemicArea;
    /**
     * 床号
     */
    private String bedNo;
    /**
     * 透析总次数
     */
    private String dialyzeTotal;
    /**
     * 诊断
     */
    private String describes;
    /**
     * 住院号
     */
    private String hospitalNo;
    /**
     * 首次透析日期
     */
    private Date firstReceiveTime;
    /**
     * 临时医嘱
     */
    private List<DialysisAdviceDO> dialysisAdviceDOS;
    /**
     * 检测记录
     */
    private List<DialysisDetectionDO> dialysisDetectionDOS;

    /**
     * 多个患者id
     */
    private String patientIds;

    /**
     * 机型
     */
    private String facilityTypeName;
    /**
     * 病区
     */
    private String facilitySubareaName;
    /**
     * 抗凝剂名称
     */
    private List<ContradictRespVO> contradictDOS;
}
