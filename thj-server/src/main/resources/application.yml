spring:
  profiles:
    active: ${profile:prod}
  thymeleaf:
    suffix: .html
    mode: HTML
    encoding: utf-8
    servlet:
      content-type: text/html
    enable-spring-el-compiler: true
  main:
    allow-circular-references: true
  application:
    name: ruoyi-vue-pro-max
  jackson:
    date-format: yyyy-MM-dd
    time-zone: GMT+8
  #    serialization:
  #      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
  #      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
  #      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
  #      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  servlet:
    multipart:
      enabled: true
      # 是单个文件大小 默认1M 10KB
      max-file-size: 30MB
      # 是设置总上传的数据大小
      max-request-size: 30MB
      #当文件达到多少时进行磁盘写入
      file-size-threshold: 30MB

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: NONE
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
encrypt:
  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=

weixin:
  openidpath: https://api.weixin.qq.com/sns/jscode2session
  appid: wx4483568d72850dbb
  appscret: f7817190649e61eac58ec777b14d586e

thj:
  info:
    base-package: com.thj
  codegen:
    base-package: ${thj.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类

wx:
  login:
    #公司
    #    app-id: wxa466fe3ce402c0f5
    #    app-secret: 528c8a184ef620cd3b20d61a499847e2
    #    token: wanlang
    #百伦
    app-id: wx0eaffcf09692c9e2
    app-secret: b80a5796e17e1a65616ef6acab0da199
    token: bailun
    #测试号
    #    app-id: wxff0f9b2536964331
    #    app-secret: aa791872e8be817e48684ede0053be37
    #    token: test
    qr-code-url: https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=TOKEN
    token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    user-info-url: https://api.weixin.qq.com/cgi-bin/user/info?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN
    show-qr-code: https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=TICKET

  ############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认60分钟，-1 代表永久有效
  timeout: 86400
  # 30分钟无操作抛异常，默认-1 代表不限制，永不冻结
  activity-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # token前缀
  token-prefix: "Bearer"
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false

deviceUrl: http://47.106.216.25:1024/analysisData/query

cnrds:
  getAccessTokenUrl: https://openapi.cnrds.net/AutoReportOpen/GetAccessToken
  checkFormDataUrl: https://openapi.cnrds.net/AutoReportOpen/CheckFormData
  reportDataUrl: https://openapi.cnrds.net/AutoReportOpen/ReportData



ai:
  report:
    generation-timeout-minutes: 3 # AI报告生成超时时间（分钟）
  deepseek:
    api-url: http://192.168.0.179:8000/v1/chat/completions
    api-key: 123456
    model: deepseek0528
    temperature: 0.1
    max-tokens: 16384
    timeout: 120000  # 5分钟超时
    retry-count: 2   # 增加重试次数到5次