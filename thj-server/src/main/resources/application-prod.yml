server:
  port: 10086
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        # 解决线程未停止的内存泄漏问题
        remove-abandoned: true # 是否开启连接泄露自动检测
        remove-abandoned-timeout: 300 # 连接长时间没有使用，被认为发生泄露时长，单位秒
        log-abandoned: true # 发生泄露时是否需要输出 log，建议在开启连接泄露检测时开启，方便排错
      primary: master
      datasource:
        master:
          name: bailun
          url: ************************/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          username: szbl
          password: Szbl0755@
        slave: # 模拟从库，可根据自己需要修改
          name: FUHAI
          lazy: true # 开启懒加载，保证启动速度
          url: jdbc:dm://127.0.0.1:5236/${spring.datasource.dynamic.datasource.slave.name}?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=UTF-8
          username: FUHAI
          password: root12345


  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: *********** # 地址
    port: 6379 # 端口
    database: 15 # 数据库索引
    password: redis123@

#logging:
#  level:
#    root: ERROR

#上传文件地址
infra:
  hostname: http://***********:${server.port}/admin-api/business/infra/


file:
  staticPath: http://***********:${server.port}
  staticPatternPath: /upimages
  uploadFolder: /home/<USER>/


his:
  #药品
  drug: http://***********:5000/api/His/Drug
  #耗材
  consumables: http://***********:5000/api/His/Consumables
  #项目分类
  prescriptionItemType: http://***********:5000/api/system/DictionaryData/309600728077305093/Data/Selector
  #项目
  prescriptionItem: http://***********:5000/api/His/PrescriptionItem
  #套餐
  combo: http://***********:5000/api/His/prescriptionpackage
  #患者
  patient: http://***********:5000/api/his/patient/SyncHmsPatient
  #排班
  arrange: http://***********:5000/api/his/patient/SyncHmsClasses
  #医嘱
  advice: http://***********:5000/api/his/prescription/SyncHmsDpCreateOrUpdate

  adviceDelete: http://***********:5000/api/his/prescription/SyncHmsDpCancel