package com.thj.boot.module.system.pojo.user.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.thj.boot.module.system.dal.datado.dept.DeptDO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/27 16:31
 * @description
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserRespVO extends UserBaseVO {

    /**
     * 用户对应的角色标识符
     */
    private List<String> sysRoleCodes;
    /**
     * 用户对应的角色id
     */
    private List<String> sysRoles;
    /**
     * 用户对应权限
     */
    private List<String> permissions;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色id
     */
    private Long roleId;
    /**
     * 多个门店id
     */
    private String deptIds;
    /**
     * 多个门店
     */
    private List<DeptDO> deptDOList;

}
