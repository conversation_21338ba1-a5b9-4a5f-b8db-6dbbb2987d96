package com.thj.boot.module.system.service.user;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.thj.boot.common.constant.Constants;
import com.thj.boot.common.enums.CourseRecordEnum;
import com.thj.boot.common.enums.DeptEnum;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.exception.enums.GlobalErrorCodeConstants;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.api.infra.InfraApi;
import com.thj.boot.module.business.api.upload.UploadApi;
import com.thj.boot.module.business.api.upload.dto.UploadDTO;
import com.thj.boot.module.system.api.dept.DeptApi;
import com.thj.boot.module.system.api.dept.dto.DeptRespDTO;
import com.thj.boot.module.system.api.patient.PatientApi;
import com.thj.boot.module.system.api.patient.dto.PatientDTO;
import com.thj.boot.module.system.common.PermissionMapUtil;
import com.thj.boot.module.system.convert.user.AdminUserConvert;
import com.thj.boot.module.system.dal.datado.dept.DeptDO;
import com.thj.boot.module.system.dal.datado.permission.MenuDO;
import com.thj.boot.module.system.dal.datado.permission.RoleDO;
import com.thj.boot.module.system.dal.datado.permission.UserRoleDO;
import com.thj.boot.module.system.dal.datado.user.AdminUserDO;
import com.thj.boot.module.system.dal.mapper.dept.DeptMapper;
import com.thj.boot.module.system.dal.mapper.permission.MenuMapper;
import com.thj.boot.module.system.dal.mapper.permission.RoleMapper;
import com.thj.boot.module.system.dal.mapper.permission.UserRoleMapper;
import com.thj.boot.module.system.dal.mapper.user.AdminUserMapper;
import com.thj.boot.module.system.enums.permission.MenuTypeEnum;
import com.thj.boot.module.system.mq.producer.permission.PermissionProducer;
import com.thj.boot.module.system.pojo.auth.vo.AuthReqVO;
import com.thj.boot.module.system.pojo.menu.vo.MenuCreateReqVO;
import com.thj.boot.module.system.pojo.user.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletRequest;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminUserServiceImpl implements AdminUserService {

    @Value("${file.staticPath}")
    private String staticPath;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private InfraApi infraApi;

    @Resource
    private UploadApi uploadApi;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private DeptApi deptApi;

    @Resource
    private PermissionProducer permissionProducer;

    @Resource
    private PatientApi patientApi;

    @Override
    public AdminUserDO getUser(Long id) {
        return userMapper.selectById(id);
    }
    @Value("${encrypt.privateKey}")
    private String privateKey;
    @Resource
    HttpServletRequest httpServletRequest;


    @Override
    public PageResult<UserRespVO> getUserPage(UserPageReqVO reqVO) {
        PageResult<AdminUserDO> adminUserDOPageResult = adminUserMapper.selectPage(reqVO);
        PageResult<UserRespVO> userRespVOPageResult = AdminUserConvert.INSTANCE.convertPageList(adminUserDOPageResult);
        if (CollectionUtil.isNotEmpty(userRespVOPageResult.getList())) {
            List<UserRespVO> collect = userRespVOPageResult.getList().stream().peek(userRespVO -> {
                userRespVO.setPassword(null);
                //创建者
                AdminUserDO adminUserDO = adminUserMapper.selectOne(AdminUserDO::getId, userRespVO.getCreator());
                userRespVO.setCreator(adminUserDO == null ? null : adminUserDO.getNickname());
                if(StrUtil.isNotEmpty(userRespVO.getRoleIdStr())){
                    //角色信息
                    List<RoleDO> roleDOS = roleMapper.selectList(RoleDO::getId, Arrays.stream(userRespVO.getRoleIdStr().split(",")).collect(Collectors.toList()));
                    userRespVO.setSysRoles(CollectionUtil.isEmpty(roleDOS) ? null : roleDOS.stream().map(RoleDO::getName).collect(Collectors.toList()));
                }
            }).collect(Collectors.toList());
            userRespVOPageResult.setList(collect);
            if (reqVO.getRoleId() != null) {
                List<UserRespVO> collect1 = collect.stream().filter(userRespVO -> userRespVO.getRoleIdStr().contains(reqVO.getRoleId() + "")).collect(Collectors.toList());
                userRespVOPageResult.setList(collect1);
                userRespVOPageResult.setTotal(Long.valueOf(collect1.size()));
            }
        }
        return userRespVOPageResult;
    }

    @Override
    public Map<String, Object> login(AuthReqVO reqVO) {
        Map<String, Object> map = Maps.newHashMap();
        AdminUserDO userDO = adminUserMapper.selectOne(AdminUserDO::getUsername, reqVO.getUsername(), AdminUserDO::getStatus, "0");
        // 对密码解密
        Cipher cipher = null;
        String decryptedText = null;
        try {
            byte[] keyBytes = Base64.decodeBase64(privateKey.getBytes());
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey priKey = keyFactory.generatePrivate(keySpec);
            cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            byte[] decryptedData = cipher.doFinal(Base64.decodeBase64(reqVO.getPassword()));
            decryptedText = new String(decryptedData);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (userDO != null && "0".equals(userDO.getStatus())) {
            if (!BCrypt.checkpw(decryptedText, userDO.getPassword())) {
                throw new ServiceException(GlobalErrorCodeConstants.LOGINEROOR);
            }
            StpUtil.login(userDO.getId());
            String tokenValue = StpUtil.getTokenValue();
            map.put("token", tokenValue);
            // 查询密码是否过期
            if (!StringUtils.isEmpty(userDO.getExpireTime())){
                boolean after = userDO.getExpireTime().before(new Date());
                map.put("expire",after);
            }
            return map;
        }
        throw new ServiceException(GlobalErrorCodeConstants.LOGINEROOR);
    }

    @Override
    @CachePut(cacheNames = "adminList", key = "#createVO.deptId", condition = " #createVO != null && #createVO.deptId != null")
    @Transactional(rollbackFor = Exception.class)
    public void createUser(UserCreateReqVO createVO) {
        //校验账号唯一性
        checkCreateUser(createVO.getUsername());
        //存入一个用户对应对个角色
        List<UserRoleDO> userRoleDOS = Lists.newArrayList();
        //新增用户
        AdminUserDO adminUserDO = AdminUserConvert.INSTANCE.convert(createVO);
        String hashpw = BCrypt.hashpw(adminUserDO.getPassword(), BCrypt.gensalt());
        adminUserDO.setPassword(hashpw);
        adminUserDO.setRoleIdStr(createVO.getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        adminUserMapper.insert(adminUserDO);
        createVO.setId(adminUserDO.getId());
        //新增角色
        if (CollectionUtil.isNotEmpty(createVO.getRoleIds())) {
            for (Long roleId : createVO.getRoleIds()) {
                UserRoleDO userRoleDO = new UserRoleDO();
                userRoleDO.setUserId(adminUserDO.getId());
                userRoleDO.setRoleId(roleId);
                userRoleDOS.add(userRoleDO);
            }
            userRoleMapper.insertBatch(userRoleDOS);
        }
        //发送MQ初始化用户对应的权限
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                permissionProducer.sendRoleMenuRefreshMessage();
            }
        });

    }

    private void checkCreateUser(String username) {
        Long count = adminUserMapper.selectCount(AdminUserDO::getUsername, username);
        if (count > 0) {
            throw new ServiceException(GlobalErrorCodeConstants.USER_NAME_LOGIN);
        }
    }


    @Override
    @CachePut(cacheNames = "adminList", key = "#updateReqVO.deptId", condition = " #updateReqVO != null && #updateReqVO.deptId != null")
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateReqVO updateReqVO) {
        checkUpdateUser(updateReqVO);
        //存入一个用户对应对个角色
        List<UserRoleDO> userRoleDOS = Lists.newArrayList();
        AdminUserDO adminUserDO = AdminUserConvert.INSTANCE.convert2(updateReqVO);
        adminUserDO.setRoleIdStr(updateReqVO.getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 往后推90天
        calendar.add(Calendar.DAY_OF_YEAR, 90);
        // 获取90天后的日期
        Date resultDate = calendar.getTime();
        adminUserDO.setExpireTime(resultDate);
        adminUserMapper.updateById(adminUserDO);
        //修改角色
        if (CollectionUtil.isNotEmpty(updateReqVO.getRoleIds())) {
            //根据用户id删除对应角色
            userRoleMapper.deleteListByUserId(updateReqVO.getId());
            //新增用户和角色
            for (Long roleId : updateReqVO.getRoleIds()) {
                UserRoleDO userRoleDO = new UserRoleDO();
                userRoleDO.setUserId(adminUserDO.getId());
                userRoleDO.setRoleId(roleId);
                userRoleDOS.add(userRoleDO);
            }
            userRoleMapper.insertBatch(userRoleDOS);
        }

        //发送MQ初始化用户对应的权限
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                permissionProducer.sendRoleMenuRefreshMessage();
            }
        });

    }

    private void checkUpdateUser(UserUpdateReqVO updateReqVO) {
        AdminUserDO adminUserDO = adminUserMapper.selectOne(AdminUserDO::getUsername, updateReqVO.getUsername());
        if (adminUserDO != null && !adminUserDO.getId().equals(updateReqVO.getId())) {
            throw new ServiceException(GlobalErrorCodeConstants.USER_NAME_LOGIN);
        }

    }

    @Override
    public void deleteUser(String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        adminUserMapper.deleteBatchIds(idList);
    }

    @Override
    //@DS("sharding")
    public List<UserRespVO> getUserList(UserCreateReqVO createReqVO,Long SystemDeptId) {
        return AdminUserConvert.INSTANCE.convertList(adminUserMapper.selectList(createReqVO,SystemDeptId));
    }

    @Override
    public UserRespVO getUserInfo(Long id) {
        AdminUserDO adminUserDO = adminUserMapper.selectOne(AdminUserDO::getId, id);
        adminUserDO.setPassword(null);
        return AdminUserConvert.INSTANCE.convert3(adminUserDO);
    }

    @Override
    public UserRespVO getLoginUserInfo() {
        //获取当前登录用户id
        long userId = 0;
        try {
            userId = StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            throw new ServiceException(GlobalErrorCodeConstants.TOKEN_EXPIRE);
        }
        log.info("当前登录用户id：{}", userId);
        //用户头像
        AdminUserDO adminUserDO = adminUserMapper.selectOne(AdminUserDO::getId, userId);
        List<UserRoleDO> userRoleDOS = userRoleMapper.selectListByUserId(userId);
        List<DeptDO> deptDOS = null;
        if (adminUserDO != null && StrUtil.isNotEmpty(adminUserDO.getDeptIds())) {
            List<Long> deptIdList = Arrays.stream(adminUserDO.getDeptIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            deptDOS = deptMapper.selectList(DeptDO::getId, deptIdList);
        }
        UserRespVO userRespVO = new UserRespVO();
        if (UserBaseVO.isAdmin(userId)) {
            List<MenuDO> menuDOS = menuMapper.selectList();
            List<String> permissions = menuDOS.stream().map(MenuDO::getPermission).collect(Collectors.toList());
            permissions.add(Constants.SUPER_PERMISSION);
            userRespVO.setSysRoleCodes(Lists.newArrayList("super_admin"));
            userRespVO.setUsername(adminUserDO.getUsername());
            userRespVO.setNickname(adminUserDO.getNickname());
            userRespVO.setRemark(adminUserDO.getRemark());
            userRespVO.setPermissions(permissions.stream().filter(permission -> !"".equals(permission)).collect(Collectors.toList()));
            userRespVO.setAvatar(staticPath + adminUserDO.getAvatar());
            userRespVO.setSysRoles(CollectionUtil.isEmpty(userRoleDOS) ? null : userRoleDOS.stream().map(UserRoleDO::getRoleId).map(String::valueOf).collect(Collectors.toList()));
            userRespVO.setSignature(staticPath + adminUserDO.getSignature());
            userRespVO.setId(userId);
            userRespVO.setDeptIds(adminUserDO.getDeptIds());
            userRespVO.setDeptDOList(deptDOS);
            return userRespVO;
        }
        //根据用户获取角色标识符
        List<RoleDO> roleDOS = PermissionMapUtil.getUserRole(userId);
        userRespVO.setSysRoleCodes(CollectionUtil.isEmpty(roleDOS) ? new ArrayList<String>() : roleDOS.stream().map(RoleDO::getCode).collect(Collectors.toList()));
        //根据用户获取对应的权限标识符
        List<MenuDO> menuDOS = PermissionMapUtil.getUserMenu(userId);
        userRespVO.setPermissions(CollectionUtil.isEmpty(menuDOS) ? new ArrayList<String>() : menuDOS.stream().map(MenuDO::getPermission).filter(permission -> !"".equals(permission)).collect(Collectors.toList()));
        //获取用户信息
        userRespVO.setUsername(adminUserDO.getUsername());
        userRespVO.setNickname(adminUserDO.getNickname());
        userRespVO.setRemark(adminUserDO.getRemark());
        userRespVO.setSysRoles(CollectionUtil.isEmpty(userRoleDOS) ? null : userRoleDOS.stream().map(UserRoleDO::getRoleId).map(String::valueOf).collect(Collectors.toList()));
        userRespVO.setId(userId);
        userRespVO.setAvatar(staticPath + adminUserDO.getAvatar());
        userRespVO.setSignature(staticPath + adminUserDO.getSignature());
        userRespVO.setDeptIds(adminUserDO.getDeptIds());
        userRespVO.setDeptDOList(deptDOS);
        return userRespVO;
    }

    @Override
    public List<MenuDO> listMenu() {
        //获取当前登录用户id
        long userId = 0;
        try {
            userId = StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            throw new ServiceException(GlobalErrorCodeConstants.TOKEN_EXPIRE);
        }
        log.info("当前登录用户id：{}", userId);
        List<MenuDO> rootList = null;
        //如果是超管获取全部菜单信息
        if (UserBaseVO.isAdmin(userId)) {
            //不包含按钮菜单列表
            MenuCreateReqVO reqVO = new MenuCreateReqVO();
            reqVO.setType(MenuTypeEnum.BUTTON.getType());
            reqVO.setStatus(0);
            List<MenuDO> menuDOS = menuMapper.selectList(reqVO);
            if(!DeptEnum.QICHUN.getDeptId().equals(httpServletRequest.getHeader("systemdeptid").toString())){
                menuDOS = menuDOS.stream().filter(menuDO -> (!CourseRecordEnum.QICHUNADMISSIONRECORD.getName().equals(menuDO.getName()) &&
                        !CourseRecordEnum.QICHUNCOURSERECORD.getName().equals(menuDO.getName())
                        &&
                        !CourseRecordEnum.QICHUNDISCHARGESUMMARY.getName().equals(menuDO.getName()))).collect(Collectors.toList());
            }
            if(!DeptEnum.RONGSHUI.getDeptId().equals(httpServletRequest.getHeader("systemdeptid").toString())){
                menuDOS = menuDOS.stream().filter(menuDO -> (!CourseRecordEnum.RONGSHUIADMISSIONRECORD.getName().equals(menuDO.getName())
                        && !CourseRecordEnum.RONGSHUICOURSERECORD.getName().equals(menuDO.getName()))).collect(Collectors.toList());
            }
            rootList = menuDOS.stream().filter(menuDO -> 0 == menuDO.getParentId()).sorted((a, b) -> a.getSort() - b.getSort()).collect(Collectors.toList());
            List<MenuDO> subList = menuDOS.stream().filter(menuDO -> 0 != menuDO.getParentId()).sorted((a, b) -> a.getSort() - b.getSort()).collect(Collectors.toList());
            rootList.forEach(root -> busort(root, subList));
            return rootList;
        }
        List<MenuDO> menuDOS = PermissionMapUtil.getUserMenu(userId);
        //过滤掉菜单类型为按钮
        List<MenuDO> menuDOList = menuDOS.stream().filter(menuDO -> MenuTypeEnum.BUTTON.getType() != menuDO.getType()).filter(menuDO -> 0 == menuDO.getStatus()).collect(Collectors.toList());
//        AdminUserDO adminUserDO = adminUserMapper.selectById(userId);
        String deptId = httpServletRequest.getHeader("systemdeptid") != null ? httpServletRequest.getHeader("systemdeptid").toString() : null;
        if(!DeptEnum.QICHUN.getDeptId().equals(deptId)){
            menuDOList = menuDOList.stream().filter(menuDO -> (!CourseRecordEnum.QICHUNADMISSIONRECORD.getName().equals(menuDO.getName()) &&
                    !CourseRecordEnum.QICHUNCOURSERECORD.getName().equals(menuDO.getName()) &&
                    !CourseRecordEnum.QICHUNDISCHARGESUMMARY.getName().equals(menuDO.getName()))).collect(Collectors.toList());
        }
        if(!DeptEnum.RONGSHUI.getDeptId().equals(deptId)){
            menuDOList = menuDOList.stream().filter(menuDO -> (!CourseRecordEnum.RONGSHUIADMISSIONRECORD.getName().equals(menuDO.getName())
                    && !CourseRecordEnum.RONGSHUICOURSERECORD.getName().equals(menuDO.getName()))).collect(Collectors.toList());
        }
        rootList = menuDOList.stream().filter(menuDO -> 0 == menuDO.getParentId()).sorted((a, b) -> a.getSort() - b.getSort()).collect(Collectors.toList());
        List<MenuDO> subList = menuDOList.stream().filter(menuDO -> 0 != menuDO.getParentId()).sorted((a, b) -> a.getSort() - b.getSort()).collect(Collectors.toList());
        rootList.forEach(root -> busort(root, subList));

        return rootList;
    }

    @Override
    public void updatePassword(UserUpdateReqVO updateReqVO) {
        AdminUserDO userDO = new AdminUserDO();
        if (StrUtil.isNotEmpty(updateReqVO.getPassword())) {
            userDO.setPassword(BCrypt.hashpw(updateReqVO.getPassword(), BCrypt.gensalt()));
            userDO.setId(updateReqVO.getId());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            // 往后推90天
            calendar.add(Calendar.DAY_OF_YEAR, 90);
            // 获取90天后的日期
            Date resultDate = calendar.getTime();
            userDO.setExpireTime(resultDate);
            adminUserMapper.updateById(userDO);
            return;
        }
        //个人中心修改密码
        long userId = StpUtil.getLoginIdAsLong();
        AdminUserDO adminUserDO = adminUserMapper.selectById(userId);
        if (adminUserDO != null && "0".equals(adminUserDO.getStatus())) {
            if (!BCrypt.checkpw(updateReqVO.getOldPassword(), adminUserDO.getPassword())) {
                throw new ServiceException(GlobalErrorCodeConstants.OLD_PASSWORD_ERROR);
            }
            userDO.setPassword(BCrypt.hashpw(updateReqVO.getNewPassword(), BCrypt.gensalt()));
            userDO.setId(userId);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            // 往后推90天
            calendar.add(Calendar.DAY_OF_YEAR, 90);
            // 获取90天后的日期
            Date resultDate = calendar.getTime();
            userDO.setExpireTime(resultDate);
        }
        adminUserMapper.updateById(userDO);
        StpUtil.logout();
    }

    @Override
    public void updateStatus(UserUpdateReqVO updateReqVO) {
        AdminUserDO adminUserDO = AdminUserConvert.INSTANCE.convert2(updateReqVO);
        adminUserMapper.updateById(adminUserDO);
    }

    @Override
    public UserRespVO profileGet() {
        long loginId = StpUtil.getLoginIdAsLong();
        AdminUserDO userDO = adminUserMapper.selectById(loginId);
        //所属角色
        UserRespVO userRespVO = AdminUserConvert.INSTANCE.convert3(userDO);
        if (userRespVO != null) {
            List<UserRoleDO> userRoleDOS = userRoleMapper.selectListByUserId(userRespVO.getId());
            if (CollectionUtil.isNotEmpty(userRoleDOS)) {
                List<Long> roleIds = userRoleDOS.stream().map(UserRoleDO::getRoleId).collect(Collectors.toList());
                List<RoleDO> roleDOS = roleMapper.selectList(RoleDO::getId, roleIds);
                if (CollectionUtil.isNotEmpty(roleDOS)) {
                    userRespVO.setRoleName(roleDOS.stream().map(RoleDO::getName).collect(Collectors.joining(",")));
                }
            }
        }
        return userRespVO;
    }

    @Override
    public void profileUpdate(UserUpdateReqVO updateReqVO) {
        AdminUserDO userDO = AdminUserConvert.INSTANCE.convert2(updateReqVO);
        long loginId = StpUtil.getLoginIdAsLong();
        userDO.setId(loginId);
        adminUserMapper.updateById(userDO);
    }


    @Override
    public Map<String, Object> updateAvater(MultipartFile multipartFile, Integer type) {
        long loginId = StpUtil.getLoginIdAsLong();
        UploadDTO uploadDTO = new UploadDTO();
        uploadDTO.setFile(multipartFile);
        Map<String, Object> map = uploadApi.uploadFileDoc(uploadDTO);
        if (MapUtil.isNotEmpty(map)) {
            adminUserMapper.update(new AdminUserDO(), new LambdaUpdateWrapper<AdminUserDO>()
                    .eq(AdminUserDO::getId, loginId)
                    .set(type == 1, AdminUserDO::getAvatar, map.get("relativePath"))
                    .set(type == 2, AdminUserDO::getSignature, map.get("relativePath")));
        }
        return map;
    }

    @Override
    public List<UserRespVO> getUserByRole(String roleCode, Long SystemDeptId) {
        MPJLambdaWrapper<AdminUserDO> wrapper = new MPJLambdaWrapper<>(AdminUserDO.class);
        wrapper.leftJoin(UserRoleDO.class, UserRoleDO::getUserId, AdminUserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .select(AdminUserDO::getId, AdminUserDO::getUsername, AdminUserDO::getNickname)
                .in(StrUtil.isNotEmpty(roleCode) && "doctor".equals(roleCode) || StrUtil.isNotEmpty(roleCode) && "director".equals(roleCode), RoleDO::getCode, Lists.newArrayList("doctor", "director"))
                .in(StrUtil.isNotEmpty(roleCode) && "nurse".equals(roleCode) || StrUtil.isNotEmpty(roleCode) && "head_nurse".equals(roleCode), RoleDO::getCode, Lists.newArrayList("nurse", "head_nurse"))
                .in(StrUtil.isNotEmpty(roleCode) && "inspect_user".equals(roleCode), RoleDO::getCode, Lists.newArrayList("nurse", "head_nurse", "nurse_h", "hulibu"))
                .eq(StrUtil.isNotEmpty(roleCode) && !Lists.newArrayList("doctor", "nurse", "inspect_user").contains(roleCode), RoleDO::getCode, roleCode)
                //.like(AdminUserDO::getDeptIds, SystemDeptId)
                .eq(AdminUserDO::getStatus, "0");
        if(SystemDeptId != null){
            wrapper.last("and CONCAT(',',dept_ids,',')  like '%," + SystemDeptId + ",%'");
        }
        List<UserRespVO> userRespVOS = adminUserMapper.selectJoinList(UserRespVO.class, wrapper);
        //去重
        if(!CollectionUtils.isEmpty(userRespVOS)) {
            userRespVOS = userRespVOS.stream()
                    .collect(Collectors.toMap(UserRespVO::getId, user -> user, (u1, u2) -> u1))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(UserRespVO::getNickname))
                    .collect(Collectors.toList());
        }
        return userRespVOS;
    }

    @Override
    public List<UserRespVO> getUserByRoleAll(String roleCode, Long systemDeptId) {
        MPJLambdaWrapper<AdminUserDO> wrapper = new MPJLambdaWrapper<>(AdminUserDO.class);
        wrapper.leftJoin(UserRoleDO.class, UserRoleDO::getUserId, AdminUserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .select(AdminUserDO::getId, AdminUserDO::getNickname,AdminUserDO::getDeptIds,AdminUserDO::getStatus,AdminUserDO::getDeleted)
                .in(StrUtil.isNotEmpty(roleCode) && "doctor".equals(roleCode) || StrUtil.isNotEmpty(roleCode) && "director".equals(roleCode), RoleDO::getCode, Lists.newArrayList("doctor", "director"))
                .in(StrUtil.isNotEmpty(roleCode) && "nurse".equals(roleCode) || StrUtil.isNotEmpty(roleCode) && "head_nurse".equals(roleCode), RoleDO::getCode, Lists.newArrayList("nurse", "head_nurse"))
                .in(StrUtil.isNotEmpty(roleCode) && "inspect_user".equals(roleCode), RoleDO::getCode, Lists.newArrayList("nurse", "head_nurse", "nurse_h", "hulibu"))
                .eq(StrUtil.isNotEmpty(roleCode) && !Lists.newArrayList("doctor", "nurse", "inspect_user").contains(roleCode), RoleDO::getCode, roleCode);
        List<UserRespVO> userRespVOS = adminUserMapper.selectJoinList(UserRespVO.class, wrapper);
        //去重
        if(!CollectionUtils.isEmpty(userRespVOS)) {
            userRespVOS = userRespVOS.stream()
                    .collect(Collectors.toMap(UserRespVO::getId, user -> user, (u1, u2) -> u1))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(UserRespVO::getNickname))
                    .collect(Collectors.toList());
        }
        return userRespVOS;
    }


    @Override
    public PageResult<AdminUserDO> getUserByRolePage(UserPageReqVO reqVO) {
        MPJLambdaWrapper<AdminUserDO> wrapper = new MPJLambdaWrapper<>(AdminUserDO.class);
        wrapper.leftJoin(UserRoleDO.class, UserRoleDO::getUserId, AdminUserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .select(AdminUserDO::getId, AdminUserDO::getUsername, AdminUserDO::getNickname)
                .eq(StrUtil.isNotEmpty(reqVO.getRoleCode()), RoleDO::getCode, reqVO.getRoleCode())
                .eq(reqVO.getUserId() != null, AdminUserDO::getId, reqVO.getUserId());

        if (reqVO.getDeptId() != null) {
            wrapper.last("AND FIND_IN_SET(" + reqVO.getDeptId() + ",dept_ids)");
        }
        Page<AdminUserDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<AdminUserDO> userRespVOPage = adminUserMapper.selectJoinPage(page, AdminUserDO.class, wrapper);
        return new PageResult<>(userRespVOPage.getRecords(), userRespVOPage.getTotal());
    }

    @Override
    public String getDialysisNo(String deptNumber, Long Systemdeptid) {
        //1-透析号，2-住院门诊号
        DeptRespDTO dept = deptApi.getDept(Systemdeptid);
        if (dept != null && StrUtil.isEmpty(dept.getDeptNumber())) {
            throw new ServiceException(GlobalErrorCodeConstants.DEPT_NUMBER_SETTING);
        }
        PatientDTO patientDTO = patientApi.getPatientDialyzeNo();
        if (patientDTO != null) {
            deptNumber = patientDTO.getDialyzeNo();
            Object deptNumbers = redisTemplate.opsForValue().get(dept.getDeptNumber());
            String format1 = String.format("%04d", deptNumbers);

                Long increment = redisTemplate.opsForValue().increment(dept.getDeptNumber());
                String format = String.format("%04d", increment);
                return dept.getDeptNumber() + format;

        }
        if (StrUtil.isNotEmpty(deptNumber)) {
            Object deptNumbers = redisTemplate.opsForValue().get(dept.getDeptNumber());
            String format1 = String.format("%04d", deptNumbers);
            if (deptNumber.equals(dept.getDeptNumber() + format1)) {
                throw new ServiceException(GlobalErrorCodeConstants.DEPT_NUMBER_UNQIE);
            }
        }
        Long increment = redisTemplate.opsForValue().increment(dept.getDeptNumber());
        String format = String.format("%04d", increment);
        return dept.getDeptNumber() + format;
    }

    @Override
    public void updateUserApp(UserUpdateReqVO updateReqVO) {
        AdminUserDO adminUserDO = AdminUserConvert.INSTANCE.convert2(updateReqVO);
        adminUserDO.setId(StpUtil.getLoginIdAsLong());
        if (StrUtil.isNotEmpty(adminUserDO.getAvatar())) {
            adminUserDO.setAvatar(StrUtil.subAfter(adminUserDO.getAvatar(), staticPath, false));
        }
        if (StrUtil.isNotEmpty(adminUserDO.getSignature())) {
            adminUserDO.setSignature(StrUtil.subAfter(adminUserDO.getSignature(), staticPath, false));
        }
        adminUserMapper.updateById(adminUserDO);
    }

    @Override
    public void updateUserPasswordApp(UserUpdateReqVO updateReqVO) {
        //查询旧密码是否存在
        long userId = StpUtil.getLoginIdAsLong();
        AdminUserDO adminUserDO = adminUserMapper.selectById(userId);
        if (adminUserDO == null || !BCrypt.checkpw(updateReqVO.getOldPassword(), adminUserDO.getPassword())) {
            throw new ServiceException(GlobalErrorCodeConstants.OLD_PASSWORD_EXITS);
        }
        AdminUserDO adminUserDO1 = new AdminUserDO();
        adminUserDO1.setId(userId);
        adminUserDO1.setPassword(BCrypt.hashpw(adminUserDO.getPassword(), BCrypt.gensalt()));
        adminUserMapper.updateById(adminUserDO1);
        StpUtil.logout();
    }

    @Override
    public List<UserRespVO> getUserListDept(UserCreateReqVO createReqVO, String systemDeptId) {
        createReqVO.setDeptIds(systemDeptId);
        return AdminUserConvert.INSTANCE.convertList(adminUserMapper.selectList(createReqVO,Long.valueOf(systemDeptId)));
    }


    private void busort(MenuDO root, List<MenuDO> subList) {
        List<MenuDO> childrenList = subList.stream().filter(sysMenu -> sysMenu.getParentId().equals(Long.valueOf(root.getId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(childrenList)) {
            root.setChildren(childrenList);
            childrenList.forEach(sysMenu -> busort(sysMenu, subList));
        } else {
            root.setChildren(null);
        }
    }

    public static void main(String[] args) {
        System.out.println(BCrypt.hashpw("wanlang", BCrypt.gensalt()));
    }
}
