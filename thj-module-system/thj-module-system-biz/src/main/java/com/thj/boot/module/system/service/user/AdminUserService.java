package com.thj.boot.module.system.service.user;

import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.dal.datado.permission.MenuDO;
import com.thj.boot.module.system.dal.datado.user.AdminUserDO;
import com.thj.boot.module.system.pojo.auth.vo.AuthReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserCreateReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserPageReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserRespVO;
import com.thj.boot.module.system.pojo.user.vo.UserUpdateReqVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface AdminUserService {

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserDO getUser(Long id);


    /**
     * 获得用户分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<UserRespVO> getUserPage(UserPageReqVO reqVO);


    /***
     * <AUTHOR>
     * @date 2023/9/27 17:27
     * @Description 登录
     **/
    Map<String, Object> login(AuthReqVO reqVO);

    /***
     * <AUTHOR>
     * @date 2023/10/6 9:40
     * @Description 新增用户
     **/
    void createUser(UserCreateReqVO createVO);

    /***
     * <AUTHOR>
     * @date 2023/10/6 9:53
     * @Description 修改用户
     **/
    void updateUser(UserUpdateReqVO updateReqVO);

    /***
     * <AUTHOR>
     * @date 2023/10/6 10:01
     * @Description 批量删除
     **/
    void deleteUser(String ids);

    /***
     * <AUTHOR>
     * @date 2023/10/6 10:15
     * @Description 列表不分页
     **/
    List<UserRespVO> getUserList(UserCreateReqVO listReqVO,Long systemDeptId);

    /***
     * <AUTHOR>
     * @date 2023/10/6 13:38
     * @Description 详情
     **/
    UserRespVO getUserInfo(Long id);

    /***
     * <AUTHOR>
     * @date 2023/10/6 15:09
     * @Description 获取登录成功后的用户信息
     **/
    UserRespVO getLoginUserInfo();

    /***
     * <AUTHOR>
     * @date 2023/10/6 15:46
     * @Description 获取菜单列表
     **/
    List<MenuDO> listMenu();

    /***
     * <AUTHOR>
     * @date 2023/10/23 14:22
     * @Description 用户修改密码
     **/
    void updatePassword(UserUpdateReqVO updateReqVO);

    /***
     * <AUTHOR>
     * @date 2023/10/23 15:11
     * @Description 修改用户状态
     **/
    void updateStatus(UserUpdateReqVO updateReqVO);

    /***
     * <AUTHOR>
     * @date 2023/10/31 11:16
     * @Description 个人中心-基本资料
     **/
    UserRespVO profileGet();

    /***
     * <AUTHOR>
     * @date 2023/10/31 11:21
     * @Description 修改基本资料
     **/
    void profileUpdate(UserUpdateReqVO updateReqVO);


    /***
     * <AUTHOR>
     * @date 2023/10/31 12:30
     * @Description 上传头像
     **/
    Map<String, Object> updateAvater(MultipartFile multipartFile,Integer type);

    /***
     * <AUTHOR>
     * @date 2023/12/1 14:05
     * @Description 通过角色roleCode获取用户信息
     **/
    List<UserRespVO> getUserByRole(String roleCode,Long SystemDeptId);
    PageResult<AdminUserDO> getUserByRolePage(UserPageReqVO reqVO);

    /***
     * <AUTHOR>
     * @date 2023/12/1 15:59
     * @Description 随机生成透析号
     **/
    String getDialysisNo(String deptNumber, Long Systemdeptid);

    /***
     * <AUTHOR>
     * @date 2024/1/11 13:48
     * @Description 修改app用户信息
     **/
    void updateUserApp(UserUpdateReqVO updateReqVO);

    /***
     * <AUTHOR>
     * @date 2024/1/12 14:00
     * @Description 修改app密码
     **/
    void updateUserPasswordApp(UserUpdateReqVO updateReqVO);

    List<UserRespVO> getUserListDept(UserCreateReqVO createReqVO, String systemDeptId);

    List<UserRespVO>  getUserByRoleAll(String roleCode, Long systemDeptId);
}
