package com.thj.boot.module.system.controller.admin.user;

import com.thj.boot.common.annotation.OperateLog;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.system.pojo.user.vo.UserCreateReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserPageReqVO;
import com.thj.boot.module.system.pojo.user.vo.UserRespVO;
import com.thj.boot.module.system.pojo.user.vo.UserUpdateReqVO;
import com.thj.boot.module.system.service.user.AdminUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2023/9/27 16:12
 * @description
 */
@RestController
@RequestMapping("/system/user")
public class AdminUserController {

    @Resource
    private AdminUserService userService;


    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<UserRespVO>> getUserPage(UserPageReqVO pageReqVO) {
        // 获得用户分页列表
        return success(userService.getUserPage(pageReqVO));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<UserRespVO>> getUserList(UserCreateReqVO createReqVO,@RequestHeader(required = false) Long SystemDeptId) {
        return success(userService.getUserList(createReqVO,SystemDeptId));
    }

    /**
     * 不分页
     */
    @GetMapping("/list/dept")
    public CommonResult<List<UserRespVO>> getUserListDept(UserCreateReqVO createReqVO,@RequestHeader(required = false) String SystemDeptId) {
        return success(userService.getUserListDept(createReqVO,SystemDeptId));
    }

    /**
     * 新增
     */
    @PostMapping("/create")
    //@Log(title = "用户新增", businessType = BusinessType.INSERT)
    @OperateLog("新增用户")
    public CommonResult<Boolean> createUser(@RequestBody UserCreateReqVO createVO) {
        userService.createUser(createVO);
        return success(true);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperateLog("修改用户")
    //@Log(title = "用户修改", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> updateUser(@RequestBody UserUpdateReqVO updateReqVO) {
        userService.updateUser(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    @OperateLog("删除用户")
    //@Log(title = "用户删除", businessType = BusinessType.DELETE)
    public CommonResult<Boolean> deleteUser(String ids) {
        userService.deleteUser(ids);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<UserRespVO> getUserInfo(Long id) {
        return success(userService.getUserInfo(id));
    }

    /**
     * 修改密码
     */
    @PostMapping("/update-password")
    @OperateLog("修改pc端用户密码")
    public CommonResult<Boolean> updatePassword(@RequestBody UserUpdateReqVO updateReqVO) {
        userService.updatePassword(updateReqVO);
        return success(true);
    }


    /**
     * 修改用户状态
     */
    @GetMapping("/update-status")
    @OperateLog("修改用户状态")
    public CommonResult<Boolean> updateStatus(UserUpdateReqVO updateReqVO) {
        userService.updateStatus(updateReqVO);
        return success(true);
    }

    /**
     * 获取个人中心基本资料
     */
    @GetMapping("/profile/get")
    public CommonResult<UserRespVO> profileGet() {
        return success(userService.profileGet());
    }

    /**
     * 修改基本资料
     */
    @PostMapping("/profile/update")
    @OperateLog("修改基本资料")
    public CommonResult<Boolean> profileUpdate(@RequestBody UserUpdateReqVO updateReqVO) {
        userService.profileUpdate(updateReqVO);
        return success(true);
    }

    /**
     * 上传头像
     */
    @PostMapping("/profile/update-avatar")
    public CommonResult<Map<String, Object>> updateAvater(@RequestParam("file") MultipartFile file, Integer type) {
        return success(userService.updateAvater(file, type));
    }

    /**
     * 通过角色roleCode获取用户信息
     */
    @GetMapping("/getUserByRole")
    public CommonResult<List<UserRespVO>> getUserByRole(String roleCode, @RequestHeader(required = false) Long SystemDeptId) {
        return success(userService.getUserByRole(roleCode, SystemDeptId));
    }

    /**
     * 通过角色roleCode获取用户信息
     */
    @GetMapping("/getUserByRoleAll")
    public CommonResult<List<UserRespVO>> getUserByRoleAll(String roleCode, @RequestHeader(required = false) Long SystemDeptId) {
        return success(userService.getUserByRoleAll(roleCode, SystemDeptId));
    }


    /**
     * 随机生成透析号
     */
    @GetMapping("/getDialysisNo")
    public CommonResult<String> getDialysisNo(String deptNumber, @RequestHeader(required = false) Long Systemdeptid) {
        return success(userService.getDialysisNo(deptNumber, Systemdeptid));
    }

    /**
     * 修改app用户信息
     */
    @PostMapping("/updateUserApp")
    @OperateLog("修改app用户信息")
    public CommonResult<Boolean> updateUserApp(@RequestBody UserUpdateReqVO updateReqVO) {
        userService.updateUserApp(updateReqVO);
        return success(true);
    }

    /**
     * 修改app密码
     */
    @PostMapping("/updateUserPasswordApp")
    @OperateLog("修改app用户密码")
    public CommonResult<Boolean> updateUserPasswordApp(@RequestBody UserUpdateReqVO updateReqVO) {
        userService.updateUserPasswordApp(updateReqVO);
        return success(true);
    }

}
